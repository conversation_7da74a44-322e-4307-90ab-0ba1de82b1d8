# Revue Starlight: Relive - Game Mechanics Analysis

## 🎭 Overview

This document provides a comprehensive analysis of the game mechanics found in Revue Starlight: Relive, based on examination of actual card data and movesets from the karth.top database.

## 📊 Card Data Structure Analysis

### Basic Card Information
- **Card ID**: Unique identifier (e.g., `4010002`)
- **Character ID**: Links to specific character (e.g., `401` = Akira)
- **Rarity**: 1-4 star system
- **Attribute**: 8 different attributes (Flower, Wind, Snow, Moon, Space, Cloud, Star, Sun)
- **Position**: Front, Middle, Rear positioning system
- **Cost**: ACT point cost for deployment

### Stats System
- **HP**: Health points (ranges from ~30k to 150k+)
- **ATK**: Attack power (affects damage output)
- **PDEF/MDEF**: Physical/Magical defense
- **AGI**: Agility (determines turn order)
- **DEX**: Dexterity (affects critical hit chance, base 5% + DEX)

## ⚔️ Combat Mechanics

### ACT System
Each card has 4 types of ACTs:
1. **ACT 1**: Low cost (1 AP), basic attack
2. **ACT 2**: Medium cost (2 AP), stronger attack
3. **ACT 3**: High cost (3 AP), special effects + attack
4. **Climax ACT**: Powerful ultimate ability (2 AP, requires brilliance)

### Attribute Effectiveness
Two separate triangles:
- **Triangle 1**: Flower > Wind > Snow > Flower
- **Triangle 2**: Moon > Space > Cloud > Moon
- **Neutral**: Star and Sun (no weaknesses/strengths)

**Damage Multipliers**:
- Strong against: 1.2x damage
- Weak against: 0.8x damage
- Neutral: 1.0x damage

## 🎯 Status Effects System

### Debilitating Effects
- **Freeze**: Prevents action, duration-based
- **Stop**: Prevents action, duration-based
- **Stun**: Prevents action, duration-based
- **Sleep**: Prevents action, broken by damage

### Damage Over Time
- **Burn**: Additive damage each turn
- **Poison**: Multiplicative (percentage) damage each turn

### Control Effects
- **Charm**: Forces unit to attack allies
- **Dazzle**: Severely reduces accuracy (30% of normal)

### Positive Effects
- **Perfect Aim**: 100% accuracy for duration
- **AP Up**: Increases ACT point generation
- **Resistance Effects**: Reduces chance of specific debuffs

### Effect Levels
- **Normal**: Base effectiveness
- **Greater**: 1.5x effectiveness, 50% resistance piercing

## 🏫 School System

### Five Schools
1. **Seisho**: Performance-focused, spotlight generation
2. **Rinmeikan**: Traditional, spirit-based buffs
3. **Frontier**: Speed and combo chains
4. **Siegfeld**: Precision and control
5. **Seiran**: Balance and adaptability

### School Synergies
- Cards from same school provide synergy bonuses
- Mixed school teams offer tactical diversity
- Unit skills often provide school-wide effects

## 📍 Position System

### Three Positions
- **Front**: High HP tanks, draw attacks, protect team
- **Middle**: Balanced support, connects front and rear
- **Rear**: High damage dealers, vulnerable to rear-targeting

### Targeting Patterns
- Most ACTs target front enemies first
- Some ACTs specifically target rear positions
- AoE ACTs can hit multiple positions
- Position affects turn order calculations

## 🎪 Advanced Mechanics

### Brilliance System
- Builds up during battle
- Required for Climax ACTs
- Generated through taking/dealing damage
- Strategic resource management

### Turn Order
- Based on AGI stat
- Memoirs provide tiebreaker advantage
- Position may affect initiative

### Critical Hits
- Base 5% chance + DEX stat
- Can be modified by effects
- Affects damage output significantly

## 🔍 Research Gaps & YouTube Analysis

### Missing Mechanics
Several mechanics need further research through gameplay videos:

#### Status Effect Interactions
- Do effects stack or override?
- Exact resistance calculation formulas
- Hidden duration extensions
- Priority systems for conflicting effects

#### Damage Calculations
- Exact critical hit formulas
- Defense penetration mechanics
- Attribute effectiveness edge cases
- Memoir/accessory bonus calculations

#### Turn Order Details
- Tied AGI resolution
- Position-based modifiers
- Speed buff interactions
- Initiative change mechanics

### Recommended Research Approach

#### Video Types to Analyze
1. **High-level PvP matches** with detailed UI
2. **Status effect showcases** and testing
3. **Damage calculation demonstrations**
4. **Team composition guides** with explanations
5. **Frame-by-frame analysis** of combat mechanics

#### Analysis Methodology
1. Record specific scenarios with known inputs
2. Measure exact damage numbers and durations
3. Test edge cases and interactions
4. Compare with card data to verify formulas
5. Document discrepancies for further research

## 🛠️ Implementation Status

### Completed Systems
- ✅ Status effects framework with levels and resistance
- ✅ Moveset analysis from card data
- ✅ Attribute effectiveness system
- ✅ Position-based targeting
- ✅ School synergy detection
- ✅ Card data parsing and analysis

### In Development
- 🔄 Damage calculation engine
- 🔄 Turn order system with AGI
- 🔄 Brilliance generation mechanics
- 🔄 Critical hit system
- 🔄 Memoir/accessory integration

### Future Research Needed
- ❓ Exact status effect stacking rules
- ❓ Hidden damage modifiers
- ❓ Stage effect interactions
- ❓ Advanced synergy mechanics
- ❓ PvP-specific rule variations

## 📈 Next Steps

1. **Implement core battle engine** with discovered mechanics
2. **Research missing formulas** through video analysis
3. **Test edge cases** and document behaviors
4. **Refine damage calculations** based on observations
5. **Add memoir/accessory systems** for complete accuracy

## 🎯 Key Insights

- **Complex Status System**: Multiple effect types with levels and resistance
- **Strategic Positioning**: Front/middle/rear affects both offense and defense
- **Resource Management**: ACT points and brilliance create tactical decisions
- **School Synergies**: Team composition significantly affects performance
- **Attribute Mastery**: Understanding effectiveness wheel is crucial for success

This analysis provides a solid foundation for implementing authentic Revue Starlight: Relive mechanics while identifying areas where additional research through gameplay videos would be valuable.
