===CASCADE_FROM_DB_LOCKS===
// Complex Analysis: Database locks causing cascading failures

DIAGNOSIS::CASCADE_FROM_DB_LOCKS
PATTERN::PERSEPHONE_CHAIN
CAUSE::[DB_LOCK->WRITE_TIMEOUT->RETRY_STORM->SERVICE_DEGRADATION]
AFFECTED_SERVICES::[USER_SERVICE, PAYMENT_SERVICE, NOTIFICATION_SERVICE]
FORCES::[ERIS::LOCK_CONTENTION, CHRONOS::TIMEOUT_ESCALATION, HADES::STORAGE_CONTENTION]
REMEDIATION::[LOCK_ANALYSIS, QUERY_OPTIMIZATION, BACKPRESSURE_CONTROL]

===END===