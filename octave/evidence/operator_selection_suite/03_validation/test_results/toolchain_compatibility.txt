=== Testing grep patterns ===

[Square Brackets Test]
SERVICE[&]DATABASE[\!]CACHE[>]PERFORMANCE
✓ [&] grep works

[Angle Brackets Test]
SERVICE<&>DATABASE<\!>CACHE<>>PERFORMANCE
✓ <&> grep works

[Sed Replacement Test]
SERVICE[AND]DATABASE
SERVICE<AND>DATABASE

=== JSON Context Tests ===
(eval):5: no such file or directory: /opt/homebrew/Cellar/python@3.12/3.12.10/bin/python3.12
(eval):8: no such file or directory: /opt/homebrew/Cellar/python@3.12/3.12.10/bin/python3.12

=== YAML Context Tests ===
(eval):12: no such file or directory: /opt/homebrew/Cellar/python@3.12/3.12.10/bin/python3.12
