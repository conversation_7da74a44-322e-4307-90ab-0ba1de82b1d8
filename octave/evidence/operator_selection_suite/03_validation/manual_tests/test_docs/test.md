# Markdown Operator Test

## Plain Text
Family A: DATABASE & CACHE VERSUS LATENCY > PERFORMANCE
Family B: DATABASE + CACHE VERSUS LATENCY -> PERFORMANCE

## As a Blockquote
> DATABASE & CACHE VERSUS LATENCY > PERFORMANCE

> DAT<PERSON><PERSON><PERSON> + CACHE VERSUS LATENCY -> PERFORMANCE

## In Code Blocks
```
Family A: DATABASE & CACHE VERSUS LATENCY > PERFORMANCE
Family B: DATABASE + CACHE VERSUS LATENCY -> PERFORMANCE
```

## Inline Code
Family A: `DATABASE & CACHE VERSUS LATENCY > PERFORMANCE`
Family B: `DATABASE + CACHE VERSUS LATENCY -> PERFORMANCE`

## Lists with Operators
- Family A uses: & for synthesis, > for progression, VERSUS for tension
- Family B uses: + for synthesis, -> for progression, VERSUS for tension

## Tables
| Family | Synthesis | Tension | Progression | Example |
|--------|-----------|---------|-------------|---------|
| A | & | VERSUS | > | DATABASE & CACHE VERSUS LATENCY > PERFORMANCE |
| B | + | VERSUS | -> | DATABASE + CACHE VERSUS LATENCY -> PERFORMANCE |