# frozen_string_literal: true

require_relative 'database/database_manager'

module CardBattle
  # Database-backed version of StageGirlDatabase
  class DatabaseStageGirlDatabase
    def initialize
      @stage_girls_db = Database::DatabaseManager.stage_girls_db
      @memoirs_db = Database::DatabaseManager.memoirs_db
      @accessories_db = Database::DatabaseManager.accessories_db
      
      # Initialize databases if not already done
      Database::DatabaseManager.initialize_databases unless @stage_girls_db
    end

    # Get all stage girls
    def all_stage_girls
      @stage_girls_db.get_all_stage_girls.map { |sg| format_stage_girl(sg) }
    end

    # Get stage girl by name
    def get_stage_girl(name)
      stage_girl = @stage_girls_db.get_stage_girl_by_name(name)
      stage_girl ? format_stage_girl(stage_girl) : nil
    end

    # Get stage girls by school
    def stage_girls_by_school(school)
      @stage_girls_db.get_stage_girls_by_school(school).map { |sg| format_stage_girl(sg) }
    end

    # Get all costumes for a stage girl
    def get_costumes(stage_girl_name)
      stage_girl = @stage_girls_db.get_stage_girl_by_name(stage_girl_name)
      return [] unless stage_girl

      @stage_girls_db.get_costumes_for_stage_girl(stage_girl[:id]).map { |costume| format_costume(costume) }
    end

    # Get specific costume
    def get_costume(stage_girl_name, costume_id)
      costumes = get_costumes(stage_girl_name)
      costumes.find { |c| c[:id] == costume_id || c[:karth_id] == costume_id.to_s }
    end

    # Get costume by karth ID
    def get_costume_by_karth_id(karth_id)
      costume = @stage_girls_db.get_costume_by_karth_id(karth_id.to_s)
      costume ? format_costume(costume) : nil
    end

    # Search functionality
    def search_stage_girls(query)
      all_stage_girls.select do |sg|
        sg[:name].downcase.include?(query.downcase) ||
        sg[:school].downcase.include?(query.downcase)
      end
    end

    def search_costumes(query)
      @stage_girls_db.search_costumes(query).map { |costume| format_costume(costume) }
    end

    # School-specific methods
    def seisho_stage_girls
      stage_girls_by_school("Seisho Music Academy")
    end

    def rinmeikan_stage_girls
      stage_girls_by_school("Rinmeikan Girls School")
    end

    def frontier_stage_girls
      stage_girls_by_school("Frontier School of Arts")
    end

    def siegfeld_stage_girls
      stage_girls_by_school("Siegfeld Institute of Music")
    end

    def seiran_stage_girls
      stage_girls_by_school("Seiran General Art Institute")
    end

    # Get costumes by attribute
    def costumes_by_attribute(attribute)
      @stage_girls_db.get_costumes_by_attribute(attribute).map { |costume| format_costume(costume) }
    end

    # Get costumes by rarity
    def costumes_by_rarity(rarity)
      all_costumes = @stage_girls_db.get_all_costumes
      filtered_costumes = all_costumes.select { |c| c[:rarity] == rarity }
      filtered_costumes.map { |costume| format_costume(costume) }
    end

    # Get all costumes across all stage girls
    def all_costumes
      @stage_girls_db.get_all_costumes.map { |costume| format_costume(costume) }
    end

    # Statistics
    def statistics
      stats = @stage_girls_db.get_costume_statistics
      {
        total_stage_girls: all_stage_girls.size,
        total_costumes: stats['total_costumes'],
        costumes_by_rarity: {
          four_star: stats['four_star_costumes'],
          three_star: stats['three_star_costumes'],
          two_star: stats['two_star_costumes'],
          one_star: stats['one_star_costumes']
        },
        stage_girls_by_school: {
          seisho: seisho_stage_girls.size,
          rinmeikan: rinmeikan_stage_girls.size,
          frontier: frontier_stage_girls.size,
          siegfeld: siegfeld_stage_girls.size,
          seiran: seiran_stage_girls.size
        }
      }
    end

    # Equipment methods
    def all_memoirs
      @memoirs_db.get_all_memoirs.map { |memoir| format_memoir(memoir) }
    end

    def get_memoir(karth_id)
      memoir = @memoirs_db.get_memoir_by_karth_id(karth_id.to_s)
      memoir ? format_memoir(memoir) : nil
    end

    def memoirs_by_rarity(rarity)
      @memoirs_db.get_memoirs_by_rarity(rarity).map { |memoir| format_memoir(memoir) }
    end

    def all_accessories
      @accessories_db.get_all_accessories.map { |accessory| format_accessory(accessory) }
    end

    def get_accessory(karth_id)
      accessory = @accessories_db.get_accessory_by_karth_id(karth_id.to_s)
      accessory ? format_accessory(accessory) : nil
    end

    def accessories_by_category(category)
      @accessories_db.get_accessories_by_category(category).map { |accessory| format_accessory(accessory) }
    end

    def accessories_by_rarity(rarity)
      @accessories_db.get_accessories_by_rarity(rarity).map { |accessory| format_accessory(accessory) }
    end

    # Close database connections
    def close
      Database::DatabaseManager.close_all_databases
    end

    private

    def format_stage_girl(stage_girl_data)
      {
        id: stage_girl_data[:id],
        name: stage_girl_data[:name],
        school: stage_girl_data[:school],
        base_stats: stage_girl_data[:base_stats],
        created_at: stage_girl_data[:created_at],
        updated_at: stage_girl_data[:updated_at]
      }
    end

    def format_costume(costume_data)
      {
        id: costume_data[:id],
        karth_id: costume_data[:karth_id],
        stage_girl_id: costume_data[:stage_girl_id],
        stage_girl_name: costume_data[:stage_girl_name],
        school: costume_data[:school],
        name: costume_data[:name],
        attribute: costume_data[:attribute],
        rarity: costume_data[:rarity],
        stats: costume_data[:stats],
        skills: costume_data[:skills],
        act_data: costume_data[:act_data],
        created_at: costume_data[:created_at],
        updated_at: costume_data[:updated_at]
      }
    end

    def format_memoir(memoir_data)
      {
        id: memoir_data[:id],
        karth_id: memoir_data[:karth_id],
        name: memoir_data[:name],
        description: memoir_data[:description],
        rarity: memoir_data[:rarity],
        max_level: memoir_data[:max_level],
        stats: memoir_data[:stats],
        effects: memoir_data[:effects],
        artwork_info: memoir_data[:artwork_info],
        created_at: memoir_data[:created_at],
        updated_at: memoir_data[:updated_at]
      }
    end

    def format_accessory(accessory_data)
      {
        id: accessory_data[:id],
        karth_id: accessory_data[:karth_id],
        name: accessory_data[:name],
        description: accessory_data[:description],
        rarity: accessory_data[:rarity],
        max_level: accessory_data[:max_level],
        category: accessory_data[:category],
        stats: accessory_data[:stats],
        effects: accessory_data[:effects],
        artwork_info: accessory_data[:artwork_info],
        created_at: accessory_data[:created_at],
        updated_at: accessory_data[:updated_at]
      }
    end
  end
end
