# frozen_string_literal: true

module Card<PERSON>attle
  # Comprehensive Status Effects System for Revue Starlight: Relive
  # Based on analysis of actual card data and movesets
  class StatusEffectsSystem
    
    # Status effect types found in card data
    STATUS_EFFECTS = {
      # Debilitating Effects
      freeze: { 
        name: "Freeze", 
        type: :debuff, 
        prevents_action: true,
        tick_damage: false,
        description: "Unit cannot act for duration"
      },
      stop: { 
        name: "Stop", 
        type: :debuff, 
        prevents_action: true,
        tick_damage: false,
        description: "Unit cannot act for duration"
      },
      stun: { 
        name: "Stun", 
        type: :debuff, 
        prevents_action: true,
        tick_damage: false,
        description: "Unit cannot act for duration"
      },
      sleep: { 
        name: "Sleep", 
        type: :debuff, 
        prevents_action: true,
        tick_damage: false,
        description: "Unit cannot act, broken by damage"
      },
      
      # Damage Over Time Effects
      burn: { 
        name: "Burn", 
        type: :debuff, 
        prevents_action: false,
        tick_damage: true,
        damage_type: :additive,
        description: "Takes damage each turn"
      },
      poison: { 
        name: "<PERSON>ison", 
        type: :debuff, 
        prevents_action: false,
        tick_damage: true,
        damage_type: :multiplicative,
        description: "Takes percentage damage each turn"
      },
      
      # Control Effects
      charm: { 
        name: "Charm", 
        type: :debuff, 
        prevents_action: false,
        tick_damage: false,
        description: "Unit attacks allies instead of enemies"
      },
      dazzle: { 
        name: "Dazzle", 
        type: :debuff, 
        prevents_action: false,
        tick_damage: false,
        description: "Unit's accuracy is severely reduced"
      },
      
      # Positive Effects
      perfect_aim: { 
        name: "Perfect Aim", 
        type: :buff, 
        prevents_action: false,
        tick_damage: false,
        description: "All attacks hit with 100% accuracy"
      },
      ap_up: { 
        name: "AP Up", 
        type: :buff, 
        prevents_action: false,
        tick_damage: false,
        description: "Increases ACT point generation"
      },
      
      # Resistance Effects
      freeze_resistance: { 
        name: "Freeze Resistance", 
        type: :buff, 
        prevents_action: false,
        tick_damage: false,
        description: "Reduces chance of being frozen"
      },
      stop_resistance: { 
        name: "Stop Resistance", 
        type: :buff, 
        prevents_action: false,
        tick_damage: false,
        description: "Reduces chance of being stopped"
      }
    }.freeze

    # Status effect levels (normal vs greater)
    EFFECT_LEVELS = {
      normal: { multiplier: 1.0, resistance_pierce: 0 },
      greater: { multiplier: 1.5, resistance_pierce: 50 }
    }.freeze

    def self.apply_status_effect(target, effect_type, level: :normal, duration: 1, value: 0, source: nil)
      effect_data = STATUS_EFFECTS[effect_type]
      return false unless effect_data

      level_data = EFFECT_LEVELS[level]
      
      # Check resistance
      resistance_value = target.get_resistance(effect_type)
      if resistance_value > level_data[:resistance_pierce]
        puts "#{target.name} resists #{effect_data[:name]}!"
        return false
      end

      # Create and apply the status effect
      status_effect = StatusEffect.new(
        type: effect_type,
        level: level,
        duration: duration,
        value: value * level_data[:multiplier],
        source: source,
        data: effect_data
      )

      target.add_status_effect(status_effect)
      puts "#{target.name} is affected by #{effect_data[:name]} (#{level}) for #{duration} turns!"
      true
    end

    def self.process_turn_effects(unit)
      effects_to_remove = []
      
      unit.status_effects.each do |effect|
        # Process tick damage
        if effect.data[:tick_damage]
          damage = calculate_tick_damage(unit, effect)
          unit.take_damage(damage)
          puts "#{unit.name} takes #{damage} #{effect.data[:name]} damage!"
        end
        
        # Reduce duration
        effect.tick_duration
        effects_to_remove << effect if effect.expired?
      end
      
      # Remove expired effects
      effects_to_remove.each { |effect| unit.remove_status_effect(effect) }
    end

    def self.calculate_tick_damage(unit, effect)
      base_damage = effect.value
      
      case effect.data[:damage_type]
      when :additive
        # Fixed damage amount
        base_damage
      when :multiplicative
        # Percentage of max HP
        (unit.max_hp * (base_damage / 100.0)).round
      else
        base_damage
      end
    end

    def self.can_act?(unit)
      unit.status_effects.none? { |effect| effect.data[:prevents_action] }
    end

    def self.modify_accuracy(unit, base_accuracy)
      accuracy = base_accuracy
      
      unit.status_effects.each do |effect|
        case effect.type
        when :perfect_aim
          return 100  # Perfect accuracy
        when :dazzle
          accuracy *= 0.3  # Severe accuracy reduction
        end
      end
      
      accuracy.round
    end

    def self.get_target_override(unit, original_target)
      charm_effect = unit.status_effects.find { |e| e.type == :charm }
      return original_target unless charm_effect
      
      # Charmed units target allies instead of enemies
      if original_target.team_number == unit.team_number
        # Find enemy team
        # This would need battle context to implement properly
        original_target
      else
        # Find ally team
        original_target
      end
    end
  end

  # Enhanced StatusEffect class with level support
  class StatusEffect
    attr_reader :type, :level, :value, :duration, :source, :data
    
    def initialize(type:, level: :normal, value:, duration:, source: nil, data: {})
      @type = type
      @level = level
      @value = value
      @duration = duration
      @source = source
      @data = data
    end
    
    def tick_duration
      @duration -= 1 if @duration > 0
    end
    
    def expired?
      @duration <= 0
    end
    
    def to_s
      level_str = @level == :greater ? " (Greater)" : ""
      "#{@data[:name]}#{level_str}: #{@value} (#{@duration} turns)"
    end
  end

  # Enhanced BattleUnit methods for status effects
  module StatusEffectMethods
    def add_status_effect(effect)
      @status_effects ||= []

      # Check for existing effect of same type
      existing = @status_effects.find { |e| e.type == effect.type }

      if existing
        # Replace if new effect is stronger or longer
        if effect.level == :greater || effect.duration > existing.duration
          @status_effects.delete(existing)
          @status_effects << effect
        end
      else
        @status_effects << effect
      end
    end

    def remove_status_effect(effect)
      @status_effects.delete(effect)
    end

    def get_resistance(effect_type)
      return 0 unless @status_effects

      resistance_type = "#{effect_type}_resistance".to_sym
      resistance_effects = @status_effects.select { |e| e.type == resistance_type }
      resistance_effects.sum(&:value)
    end

    def has_status_effect?(effect_type)
      return false unless @status_effects
      @status_effects.any? { |e| e.type == effect_type }
    end

    def can_act?
      StatusEffectsSystem.can_act?(self)
    end

    def process_turn_effects
      StatusEffectsSystem.process_turn_effects(self)
    end
  end
end
