# frozen_string_literal: true

module Card<PERSON>attle
  # Comprehensive Moveset Analysis System
  # Analyzes card data to extract movesets, effects, and mechanics
  class MovesetAnalyzer
    
    # Icon mappings for different effect types (based on card data analysis)
    EFFECT_ICONS = {
      1 => :damage,           # Basic damage
      35 => :perfect_aim,     # Perfect Aim buff
      61 => :freeze,          # Freeze debuff
      83 => :ap_up,           # AP increase
      337 => :freeze_flash,   # Freeze attack
      345 => :stop_flash,     # Stop attack
      348 => :resistance_up,  # Resistance increase
      10006 => :dispel        # Effect removal
    }.freeze

    # Attribute mappings
    ATTRIBUTES = {
      1 => :flower,
      2 => :wind, 
      3 => :snow,
      4 => :moon,
      5 => :space,
      6 => :cloud,
      7 => :star,
      8 => :sun
    }.freeze

    # Position mappings
    POSITIONS = {
      "front" => :front,
      "middle" => :middle, 
      "back" => :rear
    }.freeze

    def self.analyze_card(card_data)
      analysis = {
        basic_info: extract_basic_info(card_data),
        stats: extract_stats(card_data),
        acts: extract_acts(card_data),
        skills: extract_skills(card_data),
        movesets: extract_movesets(card_data),
        synergies: analyze_synergies(card_data)
      }
      
      analysis
    end

    def self.extract_basic_info(card_data)
      {
        card_id: card_data.dig(:basicInfo, :cardID),
        name: card_data.dig(:basicInfo, :name, :en),
        rarity: card_data.dig(:basicInfo, :rarity),
        character: card_data.dig(:basicInfo, :character),
        attribute: ATTRIBUTES[card_data.dig(:base, :attribute)],
        attack_type: card_data.dig(:base, :attackType),
        role: card_data.dig(:base, :roleIndex, :role),
        position: POSITIONS[card_data.dig(:base, :roleIndex, :role)],
        cost: card_data.dig(:base, :cost)
      }
    end

    def self.extract_stats(card_data)
      stats_data = card_data[:statRemake] || card_data[:stat]
      return {} unless stats_data

      {
        hp: stats_data[:hp],
        atk: stats_data[:atk],
        pdef: stats_data[:pdef],
        mdef: stats_data[:mdef],
        agi: stats_data[:agi],
        dex: card_data.dig(:basicInfo, :dex)
      }
    end

    def self.extract_acts(card_data)
      acts = {}
      
      [:act1, :act2, :act3].each do |act_key|
        act_data = card_data.dig(:act, act_key, :skillNormal)
        next unless act_data

        acts[act_key] = {
          id: act_data[:id],
          name: act_data.dig(:name, :en),
          attribute: ATTRIBUTES[act_data[:attribute]],
          cost: act_data[:cost],
          multiple: act_data[:multiple],
          icon: act_data[:icon],
          effects: extract_act_effects(act_data[:params])
        }
      end

      # Climax ACT
      climax_data = card_data.dig(:climaxACT, :skillNormal)
      if climax_data
        acts[:climax] = {
          id: climax_data[:id],
          name: climax_data.dig(:name, :en),
          attribute: ATTRIBUTES[climax_data[:attribute]],
          cost: climax_data[:cost],
          multiple: climax_data[:multiple],
          icon: climax_data[:icon],
          effects: extract_act_effects(climax_data[:params])
        }
      end

      acts
    end

    def self.extract_act_effects(params)
      return [] unless params&.is_a?(Array)

      effects = []
      
      params.each do |param|
        effect = {
          icon: param[:icon],
          type: EFFECT_ICONS[param[:icon]] || :unknown,
          hits: param[:hits],
          duration: parse_duration(param[:duration]),
          accuracy: param[:accuracy],
          target: param.dig(:target, :en),
          description: param.dig(:description, :en),
          power_values: extract_power_values(param.dig(:description, :en))
        }
        
        effects << effect
      end
      
      effects
    end

    def self.extract_skills(card_data)
      skills = {}
      
      # Auto Skills
      (1..3).each do |i|
        skill_key = "autoSkill#{i}".to_sym
        skill_data = card_data.dig(:skills, skill_key)
        next unless skill_data

        skills[skill_key] = {
          id: skill_data[:id],
          icon: skill_data[:icon],
          type: skill_data.dig(:type, :en),
          name: skill_data.dig(:name, :en),
          description: skill_data.dig(:description, :en),
          effects: extract_skill_effects(skill_data[:params])
        }
      end

      # Unit Skill
      unit_skill = card_data.dig(:skills, :unitSkill)
      if unit_skill
        skills[:unit_skill] = {
          id: unit_skill[:id],
          icon: unit_skill[:icon],
          type: unit_skill.dig(:type, :en),
          name: unit_skill.dig(:name, :en),
          description: unit_skill.dig(:description, :en),
          effects: extract_skill_effects(unit_skill[:params])
        }
      end

      skills
    end

    def self.extract_skill_effects(params)
      return [] unless params&.is_a?(Array)

      effects = []
      
      params.each do |param|
        effect = {
          icon: param[:icon],
          type: EFFECT_ICONS[param[:icon]] || :unknown,
          target: param.dig(:target, :en),
          description: param.dig(:description, :en),
          values: extract_power_values(param.dig(:description, :en))
        }
        
        effects << effect
      end
      
      effects
    end

    def self.extract_movesets(card_data)
      movesets = {
        offensive: [],
        defensive: [],
        support: [],
        control: []
      }

      # Analyze ACTs
      acts = extract_acts(card_data)
      acts.each do |act_key, act_data|
        act_data[:effects].each do |effect|
          case effect[:type]
          when :damage
            movesets[:offensive] << {
              source: act_key,
              name: act_data[:name],
              type: :damage,
              power: effect[:power_values],
              target: effect[:target],
              cost: act_data[:cost]
            }
          when :freeze, :stop_flash, :freeze_flash
            movesets[:control] << {
              source: act_key,
              name: act_data[:name],
              type: effect[:type],
              duration: effect[:duration],
              target: effect[:target],
              cost: act_data[:cost]
            }
          when :perfect_aim, :ap_up
            movesets[:support] << {
              source: act_key,
              name: act_data[:name],
              type: effect[:type],
              duration: effect[:duration],
              target: effect[:target],
              cost: act_data[:cost]
            }
          when :resistance_up
            movesets[:defensive] << {
              source: act_key,
              name: act_data[:name],
              type: effect[:type],
              target: effect[:target],
              cost: act_data[:cost]
            }
          end
        end
      end

      movesets
    end

    def self.analyze_synergies(card_data)
      synergies = {
        school_synergy: analyze_school_synergy(card_data),
        attribute_synergy: analyze_attribute_synergy(card_data),
        position_synergy: analyze_position_synergy(card_data),
        stage_effects: extract_stage_effects(card_data)
      }
      
      synergies
    end

    def self.analyze_school_synergy(card_data)
      # Analyze unit skills and auto skills for school-based effects
      character_id = card_data.dig(:basicInfo, :character)
      school = determine_school_from_character(character_id)
      
      {
        school: school,
        provides_school_buff: has_school_buff_effects?(card_data),
        benefits_from_school: has_school_requirements?(card_data)
      }
    end

    def self.analyze_attribute_synergy(card_data)
      attribute = ATTRIBUTES[card_data.dig(:base, :attribute)]
      
      {
        primary_attribute: attribute,
        attribute_effectiveness: get_attribute_effectiveness(attribute),
        provides_attribute_buff: has_attribute_buff_effects?(card_data)
      }
    end

    def self.analyze_position_synergy(card_data)
      position = POSITIONS[card_data.dig(:base, :roleIndex, :role)]
      
      {
        position: position,
        targets_by_position: analyze_position_targeting(card_data),
        position_requirements: has_position_requirements?(card_data)
      }
    end

    def self.extract_stage_effects(card_data)
      # Look for stage-wide effects in skills
      stage_effects = []
      
      skills = extract_skills(card_data)
      skills.each do |skill_key, skill_data|
        if skill_data[:description]&.include?("all allies") || 
           skill_data[:description]&.include?("entire team")
          stage_effects << {
            source: skill_key,
            name: skill_data[:name],
            description: skill_data[:description],
            type: :stage_wide
          }
        end
      end
      
      stage_effects
    end

    private

    def self.parse_duration(duration_data)
      return nil unless duration_data&.is_a?(Hash)
      
      duration_text = duration_data[:en] || duration_data[:ja]
      return nil unless duration_text
      
      # Extract number from duration text like "[3, 3, 3, 3, 3] Turn(s)"
      match = duration_text.match(/\[(\d+)/)
      match ? match[1].to_i : nil
    end

    def self.extract_power_values(description)
      return [] unless description
      
      # Extract power values like "[88, 92, 96, 101, 105]"
      match = description.match(/\[([0-9, ]+)\]/)
      return [] unless match
      
      match[1].split(', ').map(&:to_i)
    end

    def self.determine_school_from_character(character_id)
      case character_id.to_s[0]
      when '1' then :seisho
      when '2' then :rinmeikan  
      when '3' then :frontier
      when '4' then :siegfeld
      when '5' then :seiran
      else :unknown
      end
    end

    def self.get_attribute_effectiveness(attribute)
      # Attribute effectiveness wheel
      case attribute
      when :flower then { strong_against: :wind, weak_against: :snow }
      when :wind then { strong_against: :snow, weak_against: :flower }
      when :snow then { strong_against: :flower, weak_against: :wind }
      when :moon then { strong_against: :space, weak_against: :cloud }
      when :space then { strong_against: :cloud, weak_against: :moon }
      when :cloud then { strong_against: :moon, weak_against: :space }
      when :star, :sun then { strong_against: [], weak_against: [] }
      else { strong_against: [], weak_against: [] }
      end
    end

    def self.has_school_buff_effects?(card_data)
      # Check if card provides buffs to school members
      skills = extract_skills(card_data)
      skills.any? do |_, skill|
        skill[:description]&.downcase&.include?("school") ||
        skill[:description]&.downcase&.include?("academy")
      end
    end

    def self.has_school_requirements?(card_data)
      # Check if card has effects that scale with school members
      skills = extract_skills(card_data)
      skills.any? do |_, skill|
        skill[:description]&.include?("same school") ||
        skill[:description]&.include?("academy members")
      end
    end

    def self.has_attribute_buff_effects?(card_data)
      # Check if card provides attribute-based buffs
      skills = extract_skills(card_data)
      skills.any? do |_, skill|
        ATTRIBUTES.values.any? { |attr| skill[:description]&.include?(attr.to_s) }
      end
    end

    def self.analyze_position_targeting(card_data)
      acts = extract_acts(card_data)
      targeting = { front: 0, middle: 0, rear: 0, all: 0 }
      
      acts.each do |_, act|
        act[:effects].each do |effect|
          target = effect[:target]&.downcase
          next unless target
          
          if target.include?("1st") || target.include?("front")
            targeting[:front] += 1
          elsif target.include?("middle")
            targeting[:middle] += 1
          elsif target.include?("rear") || target.include?("back")
            targeting[:rear] += 1
          elsif target.include?("all")
            targeting[:all] += 1
          end
        end
      end
      
      targeting
    end

    def self.has_position_requirements?(card_data)
      skills = extract_skills(card_data)
      skills.any? do |_, skill|
        skill[:description]&.include?("position") ||
        skill[:description]&.include?("front") ||
        skill[:description]&.include?("rear")
      end
    end
  end
end
