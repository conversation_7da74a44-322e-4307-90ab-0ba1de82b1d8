# frozen_string_literal: true

module CardBattle
  # Authentic Pick-Ban System for Revue Starlight: Relive PvP
  # Implements the exact rules specified by the user
  class AuthenticPickBanSystem
    attr_reader :player1, :player2, :phase, :match_state

    ROSTER_SIZE = 10      # Each player selects 10 stage girls
    BAN_COUNT = 2         # Each player bans 2 opposing stage girls
    TEAM_SIZE = 5         # Final team size for battle
    REMAINING_AFTER_BANS = 8  # 10 - 2 = 8 remaining after bans

    # Load all available character-id combinations from card files
    def self.load_available_cards
      card_files = Dir.glob("lib/card_battle/cards/*.rb")
      cards = card_files.map do |file|
        File.basename(file, '.rb')
      end.sort

      # Group by character for easier browsing
      grouped = {}
      cards.each do |card_id|
        char_name = card_id.split('-').first.capitalize
        grouped[char_name] ||= []
        grouped[char_name] << card_id
      end

      { all_cards: cards, grouped_by_character: grouped }
    end

    AVAILABLE_CARDS = load_available_cards

    def initialize(player1_name = "Player 1", player2_name = "Player 2")
      @player1 = PickBanPlayer.new(player1_name)
      @player2 = PickBanPlayer.new(player2_name)
      @phase = :roster_selection
      @match_state = {
        step: 1,
        current_banner: nil,
        bans_completed: 0
      }
    end

    def start_pick_ban_match
      puts "🎭" * 25
      puts "AUTHENTIC PICK-BAN SYSTEM"
      puts "🎭" * 25
      puts "#{@player1.name} vs #{@player2.name}"
      puts "=" * 50
      
      step1_roster_selection
      step2_ban_phase  
      step3_team_composition
      
      [@player1.final_team, @player2.final_team]
    end

    private

    def step1_roster_selection
      puts "\n📋 STEP 1: ROSTER SELECTION"
      puts "Each player selects #{ROSTER_SIZE} stage girls"
      puts "-" * 40
      
      # Player 1 roster selection
      puts "\n#{@player1.name}'s Roster Selection:"
      @player1.roster = select_roster_for_player(@player1.name)
      
      # Player 2 roster selection  
      puts "\n#{@player2.name}'s Roster Selection:"
      @player2.roster = select_roster_for_player(@player2.name)
      
      display_rosters
      @phase = :ban_phase
    end

    def step2_ban_phase
      puts "\n🚫 STEP 2: BAN PHASE"
      puts "Each player sees opponent's roster and bans #{BAN_COUNT} stage girls"
      puts "-" * 50
      
      # Show both rosters before banning
      puts "\n📋 ROSTER OVERVIEW:"
      puts "#{@player1.name}'s Roster: #{@player1.roster.join(', ')}"
      puts "#{@player2.name}'s Roster: #{@player2.roster.join(', ')}"
      
      # Player 1 bans from Player 2's roster
      puts "\n#{@player1.name} bans #{BAN_COUNT} from #{@player2.name}'s roster:"
      @player1.bans = select_bans(@player1.name, @player2.roster)
      
      # Player 2 bans from Player 1's roster
      puts "\n#{@player2.name} bans #{BAN_COUNT} from #{@player1.name}'s roster:"
      @player2.bans = select_bans(@player2.name, @player1.roster)
      
      # Apply bans
      @player1.available_for_team = @player1.roster - @player2.bans
      @player2.available_for_team = @player2.roster - @player1.bans
      
      display_bans_and_remaining
      @phase = :team_composition
    end

    def step3_team_composition
      puts "\n✅ STEP 3: TEAM COMPOSITION"
      puts "Each player forms a team of #{TEAM_SIZE} from their remaining #{REMAINING_AFTER_BANS} stage girls"
      puts "-" * 60
      
      # Player 1 team selection
      puts "\n#{@player1.name}'s Team Selection:"
      puts "Available: #{@player1.available_for_team.join(', ')}"
      @player1.final_team = select_team_from_available(@player1.name, @player1.available_for_team)
      
      # Player 2 team selection
      puts "\n#{@player2.name}'s Team Selection:"
      puts "Available: #{@player2.available_for_team.join(', ')}"
      @player2.final_team = select_team_from_available(@player2.name, @player2.available_for_team)
      
      display_final_teams
      @phase = :complete
    end

    def select_roster_for_player(player_name)
      selected_roster = []

      puts "🎯 REFINED CARD SELECTION"
      puts "Use specific {name-id} format for precise character selection"
      puts "Example: meifan-4030036, karen-1010015, hikari-1020020"
      puts
      puts "Available options:"
      puts "1. Browse by character (shows all costumes)"
      puts "2. Enter specific card ID directly"
      puts "3. Quick random selection"
      puts

      ROSTER_SIZE.times do |i|
        puts "=" * 50
        puts "Select card #{i + 1}/#{ROSTER_SIZE} for #{player_name}:"
        puts "1. Browse characters  2. Enter card ID  3. Random"
        print "Choice (1-3): "

        mode = gets.chomp
        selected_card = nil

        case mode
        when "1"
          selected_card = browse_characters_for_selection
        when "2"
          selected_card = enter_specific_card_id
        when "3"
          selected_card = AVAILABLE_CARDS[:all_cards].sample
          puts "🎲 Random selection: #{selected_card}"
        else
          puts "❌ Invalid choice, using random selection"
          selected_card = AVAILABLE_CARDS[:all_cards].sample
        end

        selected_roster << selected_card
        puts "✅ Selected: #{selected_card}"
        puts
      end

      selected_roster
    end

    def browse_characters_for_selection
      puts "\n📚 CHARACTER BROWSER"
      characters = AVAILABLE_CARDS[:grouped_by_character].keys.sort

      puts "Available characters:"
      characters.each_with_index do |char, index|
        card_count = AVAILABLE_CARDS[:grouped_by_character][char].size
        puts "  #{index + 1}. #{char} (#{card_count} costumes)"
      end

      print "Select character (1-#{characters.size}): "
      char_choice = gets.chomp.to_i - 1

      if char_choice >= 0 && char_choice < characters.size
        selected_char = characters[char_choice]
        available_cards = AVAILABLE_CARDS[:grouped_by_character][selected_char]

        puts "\n🎭 Available costumes for #{selected_char}:"
        available_cards.each_with_index do |card_id, index|
          puts "  #{index + 1}. #{card_id}"
        end

        print "Select costume (1-#{available_cards.size}): "
        card_choice = gets.chomp.to_i - 1

        if card_choice >= 0 && card_choice < available_cards.size
          return available_cards[card_choice]
        else
          puts "❌ Invalid choice, selecting first costume"
          return available_cards.first
        end
      else
        puts "❌ Invalid character choice, selecting random"
        return AVAILABLE_CARDS[:all_cards].sample
      end
    end

    def enter_specific_card_id
      puts "\n⌨️  DIRECT CARD ID ENTRY"
      puts "Enter the exact card ID (e.g., meifan-4030036):"
      print "Card ID: "

      input = gets.chomp.downcase

      if AVAILABLE_CARDS[:all_cards].include?(input)
        return input
      else
        puts "❌ Card ID not found. Available cards include:"
        # Show a few examples
        examples = AVAILABLE_CARDS[:all_cards].sample(5)
        examples.each { |card| puts "  - #{card}" }
        puts "Using random selection instead..."
        return AVAILABLE_CARDS[:all_cards].sample
      end
    end

    def select_bans(banner_name, opponent_roster)
      bans = []

      puts "\n🚫 BAN PHASE for #{banner_name}"
      puts "Opponent's roster (#{opponent_roster.size} cards):"
      opponent_roster.each_with_index do |card_id, index|
        char_name = card_id.split('-').first.capitalize
        costume_id = card_id.split('-').last
        puts "  #{index + 1}. #{card_id} (#{char_name} - #{costume_id})"
      end
      puts

      BAN_COUNT.times do |i|
        puts "Ban #{i + 1}/#{BAN_COUNT}:"
        puts "Options:"
        puts "1. Select by number (1-#{opponent_roster.size})"
        puts "2. Enter specific card ID"
        print "Choice method (1-2): "

        method = gets.chomp
        banned_card = nil

        case method
        when "1"
          print "Ban choice (1-#{opponent_roster.size}): "
          choice = gets.chomp.to_i - 1

          if choice >= 0 && choice < opponent_roster.size
            banned_card = opponent_roster[choice]
          else
            puts "❌ Invalid choice, selecting random ban"
            banned_card = opponent_roster.sample
          end
        when "2"
          print "Enter card ID to ban: "
          input = gets.chomp.downcase

          if opponent_roster.include?(input)
            banned_card = input
          else
            puts "❌ Card not in opponent's roster, selecting random ban"
            banned_card = opponent_roster.sample
          end
        else
          puts "❌ Invalid method, selecting random ban"
          banned_card = opponent_roster.sample
        end

        bans << banned_card
        puts "🚫 Banned: #{banned_card}"
        puts
      end

      bans
    end

    def select_team_from_available(player_name, available_cards)
      team = []

      puts "\n⚔️ TEAM COMPOSITION for #{player_name}"
      puts "Available cards after bans (#{available_cards.size} remaining):"
      available_cards.each_with_index do |card_id, index|
        char_name = card_id.split('-').first.capitalize
        costume_id = card_id.split('-').last
        puts "  #{index + 1}. #{card_id} (#{char_name} - #{costume_id})"
      end
      puts

      TEAM_SIZE.times do |i|
        puts "Select team member #{i + 1}/#{TEAM_SIZE}:"
        puts "Options:"
        puts "1. Select by number (1-#{available_cards.size})"
        puts "2. Enter specific card ID"
        print "Choice method (1-2): "

        method = gets.chomp
        selected_card = nil

        case method
        when "1"
          print "Team choice (1-#{available_cards.size}): "
          choice = gets.chomp.to_i - 1

          if choice >= 0 && choice < available_cards.size
            selected_card = available_cards[choice]
          else
            puts "❌ Invalid choice, selecting random card"
            selected_card = available_cards.sample
          end
        when "2"
          print "Enter card ID: "
          input = gets.chomp.downcase

          if available_cards.include?(input)
            selected_card = input
          else
            puts "❌ Card not available, selecting random card"
            selected_card = available_cards.sample
          end
        else
          puts "❌ Invalid method, selecting random card"
          selected_card = available_cards.sample
        end

        team << selected_card
        puts "✅ Team member: #{selected_card}"
        puts
      end

      team
    end



    def display_rosters
      puts "\n📋 ROSTERS SELECTED:"
      puts "#{@player1.name}: #{@player1.roster.join(', ')}"
      puts "#{@player2.name}: #{@player2.roster.join(', ')}"
    end

    def display_bans_and_remaining
      puts "\n🚫 BANS APPLIED:"
      puts "#{@player1.name} banned: #{@player1.bans.join(', ')}"
      puts "#{@player2.name} banned: #{@player2.bans.join(', ')}"
      puts
      puts "📋 REMAINING AVAILABLE:"
      puts "#{@player1.name}: #{@player1.available_for_team.join(', ')} (#{@player1.available_for_team.size})"
      puts "#{@player2.name}: #{@player2.available_for_team.join(', ')} (#{@player2.available_for_team.size})"
    end

    def display_final_teams
      puts "\n🏆 FINAL TEAMS:"
      puts "#{@player1.name}: #{@player1.final_team.join(', ')}"
      puts "#{@player2.name}: #{@player2.final_team.join(', ')}"
      puts
      puts "✅ Pick-Ban phase complete! Ready for battle."
    end
  end

  # Player class for pick-ban system
  class PickBanPlayer
    attr_reader :name
    attr_accessor :roster, :bans, :available_for_team, :final_team

    def initialize(name)
      @name = name
      @roster = []              # 10 selected stage girls
      @bans = []                # 2 characters banned by opponent
      @available_for_team = []  # 8 remaining after bans
      @final_team = []          # 5 final team members
    end

    def roster_complete?
      @roster.size == AuthenticPickBanSystem::ROSTER_SIZE
    end

    def bans_complete?
      @bans.size == AuthenticPickBanSystem::BAN_COUNT
    end

    def team_complete?
      @final_team.size == AuthenticPickBanSystem::TEAM_SIZE
    end
  end
end
