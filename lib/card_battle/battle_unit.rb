# frozen_string_literal: true

module Card<PERSON>attle
  # Represents a Stage Girl in battle with all combat stats and abilities
  class BattleUnit
    attr_reader :stage_girl, :team_number, :position, :name, :school, :attribute
    attr_reader :base_stats, :memoir, :accessories, :status_effects
    attr_accessor :current_hp, :brilliance, :climax_ready
    
    def initialize(stage_girl, team_number, position)
      @stage_girl = stage_girl
      @team_number = team_number
      @position = position
      @name = stage_girl.name
      @school = stage_girl.school
      @attribute = stage_girl.get_element
      
      # Load stats from selected costume
      load_base_stats
      load_equipment
      
      # Battle state
      @current_hp = max_hp
      @brilliance = 0
      @climax_ready = false
      @status_effects = []
      
      # Determine role based on costume data
      @role = determine_role
    end
    
    def alive?
      @current_hp > 0
    end
    
    def max_hp
      total_stat(:hp)
    end
    
    def total_attack
      total_stat(:atk)
    end
    
    def total_pdef
      total_stat(:pdef)
    end
    
    def total_mdef
      total_stat(:mdef)
    end
    
    def total_agility
      base_agi = total_stat(:agi)
      memoir_bonus = @memoir ? 1 : 0  # Memoir gives tiebreaker advantage
      base_agi + memoir_bonus
    end

    def total_dexterity
      total_stat(:dex) || 50  # Default DEX if not specified
    end
    
    def has_memoir?
      !@memoir.nil?
    end
    
    def take_damage(amount)
      @current_hp = [@current_hp - amount, 0].max
    end
    
    def increase_brilliance(amount)
      @brilliance = [@brilliance + amount, 100].min
    end
    
    def decrease_brilliance(amount)
      @brilliance = [@brilliance - amount, 0].max
    end
    
    def set_climax_ready(ready)
      @climax_ready = ready
    end
    
    def climax_ready?
      @climax_ready
    end
    
    def apply_effect(effect)
      @status_effects << effect
    end
    
    # ACT abilities from costume data
    def act1
      return nil unless @stage_girl.selected_costume
      
      act_data = get_act_data(1)
      return nil unless act_data
      
      BattleAct.new(
        name: act_data.dig(:skillNormal, :name, :en) || "ACT 1",
        cost: act_data.dig(:skillNormal, :cost) || 1,
        power: extract_power_values(act_data),
        target_type: determine_target_type(act_data),
        damage_type: determine_damage_type(act_data),
        effects: extract_effects(act_data),
        attribute: act_data.dig(:skillNormal, :attribute),
        hit_count: extract_hit_count(act_data)
      )
    end
    
    def act2
      return nil unless @stage_girl.selected_costume
      
      act_data = get_act_data(2)
      return nil unless act_data
      
      BattleAct.new(
        name: act_data.dig(:skillNormal, :name, :en) || "ACT 2",
        cost: act_data.dig(:skillNormal, :cost) || 2,
        power: extract_power_values(act_data),
        target_type: determine_target_type(act_data),
        damage_type: determine_damage_type(act_data),
        effects: extract_effects(act_data),
        attribute: act_data.dig(:skillNormal, :attribute),
        hit_count: extract_hit_count(act_data)
      )
    end
    
    def act3
      return nil unless @stage_girl.selected_costume
      
      act_data = get_act_data(3)
      return nil unless act_data
      
      BattleAct.new(
        name: act_data.dig(:skillNormal, :name, :en) || "ACT 3",
        cost: act_data.dig(:skillNormal, :cost) || 3,
        power: extract_power_values(act_data),
        target_type: determine_target_type(act_data),
        damage_type: determine_damage_type(act_data),
        effects: extract_effects(act_data),
        attribute: act_data.dig(:skillNormal, :attribute),
        hit_count: extract_hit_count(act_data)
      )
    end
    
    def climax_act
      return nil unless @stage_girl.selected_costume
      
      # Load climax ACT from costume data
      costume_data = load_costume_data
      return nil unless costume_data
      
      climax_data = costume_data.dig(:groupSkills, :climaxACT)
      return nil unless climax_data
      
      BattleAct.new(
        name: climax_data.dig(:skillNormal, :name, :en) || "Climax ACT",
        cost: climax_data.dig(:skillNormal, :cost) || 2,
        power: extract_power_values(climax_data),
        target_type: determine_target_type(climax_data),
        damage_type: determine_damage_type(climax_data),
        effects: extract_effects(climax_data),
        attribute: climax_data.dig(:skillNormal, :attribute),
        is_climax: true,
        hit_count: extract_hit_count(climax_data)
      )
    end
    
    private
    
    def load_base_stats
      # Try to load real card data first
      card_data = load_real_card_data

      if card_data
        # Use statRemake if available (higher level stats), otherwise use stat
        stats_data = card_data[:statRemake] || card_data[:stat]

        @base_stats = {
          hp: stats_data[:hp] || 30000,
          atk: stats_data[:atk] || 2000,
          pdef: stats_data[:pdef] || 1000,
          mdef: stats_data[:mdef] || 1000,
          agi: stats_data[:agi] || 1500,
          dex: card_data[:basicInfo][:dex] || 50
        }
        puts "✅ Loaded real stats for #{@name}: HP=#{@base_stats[:hp]}, ATK=#{@base_stats[:atk]}, AGI=#{@base_stats[:agi]}, DEX=#{@base_stats[:dex]}"
      else
        # Fallback to default stats
        @base_stats = {
          hp: 30000,
          atk: 2000,
          pdef: 1000,
          mdef: 1000,
          agi: 1500,
          dex: 50
        }
        puts "⚠️  Using fallback stats for #{@name}: HP=#{@base_stats[:hp]}, ATK=#{@base_stats[:atk]}, DEX=#{@base_stats[:dex]}"
      end
    end
    
    def load_equipment
      @memoir = nil # Will load from equipped memoir
      @accessories = [] # Will load from equipped accessories

      # TODO: Load actual equipped memoir and accessories
      # This will need integration with the memoir/accessory assignment system
    end

    def load_real_card_data
      # Character name to ID mapping for card file lookup
      character_ids = {
        "Karen" => "101", "Hikari" => "102", "Mahiru" => "103", "Claudine" => "104", "Maya" => "105",
        "Futaba" => "108", "Junna" => "106", "Nana" => "107", "Kaoruko" => "109",
        "Tamao" => "201", "Ichie" => "202", "Fumi" => "203", "Rui" => "204", "Yuyuko" => "205",
        "Aruru" => "301", "Misora" => "302", "Lalafin" => "303", "Tsukasa" => "304", "Shizuha" => "305",
        "Akira" => "401", "Michiru" => "402", "Yachiyo" => "405", "Meifan" => "403", "Shiori" => "404",
        "Stella" => "406", "Shiro" => "407", "Ryoko" => "408", "Minku" => "409", "Kuina" => "410",
        "Koharu" => "501", "Suzu" => "502", "Hisame" => "503"
      }

      char_id = character_ids[@name]
      return nil unless char_id

      # Find available card files for this character
      card_files = Dir.glob("lib/card_battle/cards/#{@name.downcase}-#{char_id}*.rb")

      return nil if card_files.empty?

      # Use the first available card file
      selected_card = card_files.first

      begin
        # Suppress warnings when loading constants
        original_verbose = $VERBOSE
        $VERBOSE = nil

        load selected_card

        # Extract the constant name from the file
        constant_name = File.basename(selected_card, '.rb').upcase.gsub('-', '_')
        card_data = Object.const_get(constant_name)

        $VERBOSE = original_verbose
        return card_data
      rescue => e
        $VERBOSE = original_verbose
        puts "⚠️  Error loading card data for #{@name}: #{e.message}"
        return nil
      end
    end
    
    def load_costume_data
      return nil unless @stage_girl.selected_costume

      # Load real card data from the 573 card files
      character_name = @name.downcase
      costume_name = @stage_girl.selected_costume.name

      # Try to find all card files for this character
      card_files = Dir.glob("lib/card_battle/cards/#{character_name}-*.rb")

      if card_files.any?
        # Select a card file (for now, use first available)
        # TODO: Implement costume-specific card selection logic
        selected_card = card_files.first

        puts "Loading real card data for #{@name} from #{File.basename(selected_card)}"

        begin
          # Load the card file
          load selected_card

          # Extract the constant name from the file
          constant_name = File.basename(selected_card, '.rb').upcase.gsub('-', '_')
          card_data = Object.const_get(constant_name)

          puts "Successfully loaded #{constant_name} with real ACT data"
          return card_data
        rescue => e
          puts "Error loading card data from #{selected_card}: #{e.message}"
          return get_fallback_costume_data
        end
      else
        puts "No card files found for #{character_name}, using fallback"
        return get_fallback_costume_data
      end
    end

    def get_fallback_costume_data
      # Fallback ACT structure with authentic power values
      {
        act: {
          act1: {
            skillNormal: {
              name: { en: "Basic Attack" },
              cost: 1,
              params: [
                {
                  type: "normal",
                  target: { en: "1st Front Enemy" },
                  description: { en: "Normal Dmg [88, 92, 96, 101, 105]" }
                }
              ]
            }
          },
          act2: {
            skillNormal: {
              name: { en: "Power Attack" },
              cost: 2,
              params: [
                {
                  type: "normal",
                  target: { en: "1st Front Enemy" },
                  description: { en: "Normal Dmg [120, 125, 130, 136, 141]" }
                }
              ]
            }
          },
          act3: {
            skillNormal: {
              name: { en: "Special Attack" },
              cost: 3,
              params: [
                {
                  type: "normal",
                  target: { en: "All Enemies" },
                  description: { en: "Normal Dmg [100, 105, 110, 115, 120]" }
                }
              ]
            }
          }
        },
        groupSkills: {
          climaxACT: {
            skillNormal: {
              name: { en: "Climax Attack" },
              cost: 2,
              params: [
                {
                  type: "normal",
                  target: { en: "All Enemies" },
                  description: { en: "Normal Dmg [180, 188, 196, 205, 213]" }
                }
              ]
            }
          }
        }
      }
    end
    
    def map_character_code_to_name(code)
      # Map character codes to names based on the file structure
      character_map = {
        101 => "karen", 102 => "mahiru", 103 => "nana", 104 => "claudine", 105 => "maya",
        106 => "junna", 107 => "futaba", 108 => "kaoruko", 109 => "hikari",
        201 => "ichie", 202 => "fumi", 203 => "tamao", 204 => "rui", 205 => "yuyuko",
        301 => "yachiyo", 302 => "meifan", 303 => "shiori", 304 => "lalafin", 305 => "akira",
        401 => "liu_mei_fan", 402 => "ellen", 403 => "koharu", 404 => "regalia", 405 => "stella",
        406 => "shizuha", 407 => "michiru", 408 => "misora", 409 => "wilhelmina", 410 => "snow_white",
        501 => "aruru", 502 => "tsukasa", 503 => "shiro"
      }

      character_map[code]
    end
    
    def get_act_data(act_number)
      costume_data = load_costume_data
      return nil unless costume_data
      
      costume_data.dig(:act, "act#{act_number}".to_sym)
    end
    
    def total_stat(stat_name)
      base = (@base_stats && @base_stats[stat_name]) || 0
      memoir_bonus = @memoir ? (@memoir[stat_name] || 0) : 0
      accessory_bonus = @accessories.sum { |acc| acc[stat_name] || 0 }

      base + memoir_bonus + accessory_bonus
    end
    
    def determine_role
      # Determine role based on costume role index or stats
      costume_data = load_costume_data
      return :front unless costume_data
      
      role_data = costume_data.dig(:base, :roleIndex, :role)
      case role_data
      when "front"
        :front
      when "middle"
        :middle
      when "back"
        :back
      else
        # Fallback: determine by stats (high HP = front, high ATK = back)
        hp_ratio = total_stat(:hp) / 50000.0
        atk_ratio = total_stat(:atk) / 3000.0
        
        if hp_ratio > atk_ratio
          :front
        elsif atk_ratio > hp_ratio * 1.5
          :back
        else
          :middle
        end
      end
    end
    
    def extract_power_values(act_data)
      # Extract power values from description like "[88, 92, 96, 101, 105]"
      params = act_data.dig(:skillNormal, :params)
      return 100 unless params&.any?
      
      damage_param = params.find { |p| p[:type] == "normal" && p[:description] }
      return 100 unless damage_param
      
      description = damage_param.dig(:description, :en) || ""
      power_match = description.match(/\[(\d+(?:,\s*\d+)*)\]/)
      
      if power_match
        powers = power_match[1].split(',').map(&:strip).map(&:to_i)
        powers[2] || powers.first || 100  # Use middle value or first
      else
        100
      end
    end

    def extract_hit_count(act_data)
      # Extract hit count from the first param
      params = act_data.dig(:skillNormal, :params) || []
      return 1 unless params.any?

      damage_param = params.find { |p| p[:type] == "normal" }
      return 1 unless damage_param

      damage_param[:hits] || 1
    end

    def determine_target_type(act_data)
      params = act_data.dig(:skillNormal, :params)
      return :single_target unless params&.any?
      
      target_param = params.find { |p| p[:target] }
      return :single_target unless target_param
      
      target_text = target_param.dig(:target, :en) || ""
      
      case target_text.downcase
      when /all enemies/
        :all_enemies
      when /1st front enemy/
        :single_front
      else
        :single_target
      end
    end
    
    def determine_damage_type(act_data)
      # Determine if this is normal (physical) or special (magical) damage
      # This might need refinement based on more data analysis
      :normal
    end
    
    def extract_effects(act_data)
      # Extract status effects like defense down, damage up, etc.
      # This will need more detailed implementation
      []
    end
  end
end
