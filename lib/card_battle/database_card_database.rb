# frozen_string_literal: true

require_relative 'database/database_manager'

module Card<PERSON>attle
  # Database-backed version of CardDatabase that replaces file-based storage
  class DatabaseCardDatabase
    def initialize
      @stage_girls_db = Database::DatabaseManager.stage_girls_db
      @memoirs_db = Database::DatabaseManager.memoirs_db
      @accessories_db = Database::DatabaseManager.accessories_db
      
      # Initialize databases if not already done
      Database::DatabaseManager.initialize_databases unless @stage_girls_db
    end

    # Get all costumes (replaces all_cards)
    def all_cards
      @stage_girls_db.get_all_costumes.map { |costume| format_costume_as_card(costume) }
    end

    # Get cards by school
    def cards_by_school(school)
      @stage_girls_db.get_costumes_by_school(school).map { |costume| format_costume_as_card(costume) }
    end

    # Get cards by rarity
    def cards_by_rarity(rarity)
      costumes = @stage_girls_db.get_all_costumes.select { |c| c[:rarity] == rarity }
      costumes.map { |costume| format_costume_as_card(costume) }
    end

    # Get cards by attribute
    def cards_by_attribute(attribute)
      @stage_girls_db.get_costumes_by_attribute(attribute).map { |costume| format_costume_as_card(costume) }
    end

    # Search cards by name
    def search_cards(query)
      @stage_girls_db.search_costumes(query).map { |costume| format_costume_as_card(costume) }
    end

    # Get specific card by ID
    def get_card(karth_id)
      costume = @stage_girls_db.get_costume_by_karth_id(karth_id.to_s)
      costume ? format_costume_as_card(costume) : nil
    end

    # Get all stage girls
    def all_stage_girls
      @stage_girls_db.get_all_stage_girls
    end

    # Get stage girl by name
    def get_stage_girl(name)
      @stage_girls_db.get_stage_girl_by_name(name)
    end

    # Get costumes for a specific stage girl
    def get_costumes_for_stage_girl(stage_girl_name)
      stage_girl = @stage_girls_db.get_stage_girl_by_name(stage_girl_name)
      return [] unless stage_girl

      @stage_girls_db.get_costumes_for_stage_girl(stage_girl[:id]).map { |costume| format_costume_as_card(costume) }
    end

    # Get all memoirs
    def all_memoirs
      @memoirs_db.get_all_memoirs
    end

    # Get memoir by ID
    def get_memoir(karth_id)
      @memoirs_db.get_memoir_by_karth_id(karth_id.to_s)
    end

    # Get memoirs by rarity
    def memoirs_by_rarity(rarity)
      @memoirs_db.get_memoirs_by_rarity(rarity)
    end

    # Search memoirs
    def search_memoirs(query)
      @memoirs_db.search_memoirs(query)
    end

    # Get all accessories
    def all_accessories
      @accessories_db.get_all_accessories
    end

    # Get accessory by ID
    def get_accessory(karth_id)
      @accessories_db.get_accessory_by_karth_id(karth_id.to_s)
    end

    # Get accessories by category
    def accessories_by_category(category)
      @accessories_db.get_accessories_by_category(category)
    end

    # Get accessories by rarity
    def accessories_by_rarity(rarity)
      @accessories_db.get_accessories_by_rarity(rarity)
    end

    # Search accessories
    def search_accessories(query)
      @accessories_db.search_accessories(query)
    end

    # Get stackable accessories
    def stackable_accessories
      @accessories_db.get_stackable_effects.map { |effect| effect[:accessory_id] }.uniq.map do |acc_id|
        @accessories_db.get_accessory(acc_id)
      end.compact
    end

    # Statistics methods
    def costume_statistics
      @stage_girls_db.get_costume_statistics
    end

    def memoir_statistics
      @memoirs_db.get_memoir_statistics
    end

    def accessory_statistics
      @accessories_db.get_accessory_statistics
    end

    # School-specific methods (for compatibility)
    def seisho_cards
      cards_by_school("Seisho Music Academy")
    end

    def rinmeikan_cards
      cards_by_school("Rinmeikan Girls School")
    end

    def frontier_cards
      cards_by_school("Frontier School of Arts")
    end

    def siegfeld_cards
      cards_by_school("Siegfeld Institute of Music")
    end

    def seiran_cards
      cards_by_school("Seiran General Art Institute")
    end

    # Rarity-specific methods (for compatibility)
    def four_star_cards
      cards_by_rarity(4)
    end

    def three_star_cards
      cards_by_rarity(3)
    end

    def two_star_cards
      cards_by_rarity(2)
    end

    def one_star_cards
      cards_by_rarity(1)
    end

    # Attribute-specific methods (for compatibility)
    def flower_cards
      cards_by_attribute("flower")
    end

    def wind_cards
      cards_by_attribute("wind")
    end

    def snow_cards
      cards_by_attribute("snow")
    end

    def moon_cards
      cards_by_attribute("moon")
    end

    def space_cards
      cards_by_attribute("space")
    end

    def cloud_cards
      cards_by_attribute("cloud")
    end

    def star_cards
      cards_by_attribute("star")
    end

    def sun_cards
      cards_by_attribute("sun")
    end

    # Close database connections
    def close
      Database::DatabaseManager.close_all_databases
    end

    private

    # Convert database costume format to legacy card format for compatibility
    def format_costume_as_card(costume)
      stats = costume[:stats] || {}
      skills = costume[:skills] || {}
      act_data = costume[:act_data] || {}
      name_data = costume[:name] || {}

      {
        id: costume[:karth_id],
        name: name_data[:en] || name_data[:ja] || "Unknown",
        character: costume[:stage_girl_name],
        school: costume[:school],
        attribute: costume[:attribute],
        rarity: costume[:rarity],
        hp: stats[:hp] || stats["hp"],
        atk: stats[:atk] || stats["atk"],
        def: stats[:pdef] || stats["pdef"],
        agi: stats[:agi] || stats["agi"],
        dex: stats[:dex] || stats["dex"],
        cost: stats[:cost] || stats["cost"],
        total: stats[:total] || stats["total"],
        auto_skills: skills[:auto_skills] || skills["auto_skills"] || [],
        act_skills: skills[:act_skills] || skills["act_skills"] || [],
        climax_act: skills[:climax_act] || skills["climax_act"],
        finish_act: skills[:finish_act] || skills["finish_act"],
        attack_type: act_data[:attack_type] || act_data["attack_type"],
        role: act_data[:role] || act_data["role"],
        created_at: costume[:created_at],
        updated_at: costume[:updated_at]
      }
    end
  end
end
