# frozen_string_literal: true

module CardBattle
  # Authentic Revue Starlight: Relive PvP Battle System
  class AuthenticPvPBattle
    attr_reader :team1, :team2, :current_turn, :turn_player, :battle_log
    
    TEAM_SIZE = 5
    ACT_POINTS_PER_TURN = 6
    MOVES_AVAILABLE_PER_TURN = 5..6
    MAX_BRILLIANCE = 100
    
    # Attribute effectiveness system
    ATTRIBUTE_EFFECTIVENESS = {
      # Group 1: Flower > Wind > Snow > Flower
      1 => { 2 => 1.2, 3 => 0.8 }, # <PERSON> beats <PERSON>, weak to Snow
      2 => { 3 => 1.2, 1 => 0.8 }, # <PERSON> beats <PERSON>, weak to <PERSON>  
      3 => { 1 => 1.2, 2 => 0.8 }, # <PERSON> beats <PERSON>, weak to Wind
      
      # Group 2: Moon > Space > Cloud > Moon
      4 => { 5 => 1.2, 6 => 0.8 }, # <PERSON> beats Space, weak to Cloud
      5 => { 6 => 1.2, 4 => 0.8 }, # <PERSON> beats Cloud, weak to Moon
      6 => { 4 => 1.2, 5 => 0.8 }, # <PERSON> beats <PERSON>, weak to Space
      
      # Star and Sun cancel each other
      7 => { 8 => 1.0 }, # <PERSON> vs Sun
      8 => { 7 => 1.0 }, # <PERSON> vs Star
      
      # Dream is neutral to everything
      9 => {}
    }.freeze
    
    def initialize(team1_stage_girls, team2_stage_girls)
      validate_teams!(team1_stage_girls, team2_stage_girls)
      
      @team1 = create_battle_units(team1_stage_girls, 1)
      @team2 = create_battle_units(team2_stage_girls, 2)
      @current_turn = 1
      @turn_player = determine_first_player
      @battle_log = []
      @battle_state = :active
      
      log_event("🎭 AUTHENTIC PVP BATTLE STARTED")
      log_event("Team 1: #{@team1.map(&:name).join(', ')}")
      log_event("Team 2: #{@team2.map(&:name).join(', ')}")
      log_event("Turn order determined by AGI (with memoir tiebreakers)")
    end
    
    def battle_loop
      while @battle_state == :active
        execute_turn
        check_victory_conditions
        advance_turn
      end
      
      declare_winner
    end
    
    def execute_turn
      current_team = @turn_player == 1 ? @team1 : @team2
      opponent_team = @turn_player == 1 ? @team2 : @team1
      
      log_event("\n🎯 TURN #{@current_turn} - Player #{@turn_player}")
      log_event("ACT Points Available: #{ACT_POINTS_PER_TURN}")
      
      # Generate available moves for this turn
      available_moves = generate_available_moves(current_team)
      log_event("Available moves: #{available_moves.length}")
      
      # Execute moves until ACT points exhausted
      act_points_remaining = ACT_POINTS_PER_TURN
      
      while act_points_remaining > 0 && available_moves.any?
        move = select_move(available_moves, act_points_remaining)
        break unless move
        
        cost = execute_move(move, current_team, opponent_team)
        act_points_remaining -= cost
        available_moves.delete(move)
        
        # Check for brilliance changes and climax availability
        update_brilliance_bars(current_team, opponent_team)
      end
      
      log_event("Turn completed. ACT points remaining: #{act_points_remaining}")
    end
    
    private
    
    def validate_teams!(team1, team2)
      raise ArgumentError, "Team 1 must have #{TEAM_SIZE} members" unless team1.size == TEAM_SIZE
      raise ArgumentError, "Team 2 must have #{TEAM_SIZE} members" unless team2.size == TEAM_SIZE
    end
    
    def create_battle_units(stage_girls, team_number)
      stage_girls.map.with_index do |stage_girl, index|
        BattleUnit.new(stage_girl, team_number, index)
      end.sort_by { |unit| -unit.total_agility } # Sort by AGI descending
    end
    
    def determine_first_player
      # Highest AGI unit determines first player
      team1_fastest = @team1.max_by(&:total_agility)
      team2_fastest = @team2.max_by(&:total_agility)
      
      if team1_fastest.total_agility > team2_fastest.total_agility
        log_event("Player 1 goes first (AGI: #{team1_fastest.total_agility})")
        1
      elsif team2_fastest.total_agility > team1_fastest.total_agility
        log_event("Player 2 goes first (AGI: #{team2_fastest.total_agility})")
        2
      else
        # Tie - memoir tiebreaker (stage girl without memoir loses)
        team1_has_memoir = team1_fastest.has_memoir?
        team2_has_memoir = team2_fastest.has_memoir?
        
        if team1_has_memoir && !team2_has_memoir
          log_event("Player 1 goes first (memoir tiebreaker)")
          1
        elsif team2_has_memoir && !team1_has_memoir
          log_event("Player 2 goes first (memoir tiebreaker)")
          2
        else
          # Random if both have or both don't have memoirs
          [1, 2].sample
        end
      end
    end
    
    def generate_available_moves(team)
      # Generate 5-6 random moves from alive team members
      alive_units = team.select(&:alive?)
      puts "Alive units: #{alive_units.map(&:name).join(', ')}"
      return [] if alive_units.empty?

      move_count = rand(MOVES_AVAILABLE_PER_TURN)
      moves = []

      move_count.times do
        unit = alive_units.sample
        available_acts = get_available_acts(unit)
        puts "#{unit.name} available acts: #{available_acts.length}"
        next if available_acts.empty?

        act = available_acts.sample
        moves << BattleMove.new(unit, act)
      end

      puts "Generated #{moves.length} moves"
      moves
    end
    
    def get_available_acts(unit)
      acts = []

      # Check if Climax ACT is available (brilliance bar full)
      if unit.brilliance >= MAX_BRILLIANCE
        climax = unit.climax_act
        puts "  Climax ACT available: #{climax ? climax.name : 'nil'}"
        acts << climax if climax
      else
        # Regular ACTs available when brilliance not full
        act1 = unit.act1
        act2 = unit.act2
        act3 = unit.act3
        puts "  Regular ACTs - ACT1: #{act1 ? act1.name : 'nil'}, ACT2: #{act2 ? act2.name : 'nil'}, ACT3: #{act3 ? act3.name : 'nil'}"
        acts << act1 if act1
        acts << act2 if act2
        acts << act3 if act3
      end

      acts.compact
    end
    
    def select_move(available_moves, act_points_remaining)
      # Simple AI: prefer moves that fit within remaining ACT points
      affordable_moves = available_moves.select { |move| move.cost <= act_points_remaining }
      return nil if affordable_moves.empty?
      
      # Prioritize Climax ACTs, then higher cost moves
      affordable_moves.sort_by { |move| [-move.priority, -move.cost] }.first
    end
    
    def execute_move(move, current_team, opponent_team)
      log_event("#{move.unit.name} uses #{move.act.name} (Cost: #{move.cost})")
      
      # Calculate damage and effects
      targets = determine_targets(move, opponent_team)
      
      targets.each do |target|
        damage = calculate_damage(move, target)
        apply_damage(target, damage, move)
        apply_effects(move, target)
      end
      
      # Update brilliance for using move
      move.unit.increase_brilliance(5) # Base brilliance gain for acting
      
      move.cost
    end
    
    def calculate_damage(move, target)
      # Authentic Revue Starlight damage formula
      attacker = move.unit
      act = move.act

      # Base components
      attack_stat = attacker.total_attack
      act_power = act.power
      act_full_coefficient = 1.0 # Base coefficient, can be modified by skills
      climax_mode_multiplier = act.climax_act? ? 1.1 : 1.0

      # RNG component (0.92 to 1.08 in increments of 0.01)
      rng = (92 + rand(17)) / 100.0  # 0.92 to 1.08

      # Defense calculation
      defense_stat = act.damage_type == :special ? target.total_mdef : target.total_pdef

      # Core damage calculation
      # Attack = 2 * Act Power * Act Full Coefficient * Climax Act Mode 1.1x multiplier
      let_attack = 2 * act_power * act_full_coefficient * climax_mode_multiplier

      # MAX(attacker's Attack - enemy Defense or special defense, Attack/10) / Hit Count * RNG * all other multipliers
      base_damage = [let_attack * attack_stat / 100.0 - defense_stat, (let_attack * attack_stat / 100.0) / 10.0].max

      # Apply hit count (most ACTs are single hit)
      hit_count = act.hit_count || 1
      damage_per_hit = base_damage / hit_count

      # Apply RNG
      damage_with_rng = damage_per_hit * rng

      # Apply attribute effectiveness
      effectiveness = get_attribute_effectiveness(attacker.attribute, target.attribute)

      # Apply critical hit if applicable
      critical_multiplier = calculate_critical_hit(attacker, target)

      # Apply elemental damage buffs and other multipliers
      damage_buffs = calculate_damage_buffs(attacker, target, act)

      # Final damage calculation
      final_damage = damage_with_rng * effectiveness * critical_multiplier * damage_buffs

      # Ensure minimum damage (at least 10% of base attack)
      min_damage = (let_attack * attack_stat / 100.0) * 0.1

      [final_damage.round, min_damage.round].max
    end
    
    def get_attribute_effectiveness(attacker_attr, defender_attr)
      return 1.0 if attacker_attr.nil? || defender_attr.nil?

      effectiveness = ATTRIBUTE_EFFECTIVENESS.dig(attacker_attr, defender_attr) || 1.0

      # Log effectiveness for debugging
      if effectiveness != 1.0
        effect_type = effectiveness > 1.0 ? "SUPER EFFECTIVE" : "NOT VERY EFFECTIVE"
        log_event("    #{effect_type} (#{effectiveness}x)")
      end

      effectiveness
    end

    def calculate_critical_hit(attacker, target)
      # Critical hit calculation: DEX is additive with base 5% rate
      dex_stat = attacker.total_dexterity
      base_critical_rate = 5.0 # 5% base critical rate

      # DEX is simply additive with the base 5
      critical_rate = (base_critical_rate + dex_stat) / 100.0
      critical_rate = [critical_rate, 1.0].min # Cap at 100%

      if rand < critical_rate
        log_event("    CRITICAL HIT!")
        1.5 # 50% damage bonus for critical hits (base critical of 50)
      else
        1.0
      end
    end

    def calculate_damage_buffs(attacker, target, act)
      # Calculate all damage multipliers
      multiplier = 1.0

      # Elemental damage buffs from acts, autos, etc.
      # This would include effects like "Damage Up" from memoirs/accessories

      # Example: Check for damage up effects on attacker
      attacker.status_effects.each do |effect|
        case effect.type
        when :damage_up
          multiplier *= (1.0 + effect.value / 100.0)
          log_event("    Damage Up: +#{effect.value}%")
        when :attack_up
          multiplier *= (1.0 + effect.value / 100.0)
          log_event("    Attack Up: +#{effect.value}%")
        end
      end

      # Check for damage down effects on target
      target.status_effects.each do |effect|
        case effect.type
        when :damage_received_down
          multiplier *= (1.0 - effect.value / 100.0)
          log_event("    Damage Received Down: -#{effect.value}%")
        end
      end

      # Advantage damage (1.25x/1.5x/2x for hitting the right advantage)
      if has_advantage_bonus(attacker, target, act)
        advantage_multiplier = 1.25 # Base advantage bonus
        multiplier *= advantage_multiplier
        log_event("    Advantage Bonus: #{advantage_multiplier}x")
      end

      multiplier
    end

    def has_advantage_bonus(attacker, target, act)
      # Check if this attack has advantage conditions met
      # This would be based on specific act effects or unit skills
      false # Placeholder - needs specific implementation
    end
    
    def determine_targets(move, opponent_team)
      case move.act.target_type
      when :single_front
        # Target lowest AGI front unit
        front_units = opponent_team.select { |u| u.role == :front && u.alive? }
        front_units.empty? ? [opponent_team.select(&:alive?).min_by(&:total_agility)] : [front_units.min_by(&:total_agility)]
      when :all_enemies
        opponent_team.select(&:alive?)
      when :single_target
        [opponent_team.select(&:alive?).sample].compact
      else
        [opponent_team.select(&:alive?).sample].compact
      end
    end
    
    def apply_damage(target, damage, move)
      return if damage <= 0
      
      target.take_damage(damage)
      log_event("  #{target.name} takes #{damage} damage (#{target.current_hp}/#{target.max_hp} HP)")
      
      # Increase target's brilliance when hit
      target.increase_brilliance(3)
    end
    
    def apply_effects(move, target)
      # Apply status effects from the move
      move.act.effects.each do |effect|
        target.apply_effect(effect)
      end
    end
    
    def update_brilliance_bars(current_team, opponent_team)
      # Check for Climax ACT availability changes
      (current_team + opponent_team).each do |unit|
        if unit.brilliance >= MAX_BRILLIANCE && !unit.climax_ready?
          unit.set_climax_ready(true)
          log_event("✨ #{unit.name}'s Climax ACT is now available!")
        end
      end
    end
    
    def check_victory_conditions
      # Game ends when one team drains the health bar of all members of the opposing team
      team1_alive = @team1.any?(&:alive?)
      team2_alive = @team2.any?(&:alive?)

      if !team1_alive && !team2_alive
        @battle_state = :draw
        log_event("🤝 BATTLE ENDED IN A DRAW! All units defeated simultaneously!")
      elsif !team1_alive
        @battle_state = :team2_victory
        log_event("🎉 TEAM 2 WINS! All Team 1 members defeated!")
      elsif !team2_alive
        @battle_state = :team1_victory
        log_event("🎉 TEAM 1 WINS! All Team 2 members defeated!")
      end

      # Note: A full squad of 5 girls is not necessary - sometimes 2 or 3
      # synergistic units can defeat a less coordinated opposing team
    end
    
    def advance_turn
      return unless @battle_state == :active
      
      @turn_player = @turn_player == 1 ? 2 : 1
      @current_turn += 1 if @turn_player == 1
    end
    
    def declare_winner
      case @battle_state
      when :team1_victory
        log_event("🎉 TEAM 1 VICTORY!")
      when :team2_victory
        log_event("🎉 TEAM 2 VICTORY!")
      when :draw
        log_event("🤝 BATTLE DRAW!")
      end
    end
    
    def log_event(message)
      @battle_log << message
      puts message
    end
  end
end
