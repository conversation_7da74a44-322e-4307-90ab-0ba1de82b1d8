# frozen_string_literal: true

require_relative 'database_card_database'
require_relative 'database_stage_girl_database'

module CardBattle
  # Authentic Revue Starlight: ReLive battle system based on gameplay analysis
  class AuthenticBattleSystem
    attr_reader :team1, :team2, :current_turn, :battle_log, :winner

    def initialize(team1_cards, team2_cards)
      @team1 = create_battle_team(team1_cards, 1)
      @team2 = create_battle_team(team2_cards, 2)
      @current_turn = 1
      @battle_log = []
      @winner = nil
      @act_cards_available = []
      
      # Initialize starting brilliance (50 for PvP)
      (@team1 + @team2).each { |girl| girl[:brilliance] = 50 }
      
      log_event("Battle initialized: Team 1 vs Team 2")
      log_event("Each stage girl starts with 50 brilliance")
    end

    def battle
      log_event("=== BATTLE START ===")
      
      while !battle_over?
        execute_turn
        @current_turn += 1
        
        # Safety check to prevent infinite battles
        break if @current_turn > 50
      end
      
      determine_winner
      log_event("=== BATTLE END ===")
      log_event("Winner: #{@winner}")
      
      {
        winner: @winner,
        turns: @current_turn - 1,
        battle_log: @battle_log,
        final_state: {
          team1: @team1.map { |g| format_girl_state(g) },
          team2: @team2.map { |g| format_girl_state(g) }
        }
      }
    end

    private

    def create_battle_team(cards, team_id)
      cards.map.with_index do |card, index|
        {
          id: "#{team_id}_#{index}",
          team_id: team_id,
          name: card[:name] || card[:character],
          character: card[:character],
          school: card[:school],
          attribute: card[:attribute],
          rarity: card[:rarity],
          
          # Core stats
          max_hp: card[:hp] || 1000,
          current_hp: card[:hp] || 1000,
          atk: card[:atk] || 100,
          pdef: card[:def] || 100,
          mdef: card[:def] || 100, # Simplified
          agi: card[:agi] || 100,
          dex: card[:dex] || 5,
          
          # Battle state
          brilliance: 0, # Will be set to 50 in initialize
          position: determine_position(card[:agi] || 100, index),
          status_effects: {},
          barriers: {},
          
          # Equipment effects (simplified)
          memoir: nil,
          accessory: nil,
          
          # Skills (simplified for now)
          auto_skills: card[:auto_skills] || [],
          act_skills: generate_act_skills(card),
          climax_act: card[:climax_act],
          finish_act: card[:finish_act],
          
          # Battle tracking
          alive: true,
          participated_in_cr: false,
          cr_participation_turns: []
        }
      end
    end

    def determine_position(agi, index)
      # Position based on AGI: front = slowest, back = fastest
      # This is a simplified version - real game has more complex positioning
      case agi
      when 0..80
        :front
      when 81..120
        :middle  
      else
        :back
      end
    end

    def generate_act_skills(card)
      # Generate ACT skills based on card data
      # In real game, these are predefined per costume
      [
        { cost: 1, type: :normal_attack, power: 100, target: :single },
        { cost: 2, type: :special_attack, power: 150, target: :single, brilliance_gain: 20 },
        { cost: 3, type: :aoe_attack, power: 120, target: :all_enemies }
      ]
    end

    def execute_turn
      log_event("\n--- TURN #{@current_turn} ---")
      
      # 1. Generate ACT cards for each team (6 AP total per team)
      team1_acts = generate_act_cards(@team1)
      team2_acts = generate_act_cards(@team2)
      
      # 2. Apply start-of-turn effects
      apply_start_of_turn_effects
      
      # 3. Execute actions based on speed and AP cost
      execute_actions(team1_acts, team2_acts)
      
      # 4. Apply end-of-turn effects (DoT, status effects)
      apply_end_of_turn_effects
      
      # 5. Check for Climax Revue triggers
      check_climax_revue_triggers
    end

    def generate_act_cards(team)
      # Each team gets 6 AP worth of cards per turn
      # Cards are randomly selected from available skills
      alive_girls = team.select { |g| g[:alive] }
      return [] if alive_girls.empty?
      
      cards = []
      remaining_ap = 6
      
      while remaining_ap > 0 && cards.size < 5
        girl = alive_girls.sample
        available_skills = girl[:act_skills].select { |s| s[:cost] <= remaining_ap }
        
        if available_skills.any?
          skill = available_skills.sample
          cards << {
            girl_id: girl[:id],
            skill: skill,
            cost: skill[:cost]
          }
          remaining_ap -= skill[:cost]
        else
          break
        end
      end
      
      # Add climax acts if brilliance >= 100
      alive_girls.each do |girl|
        if girl[:brilliance] >= 100 && cards.size < 5
          cards << {
            girl_id: girl[:id],
            skill: { cost: 0, type: :climax_act, power: 300, target: :special },
            cost: 0,
            is_climax: true
          }
        end
      end
      
      cards
    end

    def execute_actions(team1_acts, team2_acts)
      # Combine and sort actions by AP cost (lower cost goes first)
      # Then by character speed for same AP cost
      all_actions = []
      
      team1_acts.each { |act| all_actions << { team: 1, action: act } }
      team2_acts.each { |act| all_actions << { team: 2, action: act } }
      
      # Sort by AP cost, then by character AGI
      all_actions.sort! do |a, b|
        cost_comparison = a[:action][:cost] <=> b[:action][:cost]
        if cost_comparison == 0
          girl_a = find_girl_by_id(a[:action][:girl_id])
          girl_b = find_girl_by_id(b[:action][:girl_id])
          girl_b[:agi] <=> girl_a[:agi] # Higher AGI goes first
        else
          cost_comparison
        end
      end
      
      # Execute actions in order
      all_actions.each do |action_data|
        execute_single_action(action_data[:action])
      end
    end

    def execute_single_action(action)
      girl = find_girl_by_id(action[:girl_id])
      return unless girl && girl[:alive]
      
      skill = action[:skill]
      targets = select_targets(girl, skill)
      
      log_event("#{girl[:name]} uses #{skill[:type]} (#{skill[:cost]} AP)")
      
      case skill[:type]
      when :normal_attack, :special_attack, :aoe_attack
        execute_attack(girl, skill, targets)
      when :climax_act
        execute_climax_act(girl, targets)
      when :support
        execute_support(girl, skill, targets)
      end
      
      # Grant brilliance for using skills
      brilliance_gain = calculate_brilliance_gain(skill)
      gain_brilliance(girl, brilliance_gain)
    end

    def execute_attack(attacker, skill, targets)
      targets.each do |target|
        next unless target[:alive]
        
        # Calculate damage
        base_damage = calculate_damage(attacker, target, skill)
        
        # Apply attribute effectiveness
        damage_multiplier = calculate_attribute_effectiveness(attacker[:attribute], target[:attribute])
        final_damage = (base_damage * damage_multiplier).round
        
        # Apply critical hits
        if critical_hit?(attacker)
          final_damage = (final_damage * 1.5).round
          log_event("  Critical hit!")
        end
        
        # Apply damage
        apply_damage(target, final_damage, attacker)
        
        log_event("  #{target[:name]} takes #{final_damage} damage (#{target[:current_hp]}/#{target[:max_hp]} HP)")
      end
    end

    def execute_climax_act(girl, targets)
      # Climax acts are powerful special attacks
      girl[:brilliance] = 0 # Consume all brilliance
      girl[:participated_in_cr] = true
      
      targets.each do |target|
        next unless target[:alive]
        
        # Climax acts typically do high damage and have special effects
        base_damage = girl[:atk] * 3 # High multiplier for climax acts
        damage_multiplier = calculate_attribute_effectiveness(girl[:attribute], target[:attribute])
        final_damage = (base_damage * damage_multiplier).round
        
        apply_damage(target, final_damage, girl)
        log_event("  CLIMAX ACT! #{target[:name]} takes #{final_damage} damage")
      end
    end

    def calculate_damage(attacker, target, skill)
      # Simplified damage calculation based on game mechanics
      base_atk = attacker[:atk]
      defense = skill[:type] == :special_attack ? target[:mdef] : target[:pdef]
      
      # Basic damage formula (simplified)
      damage = base_atk * (skill[:power] / 100.0) - (defense * 0.3)
      [damage, 1].max.round # Minimum 1 damage
    end

    def calculate_attribute_effectiveness(attacker_attr, target_attr)
      # Attribute effectiveness system
      effectiveness = {
        # Flower > Wind > Snow > Flower
        'flower' => { 'wind' => 1.5, 'snow' => 0.67, 'flower' => 1.0 },
        'wind' => { 'snow' => 1.5, 'flower' => 0.67, 'wind' => 1.0 },
        'snow' => { 'flower' => 1.5, 'wind' => 0.67, 'snow' => 1.0 },
        
        # Moon > Space > Cloud > Moon  
        'moon' => { 'space' => 1.5, 'cloud' => 0.67, 'moon' => 1.0 },
        'space' => { 'cloud' => 1.5, 'moon' => 0.67, 'space' => 1.0 },
        'cloud' => { 'moon' => 1.5, 'space' => 0.67, 'cloud' => 1.0 },
        
        # Star and Sun are neutral
        'star' => {},
        'sun' => {}
      }
      
      attr_map = effectiveness[attacker_attr] || {}
      attr_map[target_attr] || 1.0 # Default to neutral
    end

    def critical_hit?(attacker)
      base_crit_rate = 5.0 # 5% base
      dex_bonus = attacker[:dex] || 0
      total_crit_rate = base_crit_rate + dex_bonus
      
      rand(100) < total_crit_rate
    end

    def apply_damage(target, damage, attacker)
      # Check for barriers, dodge, guts, etc.
      actual_damage = damage
      
      # Apply damage
      target[:current_hp] -= actual_damage
      
      # Grant brilliance for taking damage
      brilliance_from_damage = (actual_damage / 100.0 * 10).round
      gain_brilliance(target, brilliance_from_damage)
      
      # Check if target dies
      if target[:current_hp] <= 0
        target[:current_hp] = 0
        target[:alive] = false
        log_event("  #{target[:name]} is defeated!")
      end
    end

    def gain_brilliance(girl, amount)
      return unless girl[:alive]
      
      girl[:brilliance] = [girl[:brilliance] + amount, 100].min
      log_event("  #{girl[:name]} gains #{amount} brilliance (#{girl[:brilliance]}/100)")
    end

    def calculate_brilliance_gain(skill)
      # Brilliance gain based on AP cost
      case skill[:cost]
      when 1 then 7
      when 2 then 14  
      when 3 then 21
      else 0
      end
    end

    def select_targets(attacker, skill)
      enemy_team = attacker[:team_id] == 1 ? @team2 : @team1
      ally_team = attacker[:team_id] == 1 ? @team1 : @team2
      
      case skill[:target]
      when :single
        [enemy_team.select { |g| g[:alive] }.sample].compact
      when :all_enemies
        enemy_team.select { |g| g[:alive] }
      when :all_allies
        ally_team.select { |g| g[:alive] }
      when :self
        [attacker]
      when :special
        # Climax acts often target all enemies
        enemy_team.select { |g| g[:alive] }
      else
        []
      end
    end

    def apply_start_of_turn_effects
      # Apply status effects, regeneration, etc.
      (@team1 + @team2).each do |girl|
        next unless girl[:alive]
        
        # Apply DoT effects (burn, poison)
        apply_dot_effects(girl)
        
        # Apply other status effects
        process_status_effects(girl)
      end
    end

    def apply_end_of_turn_effects
      # Clean up expired effects, apply end-of-turn damage
      (@team1 + @team2).each do |girl|
        next unless girl[:alive]
        
        # Reduce status effect durations
        girl[:status_effects].each do |effect, data|
          data[:duration] -= 1 if data[:duration] > 0
        end
        
        # Remove expired effects
        girl[:status_effects].reject! { |_, data| data[:duration] <= 0 }
      end
    end

    def apply_dot_effects(girl)
      # Apply damage over time effects
      %i[burn poison].each do |effect|
        if girl[:status_effects][effect]
          damage = girl[:status_effects][effect][:damage] || 0
          girl[:current_hp] -= damage
          log_event("  #{girl[:name]} takes #{damage} #{effect} damage")
          
          if girl[:current_hp] <= 0
            girl[:current_hp] = 0
            girl[:alive] = false
            log_event("  #{girl[:name]} is defeated by #{effect}!")
          end
        end
      end
    end

    def process_status_effects(girl)
      # Process various status effects
      girl[:status_effects].each do |effect, data|
        case effect
        when :freeze
          log_event("  #{girl[:name]} is frozen and cannot act")
        when :stun
          log_event("  #{girl[:name]} is stunned")
        when :sleep
          log_event("  #{girl[:name]} is asleep")
        end
      end
    end

    def check_climax_revue_triggers
      # Check if any characters can trigger Climax Revue
      [@team1, @team2].each do |team|
        ready_for_cr = team.select { |g| g[:alive] && g[:brilliance] >= 100 }
        
        if ready_for_cr.any?
          log_event("Team #{team.first[:team_id]} has characters ready for Climax Revue!")
        end
      end
    end

    def battle_over?
      team1_alive = @team1.any? { |g| g[:alive] }
      team2_alive = @team2.any? { |g| g[:alive] }
      
      !team1_alive || !team2_alive
    end

    def determine_winner
      team1_alive = @team1.any? { |g| g[:alive] }
      team2_alive = @team2.any? { |g| g[:alive] }
      
      if team1_alive && !team2_alive
        @winner = "Team 1"
      elsif team2_alive && !team1_alive
        @winner = "Team 2"
      else
        @winner = "Draw"
      end
    end

    def find_girl_by_id(id)
      (@team1 + @team2).find { |g| g[:id] == id }
    end

    def log_event(message)
      @battle_log << message
      puts message if ENV['BATTLE_DEBUG']
    end

    def format_girl_state(girl)
      {
        name: girl[:name],
        hp: "#{girl[:current_hp]}/#{girl[:max_hp]}",
        brilliance: girl[:brilliance],
        alive: girl[:alive],
        status_effects: girl[:status_effects].keys
      }
    end
  end
end
