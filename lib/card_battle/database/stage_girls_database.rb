# frozen_string_literal: true

require_relative 'database_manager'

module CardBattle
  module Database
    # SQLite database for Stage Girls and their costumes
    class StageGirlsDatabase < BaseDatabase
      def initialize
        super('stage_girls')
      end

      # Stage Girls CRUD operations
      def create_stage_girl(name:, school:, base_stats: {})
        sql = <<~SQL
          INSERT INTO stage_girls (name, school, base_stats, created_at, updated_at)
          VALUES (?, ?, ?, datetime('now'), datetime('now'))
        SQL
        
        execute_insert(sql, [name, school, base_stats.to_json])
      end

      def get_stage_girl(id)
        sql = "SELECT * FROM stage_girls WHERE id = ?"
        result = execute_query(sql, [id]).first
        parse_stage_girl(result) if result
      end

      def get_stage_girl_by_name(name)
        sql = "SELECT * FROM stage_girls WHERE name = ?"
        result = execute_query(sql, [name]).first
        parse_stage_girl(result) if result
      end

      def get_all_stage_girls
        sql = "SELECT * FROM stage_girls ORDER BY name"
        results = execute_query(sql)
        results.map { |row| parse_stage_girl(row) }
      end

      def get_stage_girls_by_school(school)
        sql = "SELECT * FROM stage_girls WHERE school = ? ORDER BY name"
        results = execute_query(sql, [school])
        results.map { |row| parse_stage_girl(row) }
      end

      def update_stage_girl(id, attributes = {})
        set_clauses = []
        params = []
        
        attributes.each do |key, value|
          case key
          when :base_stats
            set_clauses << "base_stats = ?"
            params << value.to_json
          else
            set_clauses << "#{key} = ?"
            params << value
          end
        end
        
        set_clauses << "updated_at = datetime('now')"
        params << id
        
        sql = "UPDATE stage_girls SET #{set_clauses.join(', ')} WHERE id = ?"
        execute_query(sql, params)
      end

      def delete_stage_girl(id)
        # Delete associated costumes first
        execute_query("DELETE FROM costumes WHERE stage_girl_id = ?", [id])
        # Delete stage girl
        execute_query("DELETE FROM stage_girls WHERE id = ?", [id])
      end

      # Costumes CRUD operations
      def create_costume(stage_girl_id:, karth_id:, name:, attribute:, rarity:, stats: {}, skills: {}, act_data: {})
        sql = <<~SQL
          INSERT INTO costumes (
            stage_girl_id, karth_id, name, attribute, rarity, 
            stats, skills, act_data, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        SQL
        
        execute_insert(sql, [
          stage_girl_id, karth_id, name.to_json, attribute, rarity,
          stats.to_json, skills.to_json, act_data.to_json
        ])
      end

      def get_costume(id)
        sql = "SELECT * FROM costumes WHERE id = ?"
        result = execute_query(sql, [id]).first
        parse_costume(result) if result
      end

      def get_costume_by_karth_id(karth_id)
        sql = "SELECT * FROM costumes WHERE karth_id = ?"
        result = execute_query(sql, [karth_id]).first
        parse_costume(result) if result
      end

      def get_costumes_for_stage_girl(stage_girl_id)
        sql = "SELECT * FROM costumes WHERE stage_girl_id = ? ORDER BY rarity DESC, karth_id"
        results = execute_query(sql, [stage_girl_id])
        results.map { |row| parse_costume(row) }
      end

      def get_all_costumes
        sql = <<~SQL
          SELECT c.*, sg.name as stage_girl_name, sg.school 
          FROM costumes c 
          JOIN stage_girls sg ON c.stage_girl_id = sg.id 
          ORDER BY sg.name, c.rarity DESC
        SQL
        results = execute_query(sql)
        results.map { |row| parse_costume(row) }
      end

      def get_costumes_by_attribute(attribute)
        sql = <<~SQL
          SELECT c.*, sg.name as stage_girl_name, sg.school 
          FROM costumes c 
          JOIN stage_girls sg ON c.stage_girl_id = sg.id 
          WHERE c.attribute = ? 
          ORDER BY sg.name
        SQL
        results = execute_query(sql, [attribute])
        results.map { |row| parse_costume(row) }
      end

      def get_costumes_by_school(school)
        sql = <<~SQL
          SELECT c.*, sg.name as stage_girl_name, sg.school 
          FROM costumes c 
          JOIN stage_girls sg ON c.stage_girl_id = sg.id 
          WHERE sg.school = ? 
          ORDER BY sg.name, c.rarity DESC
        SQL
        results = execute_query(sql, [school])
        results.map { |row| parse_costume(row) }
      end

      def update_costume(id, attributes = {})
        set_clauses = []
        params = []
        
        attributes.each do |key, value|
          case key
          when :name, :stats, :skills, :act_data
            set_clauses << "#{key} = ?"
            params << value.to_json
          else
            set_clauses << "#{key} = ?"
            params << value
          end
        end
        
        set_clauses << "updated_at = datetime('now')"
        params << id
        
        sql = "UPDATE costumes SET #{set_clauses.join(', ')} WHERE id = ?"
        execute_query(sql, params)
      end

      def delete_costume(id)
        execute_query("DELETE FROM costumes WHERE id = ?", [id])
      end

      # Search and filter operations
      def search_costumes(query)
        sql = <<~SQL
          SELECT c.*, sg.name as stage_girl_name, sg.school 
          FROM costumes c 
          JOIN stage_girls sg ON c.stage_girl_id = sg.id 
          WHERE sg.name LIKE ? OR json_extract(c.name, '$.en') LIKE ? 
          ORDER BY sg.name
        SQL
        like_query = "%#{query}%"
        results = execute_query(sql, [like_query, like_query])
        results.map { |row| parse_costume(row) }
      end

      def get_costume_statistics
        sql = <<~SQL
          SELECT 
            COUNT(*) as total_costumes,
            COUNT(DISTINCT stage_girl_id) as stage_girls_with_costumes,
            COUNT(CASE WHEN rarity = 4 THEN 1 END) as four_star_costumes,
            COUNT(CASE WHEN rarity = 3 THEN 1 END) as three_star_costumes,
            COUNT(CASE WHEN rarity = 2 THEN 1 END) as two_star_costumes,
            COUNT(CASE WHEN rarity = 1 THEN 1 END) as one_star_costumes
          FROM costumes
        SQL
        execute_query(sql).first
      end

      private

      def setup_database
        create_stage_girls_table
        create_costumes_table
        create_indexes
      end

      def create_stage_girls_table
        sql = <<~SQL
          CREATE TABLE IF NOT EXISTS stage_girls (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            school TEXT NOT NULL,
            base_stats TEXT NOT NULL DEFAULT '{}',
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL
          )
        SQL
        execute_query(sql)
      end

      def create_costumes_table
        sql = <<~SQL
          CREATE TABLE IF NOT EXISTS costumes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            stage_girl_id INTEGER NOT NULL,
            karth_id TEXT NOT NULL UNIQUE,
            name TEXT NOT NULL DEFAULT '{}',
            attribute TEXT,
            rarity INTEGER NOT NULL DEFAULT 1,
            stats TEXT NOT NULL DEFAULT '{}',
            skills TEXT NOT NULL DEFAULT '{}',
            act_data TEXT NOT NULL DEFAULT '{}',
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (stage_girl_id) REFERENCES stage_girls (id) ON DELETE CASCADE
          )
        SQL
        execute_query(sql)
      end

      def create_indexes
        indexes = [
          "CREATE INDEX IF NOT EXISTS idx_stage_girls_school ON stage_girls (school)",
          "CREATE INDEX IF NOT EXISTS idx_stage_girls_name ON stage_girls (name)",
          "CREATE INDEX IF NOT EXISTS idx_costumes_stage_girl_id ON costumes (stage_girl_id)",
          "CREATE INDEX IF NOT EXISTS idx_costumes_karth_id ON costumes (karth_id)",
          "CREATE INDEX IF NOT EXISTS idx_costumes_attribute ON costumes (attribute)",
          "CREATE INDEX IF NOT EXISTS idx_costumes_rarity ON costumes (rarity)"
        ]
        
        indexes.each { |sql| execute_query(sql) }
      end

      def parse_stage_girl(row)
        return nil unless row
        
        {
          id: row['id'],
          name: row['name'],
          school: row['school'],
          base_stats: JSON.parse(row['base_stats'] || '{}'),
          created_at: row['created_at'],
          updated_at: row['updated_at']
        }
      end

      def parse_costume(row)
        return nil unless row
        
        {
          id: row['id'],
          stage_girl_id: row['stage_girl_id'],
          karth_id: row['karth_id'],
          name: JSON.parse(row['name'] || '{}'),
          attribute: row['attribute'],
          rarity: row['rarity'],
          stats: JSON.parse(row['stats'] || '{}'),
          skills: JSON.parse(row['skills'] || '{}'),
          act_data: JSON.parse(row['act_data'] || '{}'),
          stage_girl_name: row['stage_girl_name'],
          school: row['school'],
          created_at: row['created_at'],
          updated_at: row['updated_at']
        }
      end
    end
  end
end
