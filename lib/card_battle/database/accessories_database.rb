# frozen_string_literal: true

require_relative 'database_manager'

module CardBattle
  module Database
    # SQLite database for Accessories
    class AccessoriesDatabase < BaseDatabase
      def initialize
        super('accessories')
      end

      # Accessories CRUD operations
      def create_accessory(karth_id:, name:, description: '', rarity:, max_level:, stats: {}, effects: {}, artwork_info: {}, category: '')
        sql = <<~SQL
          INSERT INTO accessories (
            karth_id, name, description, rarity, max_level, category,
            stats, effects, artwork_info, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        SQL
        
        execute_insert(sql, [
          karth_id, name.to_json, description.to_json, rarity, max_level, category,
          stats.to_json, effects.to_json, artwork_info.to_json
        ])
      end

      def get_accessory(id)
        sql = "SELECT * FROM accessories WHERE id = ?"
        result = execute_query(sql, [id]).first
        parse_accessory(result) if result
      end

      def get_accessory_by_karth_id(karth_id)
        sql = "SELECT * FROM accessories WHERE karth_id = ?"
        result = execute_query(sql, [karth_id]).first
        parse_accessory(result) if result
      end

      def get_all_accessories
        sql = "SELECT * FROM accessories ORDER BY rarity DESC, category, karth_id"
        results = execute_query(sql)
        results.map { |row| parse_accessory(row) }
      end

      def get_accessories_by_rarity(rarity)
        sql = "SELECT * FROM accessories WHERE rarity = ? ORDER BY category, karth_id"
        results = execute_query(sql, [rarity])
        results.map { |row| parse_accessory(row) }
      end

      def get_accessories_by_category(category)
        sql = "SELECT * FROM accessories WHERE category = ? ORDER BY rarity DESC, karth_id"
        results = execute_query(sql, [category])
        results.map { |row| parse_accessory(row) }
      end

      def get_accessories_by_stat_type(stat_type)
        sql = "SELECT * FROM accessories WHERE json_extract(stats, ?) IS NOT NULL ORDER BY rarity DESC"
        results = execute_query(sql, ["$.#{stat_type}"])
        results.map { |row| parse_accessory(row) }
      end

      def search_accessories(query)
        sql = <<~SQL
          SELECT * FROM accessories 
          WHERE json_extract(name, '$.en') LIKE ? 
             OR json_extract(name, '$.ja') LIKE ?
             OR json_extract(description, '$.en') LIKE ?
             OR category LIKE ?
          ORDER BY rarity DESC, category, karth_id
        SQL
        like_query = "%#{query}%"
        results = execute_query(sql, [like_query, like_query, like_query, like_query])
        results.map { |row| parse_accessory(row) }
      end

      def update_accessory(id, attributes = {})
        set_clauses = []
        params = []
        
        attributes.each do |key, value|
          case key
          when :name, :description, :stats, :effects, :artwork_info
            set_clauses << "#{key} = ?"
            params << value.to_json
          else
            set_clauses << "#{key} = ?"
            params << value
          end
        end
        
        set_clauses << "updated_at = datetime('now')"
        params << id
        
        sql = "UPDATE accessories SET #{set_clauses.join(', ')} WHERE id = ?"
        execute_query(sql, params)
      end

      def delete_accessory(id)
        # Delete associated levels and effects first
        execute_query("DELETE FROM accessory_levels WHERE accessory_id = ?", [id])
        execute_query("DELETE FROM accessory_effects WHERE accessory_id = ?", [id])
        # Delete accessory
        execute_query("DELETE FROM accessories WHERE id = ?", [id])
      end

      # Accessory level progression operations
      def create_accessory_level(accessory_id:, level:, required_exp:, stats_at_level: {})
        sql = <<~SQL
          INSERT INTO accessory_levels (accessory_id, level, required_exp, stats_at_level, created_at)
          VALUES (?, ?, ?, ?, datetime('now'))
        SQL
        
        execute_insert(sql, [accessory_id, level, required_exp, stats_at_level.to_json])
      end

      def get_accessory_levels(accessory_id)
        sql = "SELECT * FROM accessory_levels WHERE accessory_id = ? ORDER BY level"
        results = execute_query(sql, [accessory_id])
        results.map { |row| parse_accessory_level(row) }
      end

      def get_accessory_level(accessory_id, level)
        sql = "SELECT * FROM accessory_levels WHERE accessory_id = ? AND level = ?"
        result = execute_query(sql, [accessory_id, level]).first
        parse_accessory_level(result) if result
      end

      def update_accessory_level(id, attributes = {})
        set_clauses = []
        params = []
        
        attributes.each do |key, value|
          case key
          when :stats_at_level
            set_clauses << "#{key} = ?"
            params << value.to_json
          else
            set_clauses << "#{key} = ?"
            params << value
          end
        end
        
        params << id
        sql = "UPDATE accessory_levels SET #{set_clauses.join(', ')} WHERE id = ?"
        execute_query(sql, params)
      end

      def delete_accessory_levels(accessory_id)
        execute_query("DELETE FROM accessory_levels WHERE accessory_id = ?", [accessory_id])
      end

      # Accessory effects operations
      def create_accessory_effect(accessory_id:, effect_type:, effect_name:, effect_description:, effect_values: {}, conditions: {}, stackable: false)
        sql = <<~SQL
          INSERT INTO accessory_effects (
            accessory_id, effect_type, effect_name, effect_description, 
            effect_values, conditions, stackable, created_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'))
        SQL
        
        execute_insert(sql, [
          accessory_id, effect_type, effect_name.to_json, effect_description.to_json,
          effect_values.to_json, conditions.to_json, stackable ? 1 : 0
        ])
      end

      def get_accessory_effects(accessory_id)
        sql = "SELECT * FROM accessory_effects WHERE accessory_id = ? ORDER BY effect_type"
        results = execute_query(sql, [accessory_id])
        results.map { |row| parse_accessory_effect(row) }
      end

      def get_effects_by_type(effect_type)
        sql = <<~SQL
          SELECT ae.*, a.karth_id, a.name as accessory_name, a.rarity, a.category
          FROM accessory_effects ae
          JOIN accessories a ON ae.accessory_id = a.id
          WHERE ae.effect_type = ?
          ORDER BY a.rarity DESC, a.karth_id
        SQL
        results = execute_query(sql, [effect_type])
        results.map { |row| parse_accessory_effect(row) }
      end

      def get_stackable_effects
        sql = <<~SQL
          SELECT ae.*, a.karth_id, a.name as accessory_name, a.rarity, a.category
          FROM accessory_effects ae
          JOIN accessories a ON ae.accessory_id = a.id
          WHERE ae.stackable = 1
          ORDER BY ae.effect_type, a.rarity DESC
        SQL
        results = execute_query(sql)
        results.map { |row| parse_accessory_effect(row) }
      end

      def delete_accessory_effects(accessory_id)
        execute_query("DELETE FROM accessory_effects WHERE accessory_id = ?", [accessory_id])
      end

      # Statistics and analysis
      def get_accessory_statistics
        sql = <<~SQL
          SELECT 
            COUNT(*) as total_accessories,
            COUNT(CASE WHEN rarity = 4 THEN 1 END) as four_star_accessories,
            COUNT(CASE WHEN rarity = 3 THEN 1 END) as three_star_accessories,
            COUNT(CASE WHEN rarity = 2 THEN 1 END) as two_star_accessories,
            COUNT(CASE WHEN rarity = 1 THEN 1 END) as one_star_accessories,
            COUNT(DISTINCT category) as unique_categories,
            AVG(max_level) as average_max_level
          FROM accessories
        SQL
        execute_query(sql).first
      end

      def get_category_distribution
        sql = <<~SQL
          SELECT 
            category,
            COUNT(*) as count,
            AVG(rarity) as average_rarity
          FROM accessories
          WHERE category != ''
          GROUP BY category
          ORDER BY count DESC
        SQL
        execute_query(sql)
      end

      def get_effect_type_distribution
        sql = <<~SQL
          SELECT 
            effect_type,
            COUNT(*) as count,
            COUNT(DISTINCT accessory_id) as unique_accessories,
            COUNT(CASE WHEN stackable = 1 THEN 1 END) as stackable_count
          FROM accessory_effects
          GROUP BY effect_type
          ORDER BY count DESC
        SQL
        execute_query(sql)
      end

      def get_stackable_combinations
        sql = <<~SQL
          SELECT 
            ae1.effect_type as effect_type_1,
            ae2.effect_type as effect_type_2,
            COUNT(*) as combination_count
          FROM accessory_effects ae1
          JOIN accessory_effects ae2 ON ae1.accessory_id != ae2.accessory_id
          WHERE ae1.stackable = 1 AND ae2.stackable = 1
          GROUP BY ae1.effect_type, ae2.effect_type
          HAVING ae1.effect_type < ae2.effect_type
          ORDER BY combination_count DESC
        SQL
        execute_query(sql)
      end

      private

      def setup_database
        create_accessories_table
        create_accessory_levels_table
        create_accessory_effects_table
        create_indexes
      end

      def create_accessories_table
        sql = <<~SQL
          CREATE TABLE IF NOT EXISTS accessories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            karth_id TEXT NOT NULL UNIQUE,
            name TEXT NOT NULL DEFAULT '{}',
            description TEXT NOT NULL DEFAULT '{}',
            rarity INTEGER NOT NULL DEFAULT 1,
            max_level INTEGER NOT NULL DEFAULT 1,
            category TEXT NOT NULL DEFAULT '',
            stats TEXT NOT NULL DEFAULT '{}',
            effects TEXT NOT NULL DEFAULT '{}',
            artwork_info TEXT NOT NULL DEFAULT '{}',
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL
          )
        SQL
        execute_query(sql)
      end

      def create_accessory_levels_table
        sql = <<~SQL
          CREATE TABLE IF NOT EXISTS accessory_levels (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            accessory_id INTEGER NOT NULL,
            level INTEGER NOT NULL,
            required_exp INTEGER NOT NULL DEFAULT 0,
            stats_at_level TEXT NOT NULL DEFAULT '{}',
            created_at TEXT NOT NULL,
            FOREIGN KEY (accessory_id) REFERENCES accessories (id) ON DELETE CASCADE,
            UNIQUE(accessory_id, level)
          )
        SQL
        execute_query(sql)
      end

      def create_accessory_effects_table
        sql = <<~SQL
          CREATE TABLE IF NOT EXISTS accessory_effects (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            accessory_id INTEGER NOT NULL,
            effect_type TEXT NOT NULL,
            effect_name TEXT NOT NULL DEFAULT '{}',
            effect_description TEXT NOT NULL DEFAULT '{}',
            effect_values TEXT NOT NULL DEFAULT '{}',
            conditions TEXT NOT NULL DEFAULT '{}',
            stackable INTEGER NOT NULL DEFAULT 0,
            created_at TEXT NOT NULL,
            FOREIGN KEY (accessory_id) REFERENCES accessories (id) ON DELETE CASCADE
          )
        SQL
        execute_query(sql)
      end

      def create_indexes
        indexes = [
          "CREATE INDEX IF NOT EXISTS idx_accessories_karth_id ON accessories (karth_id)",
          "CREATE INDEX IF NOT EXISTS idx_accessories_rarity ON accessories (rarity)",
          "CREATE INDEX IF NOT EXISTS idx_accessories_category ON accessories (category)",
          "CREATE INDEX IF NOT EXISTS idx_accessories_max_level ON accessories (max_level)",
          "CREATE INDEX IF NOT EXISTS idx_accessory_levels_accessory_id ON accessory_levels (accessory_id)",
          "CREATE INDEX IF NOT EXISTS idx_accessory_levels_level ON accessory_levels (level)",
          "CREATE INDEX IF NOT EXISTS idx_accessory_effects_accessory_id ON accessory_effects (accessory_id)",
          "CREATE INDEX IF NOT EXISTS idx_accessory_effects_type ON accessory_effects (effect_type)",
          "CREATE INDEX IF NOT EXISTS idx_accessory_effects_stackable ON accessory_effects (stackable)"
        ]
        
        indexes.each { |sql| execute_query(sql) }
      end

      def parse_accessory(row)
        return nil unless row
        
        {
          id: row['id'],
          karth_id: row['karth_id'],
          name: JSON.parse(row['name'] || '{}'),
          description: JSON.parse(row['description'] || '{}'),
          rarity: row['rarity'],
          max_level: row['max_level'],
          category: row['category'],
          stats: JSON.parse(row['stats'] || '{}'),
          effects: JSON.parse(row['effects'] || '{}'),
          artwork_info: JSON.parse(row['artwork_info'] || '{}'),
          created_at: row['created_at'],
          updated_at: row['updated_at']
        }
      end

      def parse_accessory_level(row)
        return nil unless row
        
        {
          id: row['id'],
          accessory_id: row['accessory_id'],
          level: row['level'],
          required_exp: row['required_exp'],
          stats_at_level: JSON.parse(row['stats_at_level'] || '{}'),
          created_at: row['created_at']
        }
      end

      def parse_accessory_effect(row)
        return nil unless row
        
        {
          id: row['id'],
          accessory_id: row['accessory_id'],
          effect_type: row['effect_type'],
          effect_name: JSON.parse(row['effect_name'] || '{}'),
          effect_description: JSON.parse(row['effect_description'] || '{}'),
          effect_values: JSON.parse(row['effect_values'] || '{}'),
          conditions: JSON.parse(row['conditions'] || '{}'),
          stackable: row['stackable'] == 1,
          karth_id: row['karth_id'],
          accessory_name: row['accessory_name'] ? JSON.parse(row['accessory_name']) : nil,
          rarity: row['rarity'],
          category: row['category'],
          created_at: row['created_at']
        }
      end
    end
  end
end
