# frozen_string_literal: true

module CardBattle
  module Database
    # Complete character ID to name and school mapping for Revue Starlight: ReLive
    class CharacterMapping
      
      # Complete character mapping based on official game data
      CHARACTER_DATA = {
        # Seisho Music Academy (101-109)
        101 => { name: "<PERSON>", school: "Seisho Music Academy" },
        102 => { name: "<PERSON><PERSON><PERSON>", school: "Seisho Music Academy" },
        103 => { name: "Maya Tendo", school: "Seisho Music Academy" },
        104 => { name: "<PERSON><PERSON>", school: "Seisho Music Academy" },
        105 => { name: "Nana Daiba", school: "Seisho Music Academy" },
        106 => { name: "<PERSON><PERSON>gu<PERSON>", school: "Seisho Music Academy" },
        107 => { name: "<PERSON><PERSON><PERSON>", school: "Seisho Music Academy" },
        108 => { name: "Fu<PERSON>ba Isurugi", school: "Seisho Music Academy" },
        109 => { name: "Kaoruko <PERSON>", school: "Seisho Music Academy" },
        
        # Rinmeikan Girls School (201-208)
        201 => { name: "<PERSON><PERSON>", school: "Rinmeikan Girls School" },
        202 => { name: "<PERSON><PERSON>", school: "Rinmeikan Girls School" },
        203 => { name: "<PERSON><PERSON>", school: "Rinmeikan Girls School" },
        204 => { name: "Yuyuko Tanaka", school: "Rinmeikan Girls School" },
        205 => { name: "Ichie Otonashi", school: "Rinmeikan Girls School" },
        
        # Frontier School of Arts (301-305)
        301 => { name: "Lalafin Nonomiya", school: "Frontier School of Arts" },
        302 => { name: "Shizuha Kocho", school: "Frontier School of Arts" },
        303 => { name: "Misora Kano", school: "Frontier School of Arts" },
        304 => { name: "Tsukasa Ebisu", school: "Frontier School of Arts" },
        305 => { name: "Aruru Otsuki", school: "Frontier School of Arts" },
        
        # Siegfeld Institute of Music (401-410)
        401 => { name: "Akira Yukishiro", school: "Siegfeld Institute of Music" },
        402 => { name: "Michiru Otori", school: "Siegfeld Institute of Music" },
        403 => { name: "Yachiyo Tsuruhime", school: "Siegfeld Institute of Music" },
        404 => { name: "Mei Fan Huang", school: "Siegfeld Institute of Music" },
        405 => { name: "Shiori Yumeoji", school: "Siegfeld Institute of Music" },
        406 => { name: "Stella Takachiho", school: "Siegfeld Institute of Music" },
        407 => { name: "Shiro Ogami", school: "Siegfeld Institute of Music" },
        408 => { name: "Ryoko Kobato", school: "Siegfeld Institute of Music" },
        409 => { name: "Minku Umibe", school: "Siegfeld Institute of Music" },
        410 => { name: "Kuina Moriyasu", school: "Siegfeld Institute of Music" },
        
        # Seiran General Art Institute (501-503)
        501 => { name: "Koharu Yanagi", school: "Seiran General Art Institute" },
        502 => { name: "Suzu Minase", school: "Seiran General Art Institute" },
        503 => { name: "Hisame Honami", school: "Seiran General Art Institute" }
      }.freeze
      
      def self.get_character_name(character_id)
        CHARACTER_DATA.dig(character_id, :name) || "Unknown Character #{character_id}"
      end
      
      def self.get_character_school(character_id)
        CHARACTER_DATA.dig(character_id, :school) || "Unknown School"
      end
      
      def self.get_character_data(character_id)
        CHARACTER_DATA[character_id] || {
          name: "Unknown Character #{character_id}",
          school: "Unknown School"
        }
      end
      
      def self.all_characters
        CHARACTER_DATA
      end
      
      def self.characters_by_school(school)
        CHARACTER_DATA.select { |_, data| data[:school] == school }
      end
      
      # School validation
      VALID_SCHOOLS = [
        "Seisho Music Academy",
        "Rinmeikan Girls School", 
        "Frontier School of Arts",
        "Siegfeld Institute of Music",
        "Seiran General Art Institute"
      ].freeze
      
      def self.valid_school?(school)
        VALID_SCHOOLS.include?(school)
      end
      
      # Character count by school for validation
      def self.school_character_counts
        {
          "Seisho Music Academy" => 9,      # Karen, Claudine, Maya, Junna, Nana, Hikari, Mahiru, Futaba, Kaoruko
          "Rinmeikan Girls School" => 5,    # Tamao, Rui, Fumi, Yuyuko, Ichie  
          "Frontier School of Arts" => 5,   # Lalafin, Shizuha, Misora, Tsukasa, Aruru
          "Siegfeld Institute of Music" => 10, # Akira, Michiru, Yachiyo, Mei Fan, Shiori, Stella, Shiro, Ryoko, Minku, Kuina
          "Seiran General Art Institute" => 3  # Suzu, Hisame, Koharu
        }
      end
      
      def self.validate_character_distribution
        actual_counts = {}
        CHARACTER_DATA.each do |_, data|
          school = data[:school]
          actual_counts[school] = (actual_counts[school] || 0) + 1
        end
        
        expected_counts = school_character_counts
        
        validation_results = {}
        expected_counts.each do |school, expected|
          actual = actual_counts[school] || 0
          validation_results[school] = {
            expected: expected,
            actual: actual,
            valid: expected == actual
          }
        end
        
        validation_results
      end
    end
  end
end
