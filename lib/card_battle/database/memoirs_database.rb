# frozen_string_literal: true

require 'sqlite3'
require 'json'

module CardBattle
  module Database
    # SQLite database for Memoirs
    class MemoirsDatabase
      attr_reader :db, :db_path

      def initialize
        @db_path = File.join(Dir.pwd, 'db', 'memoirs.db')
        @db = SQLite3::Database.new(@db_path)
        @db.results_as_hash = true
        setup_database
      end

      def close
        @db.close if @db
      end

      def status
        return 'Closed' if @db.closed?

        table_count = @db.execute("SELECT COUNT(*) as count FROM sqlite_master WHERE type='table'")[0]['count']
        "Open (#{table_count} tables)"
      end

      def execute_query(sql, params = [])
        @db.execute(sql, params)
      end

      def execute_insert(sql, params = [])
        @db.execute(sql, params)
        @db.last_insert_row_id
      end

      # Memoirs CRUD operations
      def create_memoir(karth_id:, name:, description: '', rarity:, max_level:, stats: {}, effects: {}, artwork_info: {})
        sql = <<~SQL
          INSERT INTO memoirs (
            karth_id, name, description, rarity, max_level,
            stats, effects, artwork_info, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        SQL
        
        execute_insert(sql, [
          karth_id, name.to_json, description.to_json, rarity, max_level,
          stats.to_json, effects.to_json, artwork_info.to_json
        ])
      end

      def get_memoir(id)
        sql = "SELECT * FROM memoirs WHERE id = ?"
        result = execute_query(sql, [id]).first
        parse_memoir(result) if result
      end

      def get_memoir_by_karth_id(karth_id)
        sql = "SELECT * FROM memoirs WHERE karth_id = ?"
        result = execute_query(sql, [karth_id]).first
        parse_memoir(result) if result
      end

      def get_all_memoirs
        sql = "SELECT * FROM memoirs ORDER BY rarity DESC, karth_id"
        results = execute_query(sql)
        results.map { |row| parse_memoir(row) }
      end

      def get_memoirs_by_rarity(rarity)
        sql = "SELECT * FROM memoirs WHERE rarity = ? ORDER BY karth_id"
        results = execute_query(sql, [rarity])
        results.map { |row| parse_memoir(row) }
      end

      def get_memoirs_by_stat_type(stat_type)
        sql = "SELECT * FROM memoirs WHERE json_extract(stats, ?) IS NOT NULL ORDER BY rarity DESC"
        results = execute_query(sql, ["$.#{stat_type}"])
        results.map { |row| parse_memoir(row) }
      end

      def search_memoirs(query)
        sql = <<~SQL
          SELECT * FROM memoirs 
          WHERE json_extract(name, '$.en') LIKE ? 
             OR json_extract(name, '$.ja') LIKE ?
             OR json_extract(description, '$.en') LIKE ?
          ORDER BY rarity DESC, karth_id
        SQL
        like_query = "%#{query}%"
        results = execute_query(sql, [like_query, like_query, like_query])
        results.map { |row| parse_memoir(row) }
      end

      def update_memoir(id, attributes = {})
        set_clauses = []
        params = []
        
        attributes.each do |key, value|
          case key
          when :name, :description, :stats, :effects, :artwork_info
            set_clauses << "#{key} = ?"
            params << value.to_json
          else
            set_clauses << "#{key} = ?"
            params << value
          end
        end
        
        set_clauses << "updated_at = datetime('now')"
        params << id
        
        sql = "UPDATE memoirs SET #{set_clauses.join(', ')} WHERE id = ?"
        execute_query(sql, params)
      end

      def delete_memoir(id)
        execute_query("DELETE FROM memoirs WHERE id = ?", [id])
      end

      # Memoir level progression operations
      def create_memoir_level(memoir_id:, level:, required_exp:, stats_at_level: {})
        sql = <<~SQL
          INSERT INTO memoir_levels (memoir_id, level, required_exp, stats_at_level, created_at)
          VALUES (?, ?, ?, ?, datetime('now'))
        SQL
        
        execute_insert(sql, [memoir_id, level, required_exp, stats_at_level.to_json])
      end

      def get_memoir_levels(memoir_id)
        sql = "SELECT * FROM memoir_levels WHERE memoir_id = ? ORDER BY level"
        results = execute_query(sql, [memoir_id])
        results.map { |row| parse_memoir_level(row) }
      end

      def get_memoir_level(memoir_id, level)
        sql = "SELECT * FROM memoir_levels WHERE memoir_id = ? AND level = ?"
        result = execute_query(sql, [memoir_id, level]).first
        parse_memoir_level(result) if result
      end

      def update_memoir_level(id, attributes = {})
        set_clauses = []
        params = []
        
        attributes.each do |key, value|
          case key
          when :stats_at_level
            set_clauses << "#{key} = ?"
            params << value.to_json
          else
            set_clauses << "#{key} = ?"
            params << value
          end
        end
        
        params << id
        sql = "UPDATE memoir_levels SET #{set_clauses.join(', ')} WHERE id = ?"
        execute_query(sql, params)
      end

      def delete_memoir_levels(memoir_id)
        execute_query("DELETE FROM memoir_levels WHERE memoir_id = ?", [memoir_id])
      end

      # Memoir effects operations
      def create_memoir_effect(memoir_id:, effect_type:, effect_name:, effect_description:, effect_values: {}, conditions: {})
        sql = <<~SQL
          INSERT INTO memoir_effects (
            memoir_id, effect_type, effect_name, effect_description,
            effect_values, conditions, created_at
          ) VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
        SQL

        execute_insert(sql, [
          memoir_id, effect_type, effect_name.to_json, effect_description.to_json,
          effect_values.to_json, conditions.to_json
        ])
      end

      def get_memoir_effects(memoir_id)
        sql = "SELECT * FROM memoir_effects WHERE memoir_id = ? ORDER BY effect_type"
        results = execute_query(sql, [memoir_id])
        results.map { |row| parse_memoir_effect(row) }
      end

      def get_effects_by_type(effect_type)
        sql = <<~SQL
          SELECT me.*, m.karth_id, m.name as memoir_name, m.rarity
          FROM memoir_effects me
          JOIN memoirs m ON me.memoir_id = m.id
          WHERE me.effect_type = ?
          ORDER BY m.rarity DESC, m.karth_id
        SQL
        results = execute_query(sql, [effect_type])
        results.map { |row| parse_memoir_effect(row) }
      end

      def delete_memoir_effects(memoir_id)
        execute_query("DELETE FROM memoir_effects WHERE memoir_id = ?", [memoir_id])
      end

      # Statistics and analysis
      def get_memoir_statistics
        sql = <<~SQL
          SELECT 
            COUNT(*) as total_memoirs,
            COUNT(CASE WHEN rarity = 4 THEN 1 END) as four_star_memoirs,
            COUNT(CASE WHEN rarity = 3 THEN 1 END) as three_star_memoirs,
            COUNT(CASE WHEN rarity = 2 THEN 1 END) as two_star_memoirs,
            COUNT(CASE WHEN rarity = 1 THEN 1 END) as one_star_memoirs,
            AVG(max_level) as average_max_level
          FROM memoirs
        SQL
        execute_query(sql).first
      end

      def get_effect_type_distribution
        sql = <<~SQL
          SELECT 
            effect_type,
            COUNT(*) as count,
            COUNT(DISTINCT memoir_id) as unique_memoirs
          FROM memoir_effects
          GROUP BY effect_type
          ORDER BY count DESC
        SQL
        execute_query(sql)
      end

      private

      def setup_database
        create_memoirs_table
        create_memoir_levels_table
        create_memoir_effects_table
        create_indexes
      end

      def create_memoirs_table
        sql = <<~SQL
          CREATE TABLE IF NOT EXISTS memoirs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            karth_id TEXT NOT NULL UNIQUE,
            name TEXT NOT NULL DEFAULT '{}',
            description TEXT NOT NULL DEFAULT '{}',
            rarity INTEGER NOT NULL DEFAULT 1,
            max_level INTEGER NOT NULL DEFAULT 1,
            stats TEXT NOT NULL DEFAULT '{}',
            effects TEXT NOT NULL DEFAULT '{}',
            artwork_info TEXT NOT NULL DEFAULT '{}',
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL
          )
        SQL
        execute_query(sql)
      end

      def create_memoir_levels_table
        sql = <<~SQL
          CREATE TABLE IF NOT EXISTS memoir_levels (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            memoir_id INTEGER NOT NULL,
            level INTEGER NOT NULL,
            required_exp INTEGER NOT NULL DEFAULT 0,
            stats_at_level TEXT NOT NULL DEFAULT '{}',
            created_at TEXT NOT NULL,
            FOREIGN KEY (memoir_id) REFERENCES memoirs (id) ON DELETE CASCADE,
            UNIQUE(memoir_id, level)
          )
        SQL
        execute_query(sql)
      end

      def create_memoir_effects_table
        sql = <<~SQL
          CREATE TABLE IF NOT EXISTS memoir_effects (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            memoir_id INTEGER NOT NULL,
            effect_type TEXT NOT NULL,
            effect_name TEXT NOT NULL DEFAULT '{}',
            effect_description TEXT NOT NULL DEFAULT '{}',
            effect_values TEXT NOT NULL DEFAULT '{}',
            conditions TEXT NOT NULL DEFAULT '{}',
            created_at TEXT NOT NULL,
            FOREIGN KEY (memoir_id) REFERENCES memoirs (id) ON DELETE CASCADE
          )
        SQL
        execute_query(sql)
      end

      def create_indexes
        indexes = [
          "CREATE INDEX IF NOT EXISTS idx_memoirs_karth_id ON memoirs (karth_id)",
          "CREATE INDEX IF NOT EXISTS idx_memoirs_rarity ON memoirs (rarity)",
          "CREATE INDEX IF NOT EXISTS idx_memoirs_max_level ON memoirs (max_level)",
          "CREATE INDEX IF NOT EXISTS idx_memoir_levels_memoir_id ON memoir_levels (memoir_id)",
          "CREATE INDEX IF NOT EXISTS idx_memoir_levels_level ON memoir_levels (level)",
          "CREATE INDEX IF NOT EXISTS idx_memoir_effects_memoir_id ON memoir_effects (memoir_id)",
          "CREATE INDEX IF NOT EXISTS idx_memoir_effects_type ON memoir_effects (effect_type)"
        ]
        
        indexes.each { |sql| execute_query(sql) }
      end

      def parse_memoir(row)
        return nil unless row
        
        {
          id: row['id'],
          karth_id: row['karth_id'],
          name: JSON.parse(row['name'] || '{}'),
          description: JSON.parse(row['description'] || '{}'),
          rarity: row['rarity'],
          max_level: row['max_level'],
          stats: JSON.parse(row['stats'] || '{}'),
          effects: JSON.parse(row['effects'] || '{}'),
          artwork_info: JSON.parse(row['artwork_info'] || '{}'),
          created_at: row['created_at'],
          updated_at: row['updated_at']
        }
      end

      def parse_memoir_level(row)
        return nil unless row
        
        {
          id: row['id'],
          memoir_id: row['memoir_id'],
          level: row['level'],
          required_exp: row['required_exp'],
          stats_at_level: JSON.parse(row['stats_at_level'] || '{}'),
          created_at: row['created_at']
        }
      end

      def parse_memoir_effect(row)
        return nil unless row
        
        {
          id: row['id'],
          memoir_id: row['memoir_id'],
          effect_type: row['effect_type'],
          effect_name: JSON.parse(row['effect_name'] || '{}'),
          effect_description: JSON.parse(row['effect_description'] || '{}'),
          effect_values: JSON.parse(row['effect_values'] || '{}'),
          conditions: JSON.parse(row['conditions'] || '{}'),
          karth_id: row['karth_id'],
          memoir_name: row['memoir_name'] ? JSON.parse(row['memoir_name']) : nil,
          rarity: row['rarity'],
          created_at: row['created_at']
        }
      end
    end
  end
end
