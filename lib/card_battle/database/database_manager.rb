# frozen_string_literal: true

require 'sqlite3'
require 'json'

module CardBattle
  module Database
    # Central database manager for handling multiple SQLite databases
    class DatabaseManager
      class << self
        attr_reader :stage_girls_db, :memoirs_db, :accessories_db

        def initialize_databases
          create_database_directory
          @stage_girls_db = StageGirlsDatabase.new
          @memoirs_db = MemoirsDatabase.new
          @accessories_db = AccessoriesDatabase.new
          
          puts "✅ All databases initialized successfully"
        end

        def close_all_databases
          @stage_girls_db&.close
          @memoirs_db&.close
          @accessories_db&.close
          puts "🔒 All databases closed"
        end

        def reset_all_databases
          close_all_databases
          delete_database_files
          initialize_databases
          puts "🔄 All databases reset"
        end

        def database_status
          {
            stage_girls: @stage_girls_db&.status || 'Not initialized',
            memoirs: @memoirs_db&.status || 'Not initialized',
            accessories: @accessories_db&.status || 'Not initialized'
          }
        end

        private

        def create_database_directory
          db_dir = File.join(Dir.pwd, 'db')
          Dir.mkdir(db_dir) unless Dir.exist?(db_dir)
        end

        def delete_database_files
          db_files = [
            'db/stage_girls.db',
            'db/memoirs.db', 
            'db/accessories.db'
          ]
          
          db_files.each do |file|
            File.delete(file) if File.exist?(file)
          end
        end
      end
    end

    # Base database class with common functionality
    class BaseDatabase
      attr_reader :db, :db_path

      def initialize(db_name)
        @db_path = File.join(Dir.pwd, 'db', "#{db_name}.db")
        @db = SQLite3::Database.new(@db_path)
        @db.results_as_hash = true
        setup_database
      end

      def close
        @db.close if @db
      end

      def status
        return 'Closed' if @db.closed?
        
        table_count = @db.execute("SELECT COUNT(*) as count FROM sqlite_master WHERE type='table'")[0]['count']
        "Open (#{table_count} tables)"
      end

      def execute_query(sql, params = [])
        @db.execute(sql, params)
      end

      def execute_insert(sql, params = [])
        @db.execute(sql, params)
        @db.last_insert_row_id
      end

      def begin_transaction
        @db.execute("BEGIN TRANSACTION")
      end

      def commit_transaction
        @db.execute("COMMIT")
      end

      def rollback_transaction
        @db.execute("ROLLBACK")
      end

      private

      def setup_database
        # Override in subclasses
        raise NotImplementedError, "Subclasses must implement setup_database"
      end
    end
  end
end
