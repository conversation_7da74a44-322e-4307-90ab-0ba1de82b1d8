# frozen_string_literal: true

require_relative 'database_manager'

module CardBattle
  module Database
    # Manages migration from file-based storage to SQLite databases
    class MigrationManager
      class << self
        def migrate_all_data
          puts "🚀 Starting complete data migration..."
          
          DatabaseManager.initialize_databases
          
          migrate_stage_girls_and_costumes
          migrate_memoirs
          migrate_accessories
          
          puts "✅ All data migration completed successfully!"
          print_migration_summary
        end

        def migrate_stage_girls_and_costumes
          puts "\n📚 Migrating Stage Girls and Costumes..."

          stage_girls_db = DatabaseManager.stage_girls_db

          # Get all card files directly
          card_files = Dir.glob("lib/card_battle/cards/*.rb")
          stage_girls_map = {}
          costume_count = 0

          card_files.each do |file|
            begin
              # Load the card constant
              require file
              constant_name = File.basename(file, '.rb').upcase.gsub('-', '_')

              # Get the card data from the constant
              card_data = Object.const_get(constant_name)

              stage_girl_name = extract_stage_girl_name(card_data)
              school = extract_school(card_data)

              # Create or get stage girl
              unless stage_girls_map[stage_girl_name]
                stage_girl_id = stage_girls_db.create_stage_girl(
                  name: stage_girl_name,
                  school: school,
                  base_stats: extract_base_stats(card_data)
                )
                stage_girls_map[stage_girl_name] = stage_girl_id
                puts "  ✓ Created stage girl: #{stage_girl_name} (#{school})"
              end

              # Create costume
              stage_girl_id = stage_girls_map[stage_girl_name]
              costume_id = stage_girls_db.create_costume(
                stage_girl_id: stage_girl_id,
                karth_id: card_data.dig(:basicInfo, :cardID),
                name: extract_costume_name(card_data),
                attribute: extract_attribute(card_data),
                rarity: card_data.dig(:basicInfo, :rarity),
                stats: extract_costume_stats(card_data),
                skills: extract_costume_skills(card_data),
                act_data: extract_act_data(card_data)
              )
              costume_count += 1

              if costume_count % 50 == 0
                puts "  📊 Migrated #{costume_count} costumes..."
              end

            rescue => e
              puts "  ⚠️  Error migrating #{file}: #{e.message}"
            end
          end

          puts "  ✅ Migrated #{stage_girls_map.size} stage girls and #{costume_count} costumes"
        end

        def migrate_memoirs
          puts "\n💭 Migrating Memoirs..."
          
          memoirs_db = DatabaseManager.memoirs_db
          memoir_count = 0
          
          # Load memoir data from karth.top or existing files
          memoir_files = Dir.glob("lib/card_battle/memoirs/*.rb")
          
          memoir_files.each do |file|
            begin
              require file
              memoir_data = extract_memoir_from_file(file)
              
              if memoir_data
                memoir_id = memoirs_db.create_memoir(
                  karth_id: memoir_data[:karth_id],
                  name: memoir_data[:name],
                  description: memoir_data[:description],
                  rarity: memoir_data[:rarity],
                  max_level: memoir_data[:max_level],
                  stats: memoir_data[:stats],
                  effects: memoir_data[:effects],
                  artwork_info: memoir_data[:artwork_info]
                )
                
                # Create level progression data if available
                if memoir_data[:levels]
                  memoir_data[:levels].each do |level_data|
                    memoirs_db.create_memoir_level(
                      memoir_id: memoir_id,
                      level: level_data[:level],
                      required_exp: level_data[:required_exp],
                      stats_at_level: level_data[:stats]
                    )
                  end
                end
                
                # Create effect data if available
                if memoir_data[:effect_details]
                  memoir_data[:effect_details].each do |effect|
                    memoirs_db.create_memoir_effect(
                      memoir_id: memoir_id,
                      effect_type: effect[:type],
                      effect_name: effect[:name],
                      effect_description: effect[:description],
                      effect_values: effect[:values],
                      conditions: effect[:conditions]
                    )
                  end
                end
                
                memoir_count += 1
                puts "  ✓ Migrated memoir: #{memoir_data[:name][:en] || memoir_data[:karth_id]}"
              end
            rescue => e
              puts "  ⚠️  Error migrating memoir from #{file}: #{e.message}"
            end
          end
          
          puts "  ✅ Migrated #{memoir_count} memoirs"
        end

        def migrate_accessories
          puts "\n🎭 Migrating Accessories..."
          
          accessories_db = DatabaseManager.accessories_db
          accessory_count = 0
          
          # Load accessory data from existing files
          accessory_files = Dir.glob("lib/card_battle/accessories/*.rb")
          
          accessory_files.each do |file|
            begin
              require file
              accessory_data = extract_accessory_from_file(file)
              
              if accessory_data
                accessory_id = accessories_db.create_accessory(
                  karth_id: accessory_data[:karth_id],
                  name: accessory_data[:name],
                  description: accessory_data[:description],
                  rarity: accessory_data[:rarity],
                  max_level: accessory_data[:max_level],
                  category: accessory_data[:category],
                  stats: accessory_data[:stats],
                  effects: accessory_data[:effects],
                  artwork_info: accessory_data[:artwork_info]
                )
                
                # Create level progression data if available
                if accessory_data[:levels]
                  accessory_data[:levels].each do |level_data|
                    accessories_db.create_accessory_level(
                      accessory_id: accessory_id,
                      level: level_data[:level],
                      required_exp: level_data[:required_exp],
                      stats_at_level: level_data[:stats]
                    )
                  end
                end
                
                # Create effect data if available
                if accessory_data[:effect_details]
                  accessory_data[:effect_details].each do |effect|
                    accessories_db.create_accessory_effect(
                      accessory_id: accessory_id,
                      effect_type: effect[:type],
                      effect_name: effect[:name],
                      effect_description: effect[:description],
                      effect_values: effect[:values],
                      conditions: effect[:conditions],
                      stackable: effect[:stackable] || false
                    )
                  end
                end
                
                accessory_count += 1
                puts "  ✓ Migrated accessory: #{accessory_data[:name][:en] || accessory_data[:karth_id]}"
              end
            rescue => e
              puts "  ⚠️  Error migrating accessory from #{file}: #{e.message}"
            end
          end
          
          puts "  ✅ Migrated #{accessory_count} accessories"
        end

        def print_migration_summary
          puts "\n📊 Migration Summary:"
          
          stage_girls_stats = DatabaseManager.stage_girls_db.get_costume_statistics
          puts "  Stage Girls Database:"
          puts "    - Total costumes: #{stage_girls_stats['total_costumes']}"
          puts "    - Stage girls with costumes: #{stage_girls_stats['stage_girls_with_costumes']}"
          puts "    - 4★ costumes: #{stage_girls_stats['four_star_costumes']}"
          puts "    - 3★ costumes: #{stage_girls_stats['three_star_costumes']}"
          
          memoir_stats = DatabaseManager.memoirs_db.get_memoir_statistics
          puts "  Memoirs Database:"
          puts "    - Total memoirs: #{memoir_stats['total_memoirs']}"
          puts "    - 4★ memoirs: #{memoir_stats['four_star_memoirs']}"
          puts "    - 3★ memoirs: #{memoir_stats['three_star_memoirs']}"
          
          accessory_stats = DatabaseManager.accessories_db.get_accessory_statistics
          puts "  Accessories Database:"
          puts "    - Total accessories: #{accessory_stats['total_accessories']}"
          puts "    - 4★ accessories: #{accessory_stats['four_star_accessories']}"
          puts "    - 3★ accessories: #{accessory_stats['three_star_accessories']}"
          puts "    - Categories: #{accessory_stats['unique_categories']}"
        end

        private

        def extract_stage_girl_name(card_data)
          # Extract stage girl name from character ID
          character_id = card_data.dig(:basicInfo, :character)
          character_mapping = {
            101 => "Karen Aijo",
            102 => "Claudine Saijo",
            103 => "Maya Tendo",
            104 => "Junna Hoshimi",
            105 => "Nana Daiba",
            106 => "Hikari Kagura",
            107 => "Mahiru Tsuyuzaki",
            108 => "Futaba Isurugi",
            109 => "Kaoruko Hanayagi",
            201 => "Michiru Otori",
            202 => "Akira Yukishiro",
            203 => "Yachiyo Tsuruhime",
            204 => "Tamao Tomoe",
            205 => "Ichie Otonashi",
            206 => "Fumi Yumeoji",
            207 => "Rui Akikaze",
            208 => "Yuyuko Tanaka",
            301 => "Lalafin Nonomiya",
            302 => "Shizuha Kocho",
            303 => "Misora Kano",
            304 => "Tsukasa Ebisu"
          }
          character_mapping[character_id] || "Unknown Character #{character_id}"
        end

        def extract_school(card_data)
          # Extract school from character ID
          character_id = card_data.dig(:basicInfo, :character)
          case character_id
          when 101..109
            "Seisho Music Academy"
          when 201..208
            "Rinmeikan Girls School"
          when 301..304
            "Frontier School of Arts"
          when 401..408
            "Siegfeld Institute of Music"
          when 501..508
            "Seiran General Art Institute"
          else
            "Unknown School"
          end
        end

        def extract_base_stats(card_data)
          # Use costume stats as base (simplified approach)
          stats = card_data[:stat] || {}
          {
            hp: stats[:hp] || 1000,
            atk: stats[:atk] || 100,
            def: (stats[:pdef] || 100),
            agi: stats[:agi] || 100,
            dex: card_data.dig(:other, :dex) || 5
          }
        end

        def extract_costume_name(card_data)
          # Extract costume name with multilingual support
          name_data = card_data.dig(:basicInfo, :name) || {}
          {
            en: name_data[:en] || name_data[:ja] || 'Unknown Costume',
            ja: name_data[:ja] || ''
          }
        end

        def extract_attribute(card_data)
          # Extract attribute from base data
          attribute_mapping = {
            1 => "flower",
            2 => "wind",
            3 => "snow",
            4 => "moon",
            5 => "space",
            6 => "cloud",
            7 => "star",
            8 => "sun"
          }
          attribute_id = card_data.dig(:base, :attribute)
          attribute_mapping[attribute_id] || "unknown"
        end

        def extract_costume_stats(card_data)
          # Extract all costume stats
          stats = card_data[:stat] || {}
          base = card_data[:base] || {}
          other = card_data[:other] || {}

          {
            hp: stats[:hp],
            atk: stats[:atk],
            pdef: stats[:pdef],
            mdef: stats[:mdef],
            agi: stats[:agi],
            dex: other[:dex],
            cri: other[:cri],
            eva: other[:eva],
            cost: base[:cost],
            total: stats[:total]
          }
        end

        def extract_costume_skills(card_data)
          # Extract skill information from skillInfo
          skill_info = card_data[:skillInfo] || {}
          {
            auto_skills: skill_info[:autoSkills] || [],
            act_skills: skill_info[:actSkills] || [],
            climax_act: skill_info[:climaxAct],
            finish_act: skill_info[:finishAct]
          }
        end

        def extract_act_data(card_data)
          # Extract ACT-related data
          base = card_data[:base] || {}
          {
            attack_type: base[:attackType],
            role: base.dig(:roleIndex, :role),
            role_index: base.dig(:roleIndex, :index),
            cost: base[:cost]
          }
        end

        def extract_memoir_from_file(file)
          # Extract memoir data from Ruby constant file
          # This is a placeholder - implement based on actual memoir file structure
          filename = File.basename(file, '.rb')
          {
            karth_id: filename,
            name: { en: filename.humanize, ja: '' },
            description: { en: '', ja: '' },
            rarity: 3,
            max_level: 50,
            stats: {},
            effects: {},
            artwork_info: {}
          }
        end

        def extract_accessory_from_file(file)
          # Extract accessory data from Ruby constant file
          begin
            constant_name = File.basename(file, '.rb').upcase.gsub('-', '_')
            accessory_data = Object.const_get(constant_name)

            basic_info = accessory_data[:basicInfo] || {}
            skill_info = accessory_data[:skillInfo] || {}
            stat_info = accessory_data[:stat] || {}

            {
              karth_id: basic_info[:accID].to_s,
              name: basic_info[:name] || { en: "Unknown Accessory", ja: "" },
              description: { en: "", ja: "" },
              rarity: determine_accessory_rarity(basic_info[:accID]),
              max_level: 50, # Default max level
              category: determine_accessory_category(skill_info),
              stats: {
                hp: stat_info[:hp],
                atk: stat_info[:atk],
                pdef: stat_info[:pdef],
                mdef: stat_info[:mdef],
                agi: stat_info[:agi],
                dex: stat_info[:dex],
                cri: stat_info[:cri]
              },
              effects: extract_accessory_effects(skill_info),
              artwork_info: {
                icon_id: basic_info[:iconID],
                attribute: basic_info[:attribute],
                compatible_cards: basic_info[:cards] || []
              }
            }
          rescue => e
            puts "    ⚠️  Error parsing accessory file #{file}: #{e.message}"
            nil
          end
        end

        def determine_accessory_rarity(acc_id)
          # Determine rarity based on accessory ID patterns
          case acc_id
          when 1..10
            4 # High rarity for early accessories
          when 11..30
            3 # Medium rarity
          else
            2 # Default rarity
          end
        end

        def determine_accessory_category(skill_info)
          # Determine category based on skill information
          if skill_info[:skill]
            "active"
          elsif skill_info[:autoSkills]
            "passive"
          else
            "stat_boost"
          end
        end

        def extract_accessory_effects(skill_info)
          # Extract effects from skill information
          effects = {}

          if skill_info[:skill]
            skill = skill_info[:skill][:skillNormal]
            if skill
              effects[:active_skill] = {
                id: skill[:id],
                name: skill[:name],
                attribute: skill[:attribute],
                cost: skill[:cost],
                multiple: skill[:multiple]
              }
            end
          end

          if skill_info[:autoSkills]
            effects[:auto_skills] = skill_info[:autoSkills]
          end

          effects
        end

        def determine_school_from_name(name)
          # Determine school based on character name
          school_mapping = {
            'Karen' => 'Seisho Music Academy',
            'Claudine' => 'Seisho Music Academy',
            'Maya' => 'Seisho Music Academy',
            'Junna' => 'Seisho Music Academy',
            'Nana' => 'Seisho Music Academy',
            'Hikari' => 'Seisho Music Academy',
            'Mahiru' => 'Seisho Music Academy',
            'Futaba' => 'Seisho Music Academy',
            'Kaoruko' => 'Seisho Music Academy',
            'Michiru' => 'Siegfeld Institute of Music',
            'Yachiyo' => 'Rinmeikan Girls School',
            'Tamao' => 'Rinmeikan Girls School',
            'Ichie' => 'Rinmeikan Girls School',
            'Fumi' => 'Rinmeikan Girls School',
            'Rui' => 'Rinmeikan Girls School',
            'Yuyuko' => 'Rinmeikan Girls School'
          }
          
          school_mapping.each do |character, school|
            return school if name&.include?(character)
          end
          
          'Unknown'
        end
      end
    end
  end
end
