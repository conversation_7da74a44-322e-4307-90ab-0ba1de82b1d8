# frozen_string_literal: true

require_relative 'database_manager'
require_relative '../card_database'
require_relative '../stage_girl_database'

module CardBattle
  module Database
    # Manages migration from file-based storage to SQLite databases
    class MigrationManager
      class << self
        def migrate_all_data
          puts "🚀 Starting complete data migration..."
          
          DatabaseManager.initialize_databases
          
          migrate_stage_girls_and_costumes
          migrate_memoirs
          migrate_accessories
          
          puts "✅ All data migration completed successfully!"
          print_migration_summary
        end

        def migrate_stage_girls_and_costumes
          puts "\n📚 Migrating Stage Girls and Costumes..."
          
          stage_girls_db = DatabaseManager.stage_girls_db
          card_db = CardDatabase.new
          
          # Get all unique stage girls from card data
          stage_girls_map = {}
          costume_count = 0
          
          card_db.all_cards.each do |card|
            stage_girl_name = extract_stage_girl_name(card)
            school = extract_school(card)
            
            # Create or get stage girl
            unless stage_girls_map[stage_girl_name]
              stage_girl_id = stage_girls_db.create_stage_girl(
                name: stage_girl_name,
                school: school,
                base_stats: extract_base_stats(card)
              )
              stage_girls_map[stage_girl_name] = stage_girl_id
              puts "  ✓ Created stage girl: #{stage_girl_name} (#{school})"
            end
            
            # Create costume
            stage_girl_id = stage_girls_map[stage_girl_name]
            costume_id = stage_girls_db.create_costume(
              stage_girl_id: stage_girl_id,
              karth_id: card[:id].to_s,
              name: extract_costume_name(card),
              attribute: card[:attribute],
              rarity: card[:rarity],
              stats: extract_costume_stats(card),
              skills: extract_costume_skills(card),
              act_data: extract_act_data(card)
            )
            costume_count += 1
            
            if costume_count % 50 == 0
              puts "  📊 Migrated #{costume_count} costumes..."
            end
          end
          
          puts "  ✅ Migrated #{stage_girls_map.size} stage girls and #{costume_count} costumes"
        end

        def migrate_memoirs
          puts "\n💭 Migrating Memoirs..."
          
          memoirs_db = DatabaseManager.memoirs_db
          memoir_count = 0
          
          # Load memoir data from karth.top or existing files
          memoir_files = Dir.glob("lib/card_battle/memoirs/*.rb")
          
          memoir_files.each do |file|
            begin
              require file
              memoir_data = extract_memoir_from_file(file)
              
              if memoir_data
                memoir_id = memoirs_db.create_memoir(
                  karth_id: memoir_data[:karth_id],
                  name: memoir_data[:name],
                  description: memoir_data[:description],
                  rarity: memoir_data[:rarity],
                  max_level: memoir_data[:max_level],
                  stats: memoir_data[:stats],
                  effects: memoir_data[:effects],
                  artwork_info: memoir_data[:artwork_info]
                )
                
                # Create level progression data if available
                if memoir_data[:levels]
                  memoir_data[:levels].each do |level_data|
                    memoirs_db.create_memoir_level(
                      memoir_id: memoir_id,
                      level: level_data[:level],
                      required_exp: level_data[:required_exp],
                      stats_at_level: level_data[:stats]
                    )
                  end
                end
                
                # Create effect data if available
                if memoir_data[:effect_details]
                  memoir_data[:effect_details].each do |effect|
                    memoirs_db.create_memoir_effect(
                      memoir_id: memoir_id,
                      effect_type: effect[:type],
                      effect_name: effect[:name],
                      effect_description: effect[:description],
                      effect_values: effect[:values],
                      conditions: effect[:conditions]
                    )
                  end
                end
                
                memoir_count += 1
                puts "  ✓ Migrated memoir: #{memoir_data[:name][:en] || memoir_data[:karth_id]}"
              end
            rescue => e
              puts "  ⚠️  Error migrating memoir from #{file}: #{e.message}"
            end
          end
          
          puts "  ✅ Migrated #{memoir_count} memoirs"
        end

        def migrate_accessories
          puts "\n🎭 Migrating Accessories..."
          
          accessories_db = DatabaseManager.accessories_db
          accessory_count = 0
          
          # Load accessory data from existing files
          accessory_files = Dir.glob("lib/card_battle/accessories/*.rb")
          
          accessory_files.each do |file|
            begin
              require file
              accessory_data = extract_accessory_from_file(file)
              
              if accessory_data
                accessory_id = accessories_db.create_accessory(
                  karth_id: accessory_data[:karth_id],
                  name: accessory_data[:name],
                  description: accessory_data[:description],
                  rarity: accessory_data[:rarity],
                  max_level: accessory_data[:max_level],
                  category: accessory_data[:category],
                  stats: accessory_data[:stats],
                  effects: accessory_data[:effects],
                  artwork_info: accessory_data[:artwork_info]
                )
                
                # Create level progression data if available
                if accessory_data[:levels]
                  accessory_data[:levels].each do |level_data|
                    accessories_db.create_accessory_level(
                      accessory_id: accessory_id,
                      level: level_data[:level],
                      required_exp: level_data[:required_exp],
                      stats_at_level: level_data[:stats]
                    )
                  end
                end
                
                # Create effect data if available
                if accessory_data[:effect_details]
                  accessory_data[:effect_details].each do |effect|
                    accessories_db.create_accessory_effect(
                      accessory_id: accessory_id,
                      effect_type: effect[:type],
                      effect_name: effect[:name],
                      effect_description: effect[:description],
                      effect_values: effect[:values],
                      conditions: effect[:conditions],
                      stackable: effect[:stackable] || false
                    )
                  end
                end
                
                accessory_count += 1
                puts "  ✓ Migrated accessory: #{accessory_data[:name][:en] || accessory_data[:karth_id]}"
              end
            rescue => e
              puts "  ⚠️  Error migrating accessory from #{file}: #{e.message}"
            end
          end
          
          puts "  ✅ Migrated #{accessory_count} accessories"
        end

        def print_migration_summary
          puts "\n📊 Migration Summary:"
          
          stage_girls_stats = DatabaseManager.stage_girls_db.get_costume_statistics
          puts "  Stage Girls Database:"
          puts "    - Total costumes: #{stage_girls_stats['total_costumes']}"
          puts "    - Stage girls with costumes: #{stage_girls_stats['stage_girls_with_costumes']}"
          puts "    - 4★ costumes: #{stage_girls_stats['four_star_costumes']}"
          puts "    - 3★ costumes: #{stage_girls_stats['three_star_costumes']}"
          
          memoir_stats = DatabaseManager.memoirs_db.get_memoir_statistics
          puts "  Memoirs Database:"
          puts "    - Total memoirs: #{memoir_stats['total_memoirs']}"
          puts "    - 4★ memoirs: #{memoir_stats['four_star_memoirs']}"
          puts "    - 3★ memoirs: #{memoir_stats['three_star_memoirs']}"
          
          accessory_stats = DatabaseManager.accessories_db.get_accessory_statistics
          puts "  Accessories Database:"
          puts "    - Total accessories: #{accessory_stats['total_accessories']}"
          puts "    - 4★ accessories: #{accessory_stats['four_star_accessories']}"
          puts "    - 3★ accessories: #{accessory_stats['three_star_accessories']}"
          puts "    - Categories: #{accessory_stats['unique_categories']}"
        end

        private

        def extract_stage_girl_name(card)
          # Extract stage girl name from card data
          card[:name]&.split(' ')&.first || card[:character] || 'Unknown'
        end

        def extract_school(card)
          # Extract school from card data
          card[:school] || determine_school_from_name(card[:name]) || 'Unknown'
        end

        def extract_base_stats(card)
          # Extract base stats (these might be costume-specific, so we'll use defaults)
          {
            hp: card[:base_hp] || 1000,
            atk: card[:base_atk] || 100,
            def: card[:base_def] || 100,
            agi: card[:base_agi] || 100,
            dex: card[:base_dex] || 100
          }
        end

        def extract_costume_name(card)
          # Extract costume name with multilingual support
          {
            en: card[:name] || card[:title] || 'Unknown Costume',
            ja: card[:name_ja] || card[:title_ja] || ''
          }
        end

        def extract_costume_stats(card)
          # Extract all costume stats
          {
            hp: card[:hp],
            atk: card[:atk],
            def: card[:def],
            agi: card[:agi],
            dex: card[:dex],
            cost: card[:cost],
            max_level: card[:max_level]
          }
        end

        def extract_costume_skills(card)
          # Extract skill information
          {
            auto_skill: card[:auto_skill],
            act_skills: card[:act_skills] || [],
            climax_act: card[:climax_act],
            finish_act: card[:finish_act]
          }
        end

        def extract_act_data(card)
          # Extract ACT-related data
          {
            act_power: card[:act_power],
            act_type: card[:act_type],
            act_effects: card[:act_effects] || []
          }
        end

        def extract_memoir_from_file(file)
          # Extract memoir data from Ruby constant file
          # This is a placeholder - implement based on actual memoir file structure
          filename = File.basename(file, '.rb')
          {
            karth_id: filename,
            name: { en: filename.humanize, ja: '' },
            description: { en: '', ja: '' },
            rarity: 3,
            max_level: 50,
            stats: {},
            effects: {},
            artwork_info: {}
          }
        end

        def extract_accessory_from_file(file)
          # Extract accessory data from Ruby constant file
          # This is a placeholder - implement based on actual accessory file structure
          filename = File.basename(file, '.rb')
          {
            karth_id: filename,
            name: { en: filename.humanize, ja: '' },
            description: { en: '', ja: '' },
            rarity: 2,
            max_level: 30,
            category: 'general',
            stats: {},
            effects: {},
            artwork_info: {}
          }
        end

        def determine_school_from_name(name)
          # Determine school based on character name
          school_mapping = {
            'Karen' => 'Seisho Music Academy',
            'Claudine' => 'Seisho Music Academy',
            'Maya' => 'Seisho Music Academy',
            'Junna' => 'Seisho Music Academy',
            'Nana' => 'Seisho Music Academy',
            'Hikari' => 'Seisho Music Academy',
            'Mahiru' => 'Seisho Music Academy',
            'Futaba' => 'Seisho Music Academy',
            'Kaoruko' => 'Seisho Music Academy',
            'Michiru' => 'Siegfeld Institute of Music',
            'Yachiyo' => 'Rinmeikan Girls School',
            'Tamao' => 'Rinmeikan Girls School',
            'Ichie' => 'Rinmeikan Girls School',
            'Fumi' => 'Rinmeikan Girls School',
            'Rui' => 'Rinmeikan Girls School',
            'Yuyuko' => 'Rinmeikan Girls School'
          }
          
          school_mapping.each do |character, school|
            return school if name&.include?(character)
          end
          
          'Unknown'
        end
      end
    end
  end
end
