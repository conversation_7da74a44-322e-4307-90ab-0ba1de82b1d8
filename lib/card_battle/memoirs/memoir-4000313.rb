# frozen_string_literal: true

MEMOIR_4000313 = {
  basicInfo: {
    cardID: "4000313",
    rarity: 4,
    charas: [
      304
    ],
    name: {
      ja: "つかさと絶品スイーツ",
      en: "Exquisite Sweets with <PERSON><PERSON><PERSON><PERSON>",
      ko: "츠카사와 일품 디저트",
      zh_hant: "司與極品甜點"
    },
    profile: {
      ja: "「んん～、おいしい！」\nとっておきのおしゃれをして、カフェでスイーツに舌鼓を打つつかさ。絶品のスイーツが、日頃の疲れを癒してくれる。",
      en: "\"Hmm, so good!\"\n<PERSON><PERSON><PERSON><PERSON> enjoys a delightful dessert in a fancy cafe. Sweets ease her fatigue.",
      ko: "[으음~, 맛있어!]\n특별한 차림을 하고 카페에서 디저트를 먹으며 입맛을 다시는 츠카사. 맛있는 디저트가 평소의 피로를 풀어준다.",
      zh_hant: "「嗯～～好吃！」\n穿上珍藏服裝、打扮得漂漂亮亮的司正在咖啡廳裡滿足地品嚐美味甜點。極品甜點可療癒她平日累積的疲憊。"
    },
    released: {
      ja: 1664866800,
      ww: 1667286000
    }
  },
  stat: {
    total: 800,
    atk: 0,
    hp: 0,
    pdef: 800,
    mdef: 800
  },
  skill: {
    id: 20020,
    icon: 89,
    type: {
      ja: "開幕時",
      en: "At Start",
      ko: "개막",
      zh_hant: "開幕時"
    },
    params: [
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "キラめき回復([28, 31, 34, 37, 40])",
          en: "Brilliance Recovery ([28, 31, 34, 37, 40])",
          ko: "반짝임 회복([28, 31, 34, 37, 40])",
          zh_hant: "回復光芒（[28, 31, 34, 37, 40]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      3,
      3,
      3,
      3,
      2
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "味方の2番目のACT実行前",
          en: "Before 2nd Ally Act",
          ko: "아군의 두 번째 ACT 실행 전",
          zh_hant: "在我方發動第2個ACT前"
        },
        id: 1002
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        2,
        2,
        2,
        2,
        1
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 26,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]ターン",
          en: "[1, 1, 1, 1, 2] Turn(s)",
          ko: "[1, 1, 1, 1, 2]턴",
          zh_hant: "[1, 1, 1, 1, 2]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方の2番目のACT実行者",
          en: "2nd Acting Ally",
          ko: "아군의 두 번째 ACT 실행자",
          zh_hant: "第2名發動ACT的我方"
        },
        description: {
          ja: "継続マイナス効果耐性アップ([100, 100, 100, 100, 100])",
          en: "Cont. Neg. Effects Resistance Up ([100, 100, 100, 100, 100])",
          ko: "지속 마이너스 효과 저항 증가([100, 100, 100, 100, 100])",
          zh_hant: "提升持續性負面效果耐性（[100, 100, 100, 100, 100]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
