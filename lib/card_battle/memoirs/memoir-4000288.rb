# frozen_string_literal: true

MEMOIR_4000288 = {
  basicInfo: {
    cardID: "4000288",
    rarity: 4,
    charas: [
      101,
      301
    ],
    name: {
      ja: "運命の輪と月のレヴュー",
      en: "Revue of Wheel of Fortune and Moon",
      ko: "운명의 수레바퀴와 달의 레뷰",
      zh_hant: "命運之輪與月亮的Revue"
    },
    profile: {
      ja: "「わたし、かくよ！　ぜったい、ぜったい、やめたりしない！」\n運命の輪と月のレヴューに魅せられた、少女えるのペンは止まらない。\n未だ見ぬ戯曲の完成は、いつか、きっと――",
      en: "\"I'll write! I will never, ever give up!\"\nCharmed by the Revue of Wheel of Fortune and Moon, young <PERSON>'s pen doesn't stop. One day surely, she seeks to finish the play that hasn't taken form yet.",
      ko: "[나, 쓸게! 절대, 절대 그만두지 않을 거야!]\n운명의 수레바퀴와 달의 레뷰에 매료된 소녀 엘의 펜은 멈추지 않는다.\n아직 보지 못 한 희곡의 완성은 언젠가, 반드시――",
      zh_hant: "「我會寫的！我絕對、絕對不會放棄！」\n在見到命運之輪與月亮的Revue後，少女艾露手不停揮。\n總有一天，她一定能完成前所未聞的戲碼――"
    },
    released: {
      ja: 1658674800,
      ww: 1660680000
    }
  },
  stat: {
    total: 900,
    atk: 500,
    hp: 0,
    pdef: 0,
    mdef: 0
  },
  skill: {
    id: 10080,
    icon: 20,
    type: {
      ja: "永続",
      en: "Passive",
      ko: "영구",
      zh_hant: "永續"
    },
    params: [
      {
        icon: 20,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "クリティカル率アップ([14, 15, 17, 18, 20])",
          en: "Dexterity Up ([14, 15, 17, 18, 20])",
          ko: "크리티컬 확률 증가([14, 15, 17, 18, 20])",
          zh_hant: "提升會心率（[14, 15, 17, 18, 20]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      7,
      7,
      7,
      7,
      6
    ],
    attribute: 7,
    execution: {
      executeTiming: {
        description: {
          ja: "敵の1番目のACT実行前",
          en: "Before 1st Enemy Act",
          ko: "적의 첫 번째 ACT 실행 전",
          zh_hant: "在敵方發動第1個ACT前"
        },
        id: 2001
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        5,
        5,
        5,
        5,
        4
      ],
      recastTurns: [
        5,
        5,
        5,
        5,
        4
      ]
    },
    params: [
      {
        icon: 1,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "敵役の1番目のACT実行者",
          en: "1st Acting Enemy",
          ko: "적의 첫 번째 ACT 실행자",
          zh_hant: "最先發動ACT的敵方"
        },
        description: {
          ja: "夢属性攻撃(威力[240, 240, 280, 320, 360])",
          en: "Dream Dmg ([240, 240, 280, 320, 360])",
          ko: "꿈 속성 공격(위력 [240, 240, 280, 320, 360])",
          zh_hant: "夢屬性攻擊（威力[240, 240, 280, 320, 360]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
