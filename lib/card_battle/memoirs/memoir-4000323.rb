# frozen_string_literal: true

MEMOIR_4000323 = {
  basicInfo: {
    cardID: "4000323",
    rarity: 4,
    charas: [
      101,
      202
    ],
    name: {
      ja: "熱唱☆打ち上げパーティー",
      en: "Enthusiastic Singing☆ Cast Party",
      ko: "열창☆뒤풀이 파티",
      zh_hant: "熱唱☆慶功派對"
    },
    profile: {
      ja: "「華恋ちゃん、初めてなのに振り付けすっごい上手～☆」\n合同プログラムの打ち上げパーティーはみんなでカラオケへ。\nアイドル顔負けのパフォーマンスで大盛り上がり！",
      en: "\"<PERSON><PERSON><PERSON><PERSON>, your choreography is amazing for a first timer!☆\"\nThe cast party for the joint program is held at a karaoke. <PERSON><PERSON><PERSON><PERSON>'s performance puts even an idol to shame!",
      ko: "[카렌, 처음인데 안무를 엄청 잘 추네~☆]\n합동 프로그램 뒤풀이 파티는 다 같이 노래방에서.\n아이돌 수준의 퍼포먼스로 대성황!",
      zh_hant: "「華戀明明才第一次跳，卻把舞步都跳得非常到位呢～☆」\n大家正在卡拉OK包廂裡進行共同演出企劃的慶功派對。\n那不輸偶像的表演讓現場氣氛相當熱絡！"
    },
    released: {
      ja: 1665990000,
      ww: 1668322800
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 20020,
    icon: 89,
    type: {
      ja: "開幕時",
      en: "At Start",
      ko: "개막",
      zh_hant: "開幕時"
    },
    params: [
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "キラめき回復([28, 31, 34, 37, 40])",
          en: "Brilliance Recovery ([28, 31, 34, 37, 40])",
          ko: "반짝임 회복([28, 31, 34, 37, 40])",
          zh_hant: "回復光芒（[28, 31, 34, 37, 40]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      5,
      5,
      5,
      5,
      4
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン開始時",
          en: "Start of Turn",
          ko: "턴 시작 시",
          zh_hant: "回合開始時"
        },
        id: 1
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        4,
        4,
        4,
        4,
        3
      ],
      recastTurns: [
        4,
        4,
        4,
        4,
        3
      ]
    },
    params: [
      {
        icon: 265,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "後ろから2体の敵役",
          en: "2 Rear Enemies",
          ko: "뒤에서 2명의 적",
          zh_hant: "後面2名敵方"
        },
        description: {
          ja: "起死回生減少([1, 1, 1, 1, 2])",
          en: "Revive Reduction ([1, 1, 1, 1, 2])",
          ko: "기사회생 감소([1, 1, 1, 1, 2])",
          zh_hant: "減少起死回生（[1, 1, 1, 1, 2]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
