# frozen_string_literal: true

MEMOIR_4000292 = {
  basicInfo: {
    cardID: "4000292",
    rarity: 4,
    charas: [
      205,
      405
    ],
    name: {
      ja: "やちよのファッション指南",
      en: "<PERSON><PERSON><PERSON>'s Fashion Manual",
      ko: "야치요의 패션 지도",
      zh_hant: "八千代的時尚指南"
    },
    profile: {
      ja: "「やちよさん～本当に似合ってます？」\n「超かわいいじゃーん。ゆゆっち、後で髪も結ってあげるね～。」\nやちよに服を見繕ってもらい照れながらも、新しい自分との出会いを楽しんでいるゆゆ子だった。",
      en: "\"Ya<PERSON><PERSON>-san, are you sure I look okay in this?\"\n\"Super adorable! Yuyu<PERSON>, I'll do your hair for you later too.\"\n<PERSON><PERSON><PERSON><PERSON> feels embarrassed to have <PERSON><PERSON><PERSON> pick out clothes for her, but she's enjoying this new side of herself.",
      ko: "[야치요~ 정말로 잘 어울리나요?]\n[정말 예뻐. 유유치, 나중에 머리도 묶어줄게~.]\n야치요가 코디해 주는 것에 쑥스러워하면서도 새로운 자신과의 만남을 즐기는 유유코였다.",
      zh_hant: "「八千代同學～我真的適合這樣穿嗎？」\n「超級可愛耶～悠悠，等一下我來替妳綁髮吧～」\n雖然讓八千代替自己挑衣服有點不好意思，但悠悠子同時也很高興能遇到新的自己。"
    },
    released: {
      ja: 1659596400,
      ww: 1662015600
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 20020,
    icon: 89,
    type: {
      ja: "開幕時",
      en: "At Start",
      ko: "개막",
      zh_hant: "開幕時"
    },
    params: [
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "キラめき回復([28, 31, 34, 37, 40])",
          en: "Brilliance Recovery ([28, 31, 34, 37, 40])",
          ko: "반짝임 회복([28, 31, 34, 37, 40])",
          zh_hant: "回復光芒（[28, 31, 34, 37, 40]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "敵の1番目のACT実行前",
          en: "Before 1st Enemy Act",
          ko: "적의 첫 번째 ACT 실행 전",
          zh_hant: "在敵方發動第1個ACT前"
        },
        id: 2001
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        3,
        3,
        3,
        3,
        2
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 236,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]回",
          en: "[1, 1, 1, 1, 2] Time(s)",
          ko: "[1, 1, 1, 1, 2]회",
          zh_hant: "[1, 1, 1, 1, 2]次"
        },
        accuracy: 100,
        target: {
          ja: "敵役の1番目のACT実行者",
          en: "1st Acting Enemy",
          ko: "적의 첫 번째 ACT 실행자",
          zh_hant: "最先發動ACT的敵方"
        },
        description: {
          ja: "錯乱",
          en: "Daze",
          ko: "착란",
          zh_hant: "錯亂"
        },
        descriptionExtra: nil
      }
    ]
  }
}
