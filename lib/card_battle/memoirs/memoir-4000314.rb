# frozen_string_literal: true

MEMOIR_4000314 = {
  basicInfo: {
    cardID: "4000314",
    rarity: 4,
    charas: [
      503
    ],
    name: {
      ja: "黄昏のホーム",
      en: "Twilight Platform",
      ko: "해질녘의 승강장",
      zh_hant: "黃昏的月台"
    },
    profile: {
      ja: "思いがけず時間ができて、駅のホームで読書に耽る氷雨。\n黄昏時、秋の風がやさしく通り抜けていく。",
      en: "<PERSON><PERSON> had some unexpected free time and absorbs herself in her book on the station platform.\nAn autumn breeze blows gently at dusk.",
      ko: "생각지도 못한 시간이 생겨서 역 승강장에서 독서에 몰두하고 있는 히사메.\n해질녘, 가을 바람이 살랑살랑 지나간다.",
      zh_hant: "黃昏時分，秋風輕輕地拂過在車站月台上的冰雨。\n遇到突如其來的空檔，此刻她正沉醉於書海之中。"
    },
    released: {
      ja: 1664866800,
      ww: 1667286000
    }
  },
  stat: {
    total: 800,
    atk: 0,
    hp: 0,
    pdef: 800,
    mdef: 800
  },
  skill: {
    id: 20020,
    icon: 89,
    type: {
      ja: "開幕時",
      en: "At Start",
      ko: "개막",
      zh_hant: "開幕時"
    },
    params: [
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "キラめき回復([28, 31, 34, 37, 40])",
          en: "Brilliance Recovery ([28, 31, 34, 37, 40])",
          ko: "반짝임 회복([28, 31, 34, 37, 40])",
          zh_hant: "回復光芒（[28, 31, 34, 37, 40]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      3,
      3,
      3,
      3,
      2
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "味方の3番目のACT実行前",
          en: "Before 3rd Ally Act",
          ko: "아군의 세 번째 ACT 실행 전",
          zh_hant: "在我方發動第3個ACT前"
        },
        id: 1003
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        2,
        2,
        2,
        2,
        1
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 250,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]ターン",
          en: "[1, 1, 1, 1, 2] Turn(s)",
          ko: "[1, 1, 1, 1, 2]턴",
          zh_hant: "[1, 1, 1, 1, 2]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方の3番目のACT実行者",
          en: "3rd Acting Ally",
          ko: "아군의 세 번째 ACT 실행자",
          zh_hant: "第3名發動ACT的我方"
        },
        description: {
          ja: "回数マイナス効果耐性アップ([100, 100, 100, 100, 100])",
          en: "Count. Neg. Effects Resistance Up ([100, 100, 100, 100, 100])",
          ko: "횟수 마이너스 효과 저항 증가([100, 100, 100, 100, 100])",
          zh_hant: "提升次數性負面效果耐性（[100, 100, 100, 100, 100]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
