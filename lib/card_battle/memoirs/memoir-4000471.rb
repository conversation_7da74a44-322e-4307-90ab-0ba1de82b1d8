# frozen_string_literal: true

MEMOIR_4000471 = {
  basicInfo: {
    cardID: "4000471",
    rarity: 4,
    charas: [
      408,
      410
    ],
    name: {
      ja: "あなたのキラめき",
      en: "Your Brilliance",
      ko: "너의 반짝임",
      zh_hant: "妳的光芒"
    },
    profile: {
      ja: "「あなたのキラめきを教えてあげる！」\n同じ苦しみを抱えるものとして、クイナにレヴューを挑む良子。\nむきだしの本音をぶつける良子に、クイナの心は――",
      en: "\"I'll show you your brilliance!\"\n<PERSON><PERSON><PERSON> challenges <PERSON><PERSON> to a revue as someone who is suffering the same pain as she is. When <PERSON><PERSON><PERSON> lays out her emotions, <PERSON><PERSON>'s heart is...",
      ko: "[너의 반짝임을 가르쳐 줄게!]\n같은 아픔을 가진 사람으로서 쿠이나에게 레뷰를 신청하는 료코.\n진심을 숨김없이 드러내는 료코를 보며 쿠이나의 마음은──",
      zh_hant: "「妳的光芒就由我來告訴妳！」\n察覺彼此懷抱著相同苦楚後，良子向玖伊奈發起了Revue。\n良子那毫無保留的話語，狠狠敲擊著玖伊奈的心――"
    },
    released: {
      ja: 1706252400,
      ww: 1708671600
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 23151,
    icon: 0,
    type: {
      ja: "開幕時",
      en: "At Start",
      ko: "개막",
      zh_hant: "開幕時"
    },
    params: [
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "キラめき回復([22, 24, 27, 29, 32])",
          en: "Brilliance Recovery ([22, 24, 27, 29, 32])",
          ko: "반짝임 회복([22, 24, 27, 29, 32])",
          zh_hant: "回復光芒（[22, 24, 27, 29, 32]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 347,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]回",
          en: "[1, 1, 1, 1, 2] Time(s)",
          ko: "[1, 1, 1, 1, 2]회",
          zh_hant: "[1, 1, 1, 1, 2]次"
        },
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "上位不屈",
          en: "Greater Fortitude",
          ko: "상위 불굴",
          zh_hant: "高級不屈"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン開始時",
          en: "Start of Turn",
          ko: "턴 시작 시",
          zh_hant: "回合開始時"
        },
        id: 1
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        2,
        2,
        2,
        2,
        1
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 379,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]ターン",
          en: "[1, 1, 1, 1, 2] Turn(s)",
          ko: "[1, 1, 1, 1, 2]턴",
          zh_hant: "[1, 1, 1, 1, 2]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方全体",
          en: "All Allies",
          ko: "아군 전체",
          zh_hant: "所有我方"
        },
        description: {
          ja: "上位すばやさアップ([10, 10, 11, 12, 15])",
          en: "Greater Agility Up ([10, 10, 11, 12, 15])",
          ko: "상위 민첩 증가([10, 10, 11, 12, 15])",
          zh_hant: "[高級]提升敏捷（[10, 10, 11, 12, 15]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
