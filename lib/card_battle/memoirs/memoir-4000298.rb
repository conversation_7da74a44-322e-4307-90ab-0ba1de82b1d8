# frozen_string_literal: true

MEMOIR_4000298 = {
  basicInfo: {
    cardID: "4000298",
    rarity: 4,
    charas: nil,
    name: {
      ja: "熟れたトマト",
      en: "<PERSON><PERSON><PERSON>",
      ko: "잘 익은 토마토",
      zh_hant: "成熟的蕃茄"
    },
    profile: {
      ja: "――『劇場版 少女☆歌劇 レヴュースタァライト』より",
      en: "From [Revue Starlight: The Movie]",
      ko: "――[극장판 소녀☆가극 레뷰 스타라이트]에서",
      zh_hant: "——取自『劇場版 少女☆歌劇 Revue Starlight』"
    },
    released: {
      ja: 1661320800,
      ww: 1662616800
    }
  },
  stat: {
    total: 10500,
    atk: 2500,
    hp: 40000,
    pdef: 2000,
    mdef: 2000
  },
  skill: {
    id: 28104,
    icon: 89,
    type: {
      ja: "開幕時",
      en: "At Start",
      ko: "개막",
      zh_hant: "開幕時"
    },
    params: [
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "キラめき回復([22, 24, 27, 29, 32])",
          en: "Brilliance Recovery ([22, 24, 27, 29, 32])",
          ko: "반짝임 회복([22, 24, 27, 29, 32])",
          zh_hant: "回復光芒（[22, 24, 27, 29, 32]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 20,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[6, 6, 6, 6, 6]ターン",
          en: "[6, 6, 6, 6, 6] Turn(s)",
          ko: "[6, 6, 6, 6, 6]턴",
          zh_hant: "[6, 6, 6, 6, 6]回合"
        },
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "クリティカル率アップ([11, 12, 13, 14, 16])",
          en: "Dexterity Up ([11, 12, 13, 14, 16])",
          ko: "크리티컬 확률 증가([11, 12, 13, 14, 16])",
          zh_hant: "提升會心率（[11, 12, 13, 14, 16]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 39,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[6, 6, 6, 6, 6]ターン",
          en: "[6, 6, 6, 6, 6] Turn(s)",
          ko: "[6, 6, 6, 6, 6]턴",
          zh_hant: "[6, 6, 6, 6, 6]回合"
        },
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "有利属性ダメージアップ([8, 9, 10, 11, 12])",
          en: "Effective Element Dmg Up ([8, 9, 10, 11, 12])",
          ko: "유리한 속성 대미지 증가([8, 9, 10, 11, 12])",
          zh_hant: "提升有利屬性傷害（[8, 9, 10, 11, 12]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      3,
      3,
      3,
      3,
      2
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン開始時",
          en: "Start of Turn",
          ko: "턴 시작 시",
          zh_hant: "回合開始時"
        },
        id: 1
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        2,
        2,
        2,
        2,
        1
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 111,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]ターン",
          en: "[1, 1, 1, 1, 2] Turn(s)",
          ko: "[1, 1, 1, 1, 2]턴",
          zh_hant: "[1, 1, 1, 1, 2]回合"
        },
        accuracy: 100,
        target: {
          ja: "前から5体の味方",
          en: "5 Front Allies",
          ko: "앞에서 5명의 아군",
          zh_hant: "前面5名我方"
        },
        description: {
          ja: "与ダメージアップ([10, 10, 11, 13, 15])",
          en: "Dmg Up ([10, 10, 11, 13, 15])",
          ko: "가하는 대미지 증가([10, 10, 11, 13, 15])",
          zh_hant: "提升造成的傷害（[10, 10, 11, 13, 15]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
