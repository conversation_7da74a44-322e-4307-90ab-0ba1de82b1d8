# frozen_string_literal: true

MEMOIR_4000397 = {
  basicInfo: {
    cardID: "4000397",
    rarity: 4,
    charas: [
      406,
      407
    ],
    name: {
      ja: "Rebellion─反逆の物語─",
      en: "Rebellion - Story of Treachery",
      ko: "Rebellion ─반역의 이야기─",
      zh_hant: "Rebellion─反叛的故事─"
    },
    profile: {
      ja: "「詩呂、私は舞台が好き――この気持ち、あなたにぶつけるわ」\nステラの想いをのせた切っ先は、まっすぐ詩呂へと向かう。\n本音を曝け出したふたりのレヴューの結末は――",
      en: "\"<PERSON><PERSON>, I love the stage—I'm going to share these feelings with you.\"\nFilled with <PERSON>'s feelings, the tip of her blade is aimed at <PERSON><PERSON>. What will be the outcome of their revue?",
      ko: "[시로, 나는 무대가 좋아──이 마음, 너한테 전하겠어]\n스텔라의 마음을 담은 칼끝은 똑바로 시로에게 향한다.\n본심을 드러낸 두 사람의 레뷰의 결말은──",
      zh_hant: "「詩呂，我喜歡舞台――我會將這份情感傳遞給妳」\n史黛拉將自己的想法寄託在劍尖，筆直地刺向詩呂。\n在彼此吐露心聲後，Revue的結果究竟為何――"
    },
    released: {
      ja: 1689404400,
      ww: 1691823600
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 12167,
    icon: 29,
    type: {
      ja: "永続",
      en: "Passive",
      ko: "영구",
      zh_hant: "永續"
    },
    params: [
      {
        icon: 29,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "毎ターンキラめき回復([10, 10, 10, 10, 20])",
          en: "Brilliance Regen ([10, 10, 10, 10, 20])",
          ko: "매 턴마다 반짝임 회복([10, 10, 10, 10, 20])",
          zh_hant: "每回合回復光芒（[10, 10, 10, 10, 20]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 14,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "すばやさアップ([10, 10, 10, 10, 12])",
          en: "Agility Up ([10, 10, 10, 10, 12])",
          ko: "민첩 증가([10, 10, 10, 10, 12])",
          zh_hant: "提升敏捷（[10, 10, 10, 10, 12]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン終了時",
          en: "End of Turn",
          ko: "턴 종료 시",
          zh_hant: "回合結束時"
        },
        id: 2
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        2,
        2,
        2,
        2,
        1
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 157,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[2, 2, 2, 2, 3]ターン",
          en: "[2, 2, 2, 2, 3] Turn(s)",
          ko: "[2, 2, 2, 2, 3]턴",
          zh_hant: "[2, 2, 2, 2, 3]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方全体",
          en: "All Allies",
          ko: "아군 전체",
          zh_hant: "所有我方"
        },
        description: {
          ja: "AP減少",
          en: "AP Down",
          ko: "AP 감소",
          zh_hant: "AP減少"
        },
        descriptionExtra: nil
      }
    ]
  }
}
