# frozen_string_literal: true

MEMOIR_4000394 = {
  basicInfo: {
    cardID: "4000394",
    rarity: 4,
    charas: [
      204
    ],
    name: {
      ja: "最強の音楽 チュチュ＆塁",
      en: "Strongest Music CHU² & Rui",
      ko: "최강의 음악 츄츄 & 루이",
      zh_hant: "最強音樂 CHU²&壘"
    },
    profile: {
      ja: "最強の音楽を奏でる最強のバンド・RAISE A SUILENと舞台少女が夢の共演！\n正反対のようで息はピッタリ！？なチュチュと塁のDJタイム、Start！",
      en: "A dream collaboration between RAISE A SUILEN, the most powerful band playing the most powerful music, and Stage Girls! They may seem to be opposites, but they are in perfect sync! It's CHU² and <PERSON><PERSON>'s DJ time!",
      ko: "최강의 음악을 연주하는 최강의 밴드 RAISE A SUILEN과 무대소녀가 꿈의 합동 공연!\n정반대인 것 같지만 환상의 호흡?! 츄츄와 루이의 DJ 타임, 스타트!",
      zh_hant: "演奏出最強音樂的最強樂團——RAISE A SUILEN與舞台少女的夢幻共演！\nCHU²與壘的DJ TIME即將開場！看似南轅北轍的兩人卻意外地默契十足！？"
    },
    released: {
      ja: 1686466800,
      ww: 1688972400
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 12167,
    icon: 29,
    type: {
      ja: "永続",
      en: "Passive",
      ko: "영구",
      zh_hant: "永續"
    },
    params: [
      {
        icon: 29,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "毎ターンキラめき回復([10, 10, 10, 10, 20])",
          en: "Brilliance Regen ([10, 10, 10, 10, 20])",
          ko: "매 턴마다 반짝임 회복([10, 10, 10, 10, 20])",
          zh_hant: "每回合回復光芒（[10, 10, 10, 10, 20]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 14,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "すばやさアップ([10, 10, 10, 10, 12])",
          en: "Agility Up ([10, 10, 10, 10, 12])",
          ko: "민첩 증가([10, 10, 10, 10, 12])",
          zh_hant: "提升敏捷（[10, 10, 10, 10, 12]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "味方の1番目のACT実行前",
          en: "Before 1st Ally Act",
          ko: "아군의 첫 번째 ACT 실행 전",
          zh_hant: "在我方發動第1個ACT前"
        },
        id: 1001
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        3,
        3,
        3,
        3,
        2
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 300,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "味方の1番目のACT実行者",
          en: "1st Acting Ally",
          ko: "아군의 첫 번째 ACT 실행자",
          zh_hant: "最先發動ACT的我方"
        },
        description: {
          ja: "回数マイナス効果解除",
          en: "Dispel Count. Neg. Effects",
          ko: "횟수 마이너스 효과 해제",
          zh_hant: "解除次數性負面效果"
        },
        descriptionExtra: nil
      },
      {
        icon: 250,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]ターン",
          en: "[1, 1, 1, 1, 2] Turn(s)",
          ko: "[1, 1, 1, 1, 2]턴",
          zh_hant: "[1, 1, 1, 1, 2]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方の1番目のACT実行者",
          en: "1st Acting Ally",
          ko: "아군의 첫 번째 ACT 실행자",
          zh_hant: "最先發動ACT的我方"
        },
        description: {
          ja: "回数マイナス効果耐性アップ([50, 50, 60, 80, 100])",
          en: "Count. Neg. Effects Resistance Up ([50, 50, 60, 80, 100])",
          ko: "횟수 마이너스 효과 저항 증가([50, 50, 60, 80, 100])",
          zh_hant: "提升次數性負面效果耐性（[50, 50, 60, 80, 100]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 156,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 1]ターン",
          en: "[1, 1, 1, 1, 1] Turn(s)",
          ko: "[1, 1, 1, 1, 1]턴",
          zh_hant: "[1, 1, 1, 1, 1]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方の1番目のACT実行者",
          en: "1st Acting Ally",
          ko: "아군의 첫 번째 ACT 실행자",
          zh_hant: "最先發動ACT的我方"
        },
        description: {
          ja: "無敵",
          en: "Invincible",
          ko: "무적",
          zh_hant: "無敵"
        },
        descriptionExtra: nil
      }
    ]
  }
}
