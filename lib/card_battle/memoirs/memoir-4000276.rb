# frozen_string_literal: true

MEMOIR_4000276 = {
  basicInfo: {
    cardID: "4000276",
    rarity: 4,
    charas: [
      203,
      303
    ],
    name: {
      ja: "『カルメン』",
      en: "\"<PERSON>\"",
      ko: "[카르멘]",
      zh_hant: "『卡門』"
    },
    profile: {
      ja: "「俺から離れるのか、カルメン！？」\nドン・ホセの情念の叫びは、自由を愛するカルメンに響くのか――？",
      en: "\"Are you leaving me, <PERSON>!?\"\nWill the freedom-loving <PERSON> respond to <PERSON>'s emotional plea?",
      ko: "[나에게서 멀어지려는 거야? 카르멘?!]\n돈 호세의 정념에 찬 외침은 자유를 사랑하는 카르멘의 마음을 울릴 수 있을 것인가――?",
      zh_hant: "「妳要從我身邊離去嗎？卡門！？」\n唐・荷塞的深情呼喚，是否能喚回熱愛自由的卡門――？"
    },
    released: {
      ja: 1655017200,
      ww: 1658300400
    }
  },
  stat: {
    total: 800,
    atk: 0,
    hp: 0,
    pdef: 800,
    mdef: 800
  },
  skill: {
    id: 12188,
    icon: 29,
    type: {
      ja: "永続",
      en: "Passive",
      ko: "영구",
      zh_hant: "永續"
    },
    params: [
      {
        icon: 29,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "毎ターンキラめき回復([10, 12, 14, 16, 20])",
          en: "Brilliance Regen ([10, 12, 14, 16, 20])",
          ko: "매 턴마다 반짝임 회복([10, 12, 14, 16, 20])",
          zh_hant: "每回合回復光芒（[10, 12, 14, 16, 20]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン終了時",
          en: "End of Turn",
          ko: "턴 종료 시",
          zh_hant: "回合結束時"
        },
        id: 2
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        3,
        3,
        3,
        3,
        2
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 29,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[2, 2, 2, 2, 3]ターン",
          en: "[2, 2, 2, 2, 3] Turn(s)",
          ko: "[2, 2, 2, 2, 3]턴",
          zh_hant: "[2, 2, 2, 2, 3]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方全体",
          en: "All Allies",
          ko: "아군 전체",
          zh_hant: "所有我方"
        },
        description: {
          ja: "毎ターンキラめき回復([20, 20, 21, 23, 25])",
          en: "Brilliance Regen ([20, 20, 21, 23, 25])",
          ko: "매 턴마다 반짝임 회복([20, 20, 21, 23, 25])",
          zh_hant: "每回合回復光芒（[20, 20, 21, 23, 25]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
