# frozen_string_literal: true

MEMOIR_4000324 = {
  basicInfo: {
    cardID: "4000324",
    rarity: 4,
    charas: [
      106,
      107
    ],
    name: {
      ja: "ルームメイトの支え",
      en: "Roommate's Help",
      ko: "룸메이트의 도움",
      zh_hant: "室友的扶持"
    },
    profile: {
      ja: "寮に戻った自由時間も、やっぱり舞台のことで話題は持ち切り。稽古中のジークフリートの演技を見せるななに、的確な感想を告げる純那。\n別チームでの合同プログラムでも、２人は支え合い舞台を創る。",
      en: "Even in their spare time at the dorm, the two talk about the stage. <PERSON> performs her role in <PERSON><PERSON><PERSON>, while <PERSON><PERSON> offers her advice. Even though they belong to different teams, the two of them help each other for the stage.",
      ko: "기숙사에서의 자유시간도 역시 무대 관련 화제로 왁자지껄. 연습 중인 지크프리트 연기를 보여주는 나나에게 적절한 감상평을 말해주는 준나.\n다른 팀에서의 합동 프로그램에서도 둘은 서로 도우며 무대를 만든다.",
      zh_hant: "兩人即使回到宿舍，仍會在自由時間中討論有關舞台的話題。看到奈奈在排演時所詮釋的齊格菲後，純那向她說出了內心的感想。\n即使身在共同演出企劃的不同組別，兩人仍互相扶持創造舞台。"
    },
    released: {
      ja: 1666245600,
      ww: 1668668400
    }
  },
  stat: {
    total: 800,
    atk: 0,
    hp: 0,
    pdef: 800,
    mdef: 800
  },
  skill: {
    id: 12188,
    icon: 29,
    type: {
      ja: "永続",
      en: "Passive",
      ko: "영구",
      zh_hant: "永續"
    },
    params: [
      {
        icon: 29,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "毎ターンキラめき回復([10, 12, 14, 16, 20])",
          en: "Brilliance Regen ([10, 12, 14, 16, 20])",
          ko: "매 턴마다 반짝임 회복([10, 12, 14, 16, 20])",
          zh_hant: "每回合回復光芒（[10, 12, 14, 16, 20]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "味方の2番目のACT実行前",
          en: "Before 2nd Ally Act",
          ko: "아군의 두 번째 ACT 실행 전",
          zh_hant: "在我方發動第2個ACT前"
        },
        id: 1002
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        3,
        3,
        3,
        3,
        2
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 29,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[2, 2, 2, 2, 3]ターン",
          en: "[2, 2, 2, 2, 3] Turn(s)",
          ko: "[2, 2, 2, 2, 3]턴",
          zh_hant: "[2, 2, 2, 2, 3]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方全体",
          en: "All Allies",
          ko: "아군 전체",
          zh_hant: "所有我方"
        },
        description: {
          ja: "毎ターンキラめき回復([20, 20, 21, 23, 25])",
          en: "Brilliance Regen ([20, 20, 21, 23, 25])",
          ko: "매 턴마다 반짝임 회복([20, 20, 21, 23, 25])",
          zh_hant: "每回合回復光芒（[20, 20, 21, 23, 25]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
