# frozen_string_literal: true

MEMOIR_4000368 = {
  basicInfo: {
    cardID: "4000368",
    rarity: 4,
    charas: [
      404
    ],
    name: {
      ja: "One special day 栞",
      en: "One Special Day Shiori",
      ko: "One special day 시오리",
      zh_hant: "One special day 栞"
    },
    profile: {
      ja: "特別な日には、特別な衣裳に特別の笑顔。そして、いつものみんなと。",
      en: "A special costume and a special smile for a special occasion with her dear ones.",
      ko: "특별한 날에는 특별한 의상에 특별한 미소. 그리고, 평소와 같은 모두와 함께.",
      zh_hant: "在這個特別的日子裡要換上特別的服裝，並帶著特別的笑容與大家一同度過。"
    },
    released: {
      ja: 1687273200,
      ww: 1687273200
    }
  },
  stat: {
    total: 2023,
    atk: 770,
    hp: 3370,
    pdef: 300,
    mdef: 300
  },
  skill: {
    id: 22964,
    icon: 0,
    type: {
      ja: "開幕時",
      en: "At Start",
      ko: "개막",
      zh_hant: "開幕時"
    },
    params: [
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "キラめき回復([22, 24, 27, 29, 32])",
          en: "Brilliance Recovery ([22, 24, 27, 29, 32])",
          ko: "반짝임 회복([22, 24, 27, 29, 32])",
          zh_hant: "回復光芒（[22, 24, 27, 29, 32]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 22,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[6, 6, 6, 6, 6]ターン",
          en: "[6, 6, 6, 6, 6] Turn(s)",
          ko: "[6, 6, 6, 6, 6]턴",
          zh_hant: "[6, 6, 6, 6, 6]回合"
        },
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "クリティカル威力アップ([21, 23, 25, 27, 30])",
          en: "Critical Up ([21, 23, 25, 27, 30])",
          ko: "크리티컬 위력 증가([21, 23, 25, 27, 30])",
          zh_hant: "提升會心威力（[21, 23, 25, 27, 30]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 39,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[6, 6, 6, 6, 6]ターン",
          en: "[6, 6, 6, 6, 6] Turn(s)",
          ko: "[6, 6, 6, 6, 6]턴",
          zh_hant: "[6, 6, 6, 6, 6]回合"
        },
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "有利属性ダメージアップ([8, 9, 10, 11, 12])",
          en: "Effective Element Dmg Up ([8, 9, 10, 11, 12])",
          ko: "유리한 속성 대미지 증가([8, 9, 10, 11, 12])",
          zh_hant: "提升有利屬性傷害（[8, 9, 10, 11, 12]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      3,
      3,
      3,
      3,
      2
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン開始時",
          en: "Start of Turn",
          ko: "턴 시작 시",
          zh_hant: "回合開始時"
        },
        id: 1
      },
      executeLimitCounts: [
        2,
        2,
        2,
        2,
        3
      ],
      firstExecutableTurns: [
        2,
        2,
        2,
        2,
        1
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 39,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[2, 2, 2, 2, 3]ターン",
          en: "[2, 2, 2, 2, 3] Turn(s)",
          ko: "[2, 2, 2, 2, 3]턴",
          zh_hant: "[2, 2, 2, 2, 3]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方の月属性",
          en: "Moon Allies",
          ko: "아군 달 속성",
          zh_hant: "月屬性的我方"
        },
        description: {
          ja: "有利属性ダメージアップ([30, 30, 35, 40, 50])",
          en: "Effective Element Dmg Up ([30, 30, 35, 40, 50])",
          ko: "유리한 속성 대미지 증가([30, 30, 35, 40, 50])",
          zh_hant: "提升有利屬性傷害（[30, 30, 35, 40, 50]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
