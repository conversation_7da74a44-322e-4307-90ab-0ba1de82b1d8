# frozen_string_literal: true

MEMOIR_4000312 = {
  basicInfo: {
    cardID: "4000312",
    rarity: 4,
    charas: [
      409,
      410
    ],
    name: {
      ja: "共演の舞台",
      en: "Co-Star Stage",
      ko: "합동 공연 무대",
      zh_hant: "共演的舞台"
    },
    profile: {
      ja: "「舞台少女としての生存を賭けて──歌って、踊って、生き残りましょう」\nいずれ消えゆく運命を変えるため、みんくとクイナは地下舞台でのレヴューに挑む。",
      en: "\"Our lives as Stage Girls are on the line—let's sing, dance, and battle for survival.\"\nDestined to be forgotten, <PERSON><PERSON> and <PERSON><PERSON> resolve to battle in a Revue in the underground stage to change their fate.",
      ko: "[무대소녀로서의 생존을 걸고── 노래하고, 춤추고, 살아남자]\n언젠가 사라질 운명을 바꾸기 위해 밍크와 쿠이나는 지하 무대의 레뷰에 도전한다.",
      zh_hant: "「讓我們賭上作為舞台少女的生命——一起高歌、起舞並生存下去吧」\n為了改變自己終會消失的命運，明久與玖伊奈決定站上地下舞台挑戰Revue。"
    },
    released: {
      ja: 1663743600,
      ww: 1666854000
    }
  },
  stat: {
    total: 900,
    atk: 500,
    hp: 0,
    pdef: 0,
    mdef: 0
  },
  skill: {
    id: 10118,
    icon: 22,
    type: {
      ja: "永続",
      en: "Passive",
      ko: "영구",
      zh_hant: "永續"
    },
    params: [
      {
        icon: 22,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "クリティカル威力アップ([21, 23, 25, 27, 30])",
          en: "Critical Up ([21, 23, 25, 27, 30])",
          ko: "크리티컬 위력 증가([21, 23, 25, 27, 30])",
          zh_hant: "提升會心威力（[21, 23, 25, 27, 30]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 20,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "クリティカル率アップ([11, 12, 13, 14, 16])",
          en: "Dexterity Up ([11, 12, 13, 14, 16])",
          ko: "크리티컬 확률 증가([11, 12, 13, 14, 16])",
          zh_hant: "提升會心率（[11, 12, 13, 14, 16]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "味方の2番目のACT実行前",
          en: "Before 2nd Ally Act",
          ko: "아군의 두 번째 ACT 실행 전",
          zh_hant: "在我方發動第2個ACT前"
        },
        id: 1002
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        3,
        3,
        3,
        3,
        2
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 249,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "味方の2番目のACT実行者",
          en: "2nd Acting Ally",
          ko: "아군의 두 번째 ACT 실행자",
          zh_hant: "第2名發動ACT的我方"
        },
        description: {
          ja: "回数マイナス効果減少([1, 1, 1, 1, 2])",
          en: "Count. Neg. Effects Reduction ([1, 1, 1, 1, 2])",
          ko: "횟수 마이너스 효과 감소([1, 1, 1, 1, 2])",
          zh_hant: "減少次數性負面效果（[1, 1, 1, 1, 2]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
