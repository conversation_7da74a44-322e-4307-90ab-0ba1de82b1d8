# frozen_string_literal: true

MEMOIR_4000343 = {
  basicInfo: {
    cardID: "4000343",
    rarity: 4,
    charas: [
      401,
      402,
      403,
      404,
      405
    ],
    name: {
      ja: "キラめき Every day シークフェルト音楽学院",
      en: "Brilliance Every Day Siegfeld Institute of Music",
      ko: "반짝임 Every day 시크펠트 음악학원",
      zh_hant: "光輝閃耀的每一天 席格菲特音樂學院"
    },
    profile: {
      ja: "舞台少女は、いついかなる時も眩しくキラめく。",
      en: "Stage Girls shine brilliantly at any given time.",
      ko: "무대소녀는 언제나 눈부시게 빛난다.",
      zh_hant: "不論身處何時何地，舞台少女都是那麼光彩耀目。"
    },
    released: {
      ja: 1672516800,
      ww: 1672516800
    }
  },
  stat: {
    total: 2022,
    atk: 400,
    hp: 5020,
    pdef: 800,
    mdef: 800
  },
  skill: {
    id: 12185,
    icon: 14,
    type: {
      ja: "永続",
      en: "Passive",
      ko: "영구",
      zh_hant: "永續"
    },
    params: [
      {
        icon: 14,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "すばやさアップ([20, 22, 24, 26, 30])",
          en: "Agility Up ([20, 22, 24, 26, 30])",
          ko: "민첩 증가([20, 22, 24, 26, 30])",
          zh_hant: "提升敏捷（[20, 22, 24, 26, 30]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン開始時",
          en: "Start of Turn",
          ko: "턴 시작 시",
          zh_hant: "回合開始時"
        },
        id: 1
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        2,
        2,
        2,
        2,
        1
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 14,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]ターン",
          en: "[1, 1, 1, 1, 2] Turn(s)",
          ko: "[1, 1, 1, 1, 2]턴",
          zh_hant: "[1, 1, 1, 1, 2]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方全体",
          en: "All Allies",
          ko: "아군 전체",
          zh_hant: "所有我方"
        },
        description: {
          ja: "すばやさアップ([15, 15, 20, 25, 30])",
          en: "Agility Up ([15, 15, 20, 25, 30])",
          ko: "민첩 증가([15, 15, 20, 25, 30])",
          zh_hant: "提升敏捷（[15, 15, 20, 25, 30]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 14,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]ターン",
          en: "[1, 1, 1, 1, 2] Turn(s)",
          ko: "[1, 1, 1, 1, 2]턴",
          zh_hant: "[1, 1, 1, 1, 2]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方のシークフェルト音楽学院",
          en: "Siegfeld Allies",
          ko: "아군 시크펠트 음악학원",
          zh_hant: "我方席格菲特音樂學院"
        },
        description: {
          ja: "すばやさアップ([15, 15, 20, 25, 30])",
          en: "Agility Up ([15, 15, 20, 25, 30])",
          ko: "민첩 증가([15, 15, 20, 25, 30])",
          zh_hant: "提升敏捷（[15, 15, 20, 25, 30]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
