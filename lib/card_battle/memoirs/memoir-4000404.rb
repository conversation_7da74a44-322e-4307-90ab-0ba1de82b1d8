# frozen_string_literal: true

MEMOIR_4000404 = {
  basicInfo: {
    cardID: "4000404",
    rarity: 4,
    charas: [
      103,
      201
    ],
    name: {
      ja: "パーティー料理を召し上がれ♪",
      en: "Exquisite Party Food♪",
      ko: "파티 요리를 드셔보세요♪",
      zh_hant: "來開吃派對料理囉♪"
    },
    profile: {
      ja: "料理の腕が評判のまひると珠緒。\nケーキと様々なお料理で『ごちそうたくさん♪手作り料理パーティー』の幕開けです！",
      en: "<PERSON><PERSON><PERSON> and <PERSON><PERSON> are well known for their cooking skills. The \"Exquisite Homemade Food Party♪\" opens with cakes and a variety of dishes!",
      ko: "요리 솜씨가 좋은 마히루와 타마오.\n케이크와 다양한 요리로 [맛있는 요리가 한가득♪ 손수 만든 요리 파티]가 지금 시작됩니다!",
      zh_hant: "真晝和珠緒的廚藝都非常高超。\n蛋糕和各種美食擺滿餐桌，『滿桌佳餚美食♪手工料理組合』的派對要開始囉！"
    },
    released: {
      ja: 1694156400,
      ww: 1696662000
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 12185,
    icon: 14,
    type: {
      ja: "永続",
      en: "Passive",
      ko: "영구",
      zh_hant: "永續"
    },
    params: [
      {
        icon: 14,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "すばやさアップ([20, 22, 24, 26, 30])",
          en: "Agility Up ([20, 22, 24, 26, 30])",
          ko: "민첩 증가([20, 22, 24, 26, 30])",
          zh_hant: "提升敏捷（[20, 22, 24, 26, 30]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン開始時",
          en: "Start of Turn",
          ko: "턴 시작 시",
          zh_hant: "回合開始時"
        },
        id: 1
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        2,
        2,
        2,
        2,
        1
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 14,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]ターン",
          en: "[1, 1, 1, 1, 2] Turn(s)",
          ko: "[1, 1, 1, 1, 2]턴",
          zh_hant: "[1, 1, 1, 1, 2]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方全体",
          en: "All Allies",
          ko: "아군 전체",
          zh_hant: "所有我方"
        },
        description: {
          ja: "すばやさアップ([15, 15, 20, 25, 30])",
          en: "Agility Up ([15, 15, 20, 25, 30])",
          ko: "민첩 증가([15, 15, 20, 25, 30])",
          zh_hant: "提升敏捷（[15, 15, 20, 25, 30]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 14,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]ターン",
          en: "[1, 1, 1, 1, 2] Turn(s)",
          ko: "[1, 1, 1, 1, 2]턴",
          zh_hant: "[1, 1, 1, 1, 2]回合"
        },
        accuracy: 100,
        target: {
          ja: "前から2体の味方",
          en: "2 Front Allies",
          ko: "앞에서 2명의 아군",
          zh_hant: "前面2名我方"
        },
        description: {
          ja: "すばやさアップ([15, 15, 20, 25, 30])",
          en: "Agility Up ([15, 15, 20, 25, 30])",
          ko: "민첩 증가([15, 15, 20, 25, 30])",
          zh_hant: "提升敏捷（[15, 15, 20, 25, 30]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 347,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]回",
          en: "[1, 1, 1, 1, 2] Time(s)",
          ko: "[1, 1, 1, 1, 2]회",
          zh_hant: "[1, 1, 1, 1, 2]次"
        },
        accuracy: 100,
        target: {
          ja: "前から2体の味方",
          en: "2 Front Allies",
          ko: "앞에서 2명의 아군",
          zh_hant: "前面2名我方"
        },
        description: {
          ja: "上位不屈",
          en: "Greater Fortitude",
          ko: "상위 불굴",
          zh_hant: "高級不屈"
        },
        descriptionExtra: nil
      }
    ]
  }
}
