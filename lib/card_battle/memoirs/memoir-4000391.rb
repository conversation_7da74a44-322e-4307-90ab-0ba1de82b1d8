# frozen_string_literal: true

MEMOIR_4000391 = {
  basicInfo: {
    cardID: "4000391",
    rarity: 4,
    charas: [
      103
    ],
    name: {
      ja: "まひるの腕前",
      en: "<PERSON><PERSON><PERSON>'s Talent",
      ko: "마히루의 실력",
      zh_hant: "真晝的身手"
    },
    profile: {
      ja: "「こんな風にバトンの演技を見られるの、なんだか緊張するなぁ」\n少し照れながらバトンを掲げるまひる。その朗らかな笑顔と演技は見る者を魅了する。",
      en: "\"I'm kind of nervous to display my baton skills like this.\"\n<PERSON><PERSON><PERSON> holds up the baton with a little embarrassment. Her cheerful smile and performance captivate all who see her.",
      ko: "[이런 식으로 배턴 연기를 보여주는 거 왠지 긴장되네]\n조금 쑥스러워하며 배턴을 들어 올리는 마히루. 그 쾌활한 미소와 연기는 보는 사람을 사로잡는다.",
      zh_hant: "「像這樣向大家表演舞棒，讓我有點緊張呢」\n真晝有點羞澀地舉起了舞棒，隨後便以和煦的笑容與舞藝迷倒了全場。"
    },
    released: {
      ja: 1683874800,
      ww: 1686639600
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 12167,
    icon: 29,
    type: {
      ja: "永続",
      en: "Passive",
      ko: "영구",
      zh_hant: "永續"
    },
    params: [
      {
        icon: 29,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "毎ターンキラめき回復([10, 10, 10, 10, 20])",
          en: "Brilliance Regen ([10, 10, 10, 10, 20])",
          ko: "매 턴마다 반짝임 회복([10, 10, 10, 10, 20])",
          zh_hant: "每回合回復光芒（[10, 10, 10, 10, 20]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 14,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "すばやさアップ([10, 10, 10, 10, 12])",
          en: "Agility Up ([10, 10, 10, 10, 12])",
          ko: "민첩 증가([10, 10, 10, 10, 12])",
          zh_hant: "提升敏捷（[10, 10, 10, 10, 12]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "敵の1番目のACT実行前",
          en: "Before 1st Enemy Act",
          ko: "적의 첫 번째 ACT 실행 전",
          zh_hant: "在敵方發動第1個ACT前"
        },
        id: 2001
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        3,
        3,
        3,
        3,
        2
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 57,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]ターン",
          en: "[1, 1, 1, 1, 2] Turn(s)",
          ko: "[1, 1, 1, 1, 2]턴",
          zh_hant: "[1, 1, 1, 1, 2]回合"
        },
        accuracy: 100,
        target: {
          ja: "敵役の1番目のACT実行者",
          en: "1st Acting Enemy",
          ko: "적의 첫 번째 ACT 실행자",
          zh_hant: "最先發動ACT的敵方"
        },
        description: {
          ja: "スタン",
          en: "Stun",
          ko: "스턴",
          zh_hant: "暈眩"
        },
        descriptionExtra: nil
      },
      {
        icon: 221,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]ターン",
          en: "[1, 1, 1, 1, 2] Turn(s)",
          ko: "[1, 1, 1, 1, 2]턴",
          zh_hant: "[1, 1, 1, 1, 2]回合"
        },
        accuracy: 100,
        target: {
          ja: "敵役の1番目のACT実行者",
          en: "1st Acting Enemy",
          ko: "적의 첫 번째 ACT 실행자",
          zh_hant: "最先發動ACT的敵方"
        },
        description: {
          ja: "恋わずらい",
          en: "Lovesickness",
          ko: "상사병",
          zh_hant: "苦戀"
        },
        descriptionExtra: nil
      }
    ]
  }
}
