# frozen_string_literal: true

MEMOIR_4000279 = {
  basicInfo: {
    cardID: "4000279",
    rarity: 4,
    charas: [
      402,
      405
    ],
    name: {
      ja: "映画鑑賞は劇場で",
      en: "Movies at the Theater",
      ko: "영화 감상은 극장에서",
      zh_hant: "在劇場中看電影"
    },
    profile: {
      ja: "「見てくださいミチル先輩。このインタビュー記事、なかなか興味深いですよ～？」\n「わっ、こんな裏話もあったんだ！」\n上映開始まで会話は尽きない。",
      en: "\"Look, <PERSON><PERSON>ru-senpai! This article about the interview is really interesting!\"\n\"Wow, I had no idea that was going on in the background!\".\nSo many things to talk about before the start of the movie.",
      ko: "[이거 보세요, 미치루 선배. 이 인터뷰 기사, 꽤나 흥미롭지 않나요~?]\n[우와, 이런 뒷이야기도 있었구나!]\n영화가 시작될 때까지 대화는 끊이질 않는다.",
      zh_hant: "「妳看，未知留學姐。這個訪談文章的內容很有意思喔～？」\n「哇，原來背後還有這樣的內幕啊！」\n電影放映前，兩人的話題毫無止盡。"
    },
    released: {
      ja: 1655622000,
      ww: 1658127600
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 20020,
    icon: 89,
    type: {
      ja: "開幕時",
      en: "At Start",
      ko: "개막",
      zh_hant: "開幕時"
    },
    params: [
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "キラめき回復([28, 31, 34, 37, 40])",
          en: "Brilliance Recovery ([28, 31, 34, 37, 40])",
          ko: "반짝임 회복([28, 31, 34, 37, 40])",
          zh_hant: "回復光芒（[28, 31, 34, 37, 40]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "敵の3番目のACT実行前",
          en: "Before 3rd Enemy Act",
          ko: "적의 세 번째 ACT 실행 전",
          zh_hant: "在敵方發動第3個ACT前"
        },
        id: 2003
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        3,
        3,
        3,
        3,
        2
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 237,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]回",
          en: "[1, 1, 1, 1, 2] Time(s)",
          ko: "[1, 1, 1, 1, 2]회",
          zh_hant: "[1, 1, 1, 1, 2]次"
        },
        accuracy: 100,
        target: {
          ja: "敵役の3番目のACT実行者",
          en: "3rd Acting Enemy",
          ko: "적의 세 번째 ACT 실행자",
          zh_hant: "第3名發動ACT的敵方"
        },
        description: {
          ja: "慢心([3500, 3500, 4000, 4500, 5000])",
          en: "Impudence ([3500, 3500, 4000, 4500, 5000])",
          ko: "방심([3500, 3500, 4000, 4500, 5000])",
          zh_hant: "傲慢（[3500, 3500, 4000, 4500, 5000]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
