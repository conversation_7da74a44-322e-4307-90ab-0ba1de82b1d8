# frozen_string_literal: true

MEMOIR_4000275 = {
  basicInfo: {
    cardID: "4000275",
    rarity: 4,
    charas: [
      205
    ],
    name: {
      ja: "キラめきBirthday 田中ゆゆ子",
      en: "Brilliance Birthday Yuyu<PERSON> Tanaka",
      ko: "반짝임 Birthday 타나카 유유코",
      zh_hant: "光輝閃耀的生日 田中悠悠子"
    },
    profile: {
      ja: "１年に１度の誕生日、大切なみんなと一緒に過ごす特別な日。\n新たな始まりの日に、よりキラめく自分になると、新たな誓いをその胸に抱いて。",
      en: "A special day to spend with all of your loved ones that comes only once a year. A new day and a new vow to become even more brilliant.",
      ko: "1년에 한 번뿐인 생일, 소중한 모두와 함께 보내는 특별한 날.\n새로운 시작의 날에 더욱 반짝이는 내가 되겠다는 새로운 맹세를 이 가슴에 안고.",
      zh_hant: "一年一度的生日，也是能與最重要的大家一起度過的、獨一無二的時光。\n在嶄新的起始之日裡，為成就更閃耀的自己而許下新的誓言，並將其銘刻在心。"
    },
    released: {
      ja: 1654354800,
      ww: 1654354800
    }
  },
  stat: {
    total: 2022,
    atk: 400,
    hp: 5020,
    pdef: 800,
    mdef: 800
  },
  skill: {
    id: 12202,
    icon: 29,
    type: {
      ja: "永続",
      en: "Passive",
      ko: "영구",
      zh_hant: "永續"
    },
    params: [
      {
        icon: 29,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身かつ田中ゆゆ子",
          en: "Self & Yuyuko Tanaka",
          ko: "장착한 무대소녀가 타나카 유유코",
          zh_hant: "自己（必須為田中悠悠子）"
        },
        description: {
          ja: "毎ターンキラめき回復([20, 21, 22, 23, 25])",
          en: "Brilliance Regen ([20, 21, 22, 23, 25])",
          ko: "매 턴마다 반짝임 회복([20, 21, 22, 23, 25])",
          zh_hant: "每回合回復光芒（[20, 21, 22, 23, 25]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 20,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身かつ田中ゆゆ子",
          en: "Self & Yuyuko Tanaka",
          ko: "장착한 무대소녀가 타나카 유유코",
          zh_hant: "自己（必須為田中悠悠子）"
        },
        description: {
          ja: "クリティカル率アップ([10, 11, 12, 13, 15])",
          en: "Dexterity Up ([10, 11, 12, 13, 15])",
          ko: "크리티컬 확률 증가([10, 11, 12, 13, 15])",
          zh_hant: "提升會心率（[10, 11, 12, 13, 15]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン開始時",
          en: "Start of Turn",
          ko: "턴 시작 시",
          zh_hant: "回合開始時"
        },
        id: 1
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        3,
        3,
        3,
        3,
        2
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 8,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[2, 2, 2, 2, 3]ターン",
          en: "[2, 2, 2, 2, 3] Turn(s)",
          ko: "[2, 2, 2, 2, 3]턴",
          zh_hant: "[2, 2, 2, 2, 3]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方全体",
          en: "All Allies",
          ko: "아군 전체",
          zh_hant: "所有我方"
        },
        description: {
          ja: "ACTパワーアップ([10, 10, 12, 16, 20])",
          en: "Act Power Up ([10, 10, 12, 16, 20])",
          ko: "ACT 파워 증가([10, 10, 12, 16, 20])",
          zh_hant: "提升ACT力量（[10, 10, 12, 16, 20]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 20,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[2, 2, 2, 2, 3]ターン",
          en: "[2, 2, 2, 2, 3] Turn(s)",
          ko: "[2, 2, 2, 2, 3]턴",
          zh_hant: "[2, 2, 2, 2, 3]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方全体",
          en: "All Allies",
          ko: "아군 전체",
          zh_hant: "所有我方"
        },
        description: {
          ja: "クリティカル率アップ([10, 10, 12, 16, 20])",
          en: "Dexterity Up ([10, 10, 12, 16, 20])",
          ko: "크리티컬 확률 증가([10, 10, 12, 16, 20])",
          zh_hant: "提升會心率（[10, 10, 12, 16, 20]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 8,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[2, 2, 2, 2, 3]ターン",
          en: "[2, 2, 2, 2, 3] Turn(s)",
          ko: "[2, 2, 2, 2, 3]턴",
          zh_hant: "[2, 2, 2, 2, 3]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方の田中ゆゆ子",
          en: "Ally Yuyuko Tanaka",
          ko: "아군 타나카 유유코",
          zh_hant: "我方田中悠悠子"
        },
        description: {
          ja: "ACTパワーアップ([10, 10, 12, 16, 20])",
          en: "Act Power Up ([10, 10, 12, 16, 20])",
          ko: "ACT 파워 증가([10, 10, 12, 16, 20])",
          zh_hant: "提升ACT力量（[10, 10, 12, 16, 20]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 20,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[2, 2, 2, 2, 3]ターン",
          en: "[2, 2, 2, 2, 3] Turn(s)",
          ko: "[2, 2, 2, 2, 3]턴",
          zh_hant: "[2, 2, 2, 2, 3]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方の田中ゆゆ子",
          en: "Ally Yuyuko Tanaka",
          ko: "아군 타나카 유유코",
          zh_hant: "我方田中悠悠子"
        },
        description: {
          ja: "クリティカル率アップ([10, 10, 12, 16, 20])",
          en: "Dexterity Up ([10, 10, 12, 16, 20])",
          ko: "크리티컬 확률 증가([10, 10, 12, 16, 20])",
          zh_hant: "提升會心率（[10, 10, 12, 16, 20]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
