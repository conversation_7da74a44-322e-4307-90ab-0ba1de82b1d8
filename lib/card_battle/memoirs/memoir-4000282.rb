# frozen_string_literal: true

MEMOIR_4000282 = {
  basicInfo: {
    cardID: "4000282",
    rarity: 4,
    charas: [
      406
    ],
    name: {
      ja: "転入生の朝",
      en: "First Morning",
      ko: "전학생의 아침",
      zh_hant: "轉學生的早上"
    },
    profile: {
      ja: "「詩呂、上着取って？」\n普段は子供扱いを嫌がるステラだが、この日は特別。\n不慣れな日本での転入初日の朝、不安な気持ちを解かすため、信頼するメイドにちょっぴり甘えたかったのだ。",
      en: "\"<PERSON><PERSON>, can you get me my coat?\"\n<PERSON><PERSON><PERSON> usually dislikes being treated like a child, but today it's the first morning at their new school in Japan. Relying on her faithful caretaker helps her relax her tension just a little.",
      ko: "[시로, 겉옷 좀 줄래?]\n평소 어린아이 취급받기 싫어하는 스텔라지만 이 날만은 달랐다.\n익숙하지 않은 일본에서 맞이하는 전학 첫 날 아침, 불안한 마음을 달래기 위해 신뢰하는 메이드에게 조금 어리광을 부리고 싶었던 것이다.",
      zh_hant: "「詩呂，可以替我拿外套嗎？」\n史黛拉雖然平時討厭被當作小孩子，但這天卻例外。\n在來到陌生的日本後，轉學的第一天早上，她希望能向自己信賴的女僕撒嬌以解除內心的不安。"
    },
    released: {
      ja: 1656918000,
      ww: 1660892400
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 20020,
    icon: 89,
    type: {
      ja: "開幕時",
      en: "At Start",
      ko: "개막",
      zh_hant: "開幕時"
    },
    params: [
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "キラめき回復([28, 31, 34, 37, 40])",
          en: "Brilliance Recovery ([28, 31, 34, 37, 40])",
          ko: "반짝임 회복([28, 31, 34, 37, 40])",
          zh_hant: "回復光芒（[28, 31, 34, 37, 40]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "敵の3番目のACT実行前",
          en: "Before 3rd Enemy Act",
          ko: "적의 세 번째 ACT 실행 전",
          zh_hant: "在敵方發動第3個ACT前"
        },
        id: 2003
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        3,
        3,
        3,
        3,
        2
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 242,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 1]ターン",
          en: "[1, 1, 1, 1, 1] Turn(s)",
          ko: "[1, 1, 1, 1, 1]턴",
          zh_hant: "[1, 1, 1, 1, 1]回合"
        },
        accuracy: 100,
        target: {
          ja: "敵役の3番目のACT実行者",
          en: "3rd Acting Enemy",
          ko: "적의 세 번째 ACT 실행자",
          zh_hant: "第3名發動ACT的敵方"
        },
        description: {
          ja: "悶絶",
          en: "Agony",
          ko: "민절",
          zh_hant: "窒息"
        },
        descriptionExtra: nil
      }
    ]
  }
}
