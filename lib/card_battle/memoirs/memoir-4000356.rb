# frozen_string_literal: true

MEMOIR_4000356 = {
  basicInfo: {
    cardID: "4000356",
    rarity: 4,
    charas: [
      305
    ],
    name: {
      ja: "ミルキィホームズ エルキュール＆静羽",
      en: "<PERSON><PERSON> Holmes Hercule & Shizuha",
      ko: "밀키홈즈 에르큘 & 시즈하",
      zh_hant: "少女福爾摩斯 艾露克露&靜羽"
    },
    profile: {
      ja: "お揃いの衣裳を纏ったエリーと静羽。\nふたりの間に静かでやさしい時間が流れる。",
      en: "<PERSON> and <PERSON><PERSON><PERSON> in matching costumes. They spend a quiet and gentle time together.",
      ko: "같은 의상을 걸친 에리와 시즈하.\n둘 사이에 조용하고 기분 좋은 시간이 흐른다.",
      zh_hant: "身穿同款服裝的艾莉與靜羽。\n嫻靜的時光正在兩人身旁悄悄流逝。"
    },
    released: {
      ja: 1675839600,
      ww: 1678950000
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 22850,
    icon: 89,
    type: {
      ja: "開幕時",
      en: "At Start",
      ko: "개막",
      zh_hant: "開幕時"
    },
    params: [
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "キラめき回復([22, 24, 27, 29, 32])",
          en: "Brilliance Recovery ([22, 24, 27, 29, 32])",
          ko: "반짝임 회복([22, 24, 27, 29, 32])",
          zh_hant: "回復光芒（[22, 24, 27, 29, 32]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 29,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 1]ターン",
          en: "[1, 1, 1, 1, 1] Turn(s)",
          ko: "[1, 1, 1, 1, 1]턴",
          zh_hant: "[1, 1, 1, 1, 1]回合"
        },
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "毎ターンキラめき回復([10, 12, 14, 17, 20])",
          en: "Brilliance Regen ([10, 12, 14, 17, 20])",
          ko: "매 턴마다 반짝임 회복([10, 12, 14, 17, 20])",
          zh_hant: "每回合回復光芒（[10, 12, 14, 17, 20]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      3,
      3,
      3,
      3,
      2
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン終了時",
          en: "End of Turn",
          ko: "턴 종료 시",
          zh_hant: "回合結束時"
        },
        id: 2
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        2,
        2,
        2,
        2,
        1
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "味方全体",
          en: "All Allies",
          ko: "아군 전체",
          zh_hant: "所有我方"
        },
        description: {
          ja: "キラめき回復([10, 10, 11, 13, 15])",
          en: "Brilliance Recovery ([10, 10, 11, 13, 15])",
          ko: "반짝임 회복([10, 10, 11, 13, 15])",
          zh_hant: "回復光芒（[10, 10, 11, 13, 15]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 178,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[2, 2, 2, 2, 2]ターン",
          en: "[2, 2, 2, 2, 2] Turn(s)",
          ko: "[2, 2, 2, 2, 2]턴",
          zh_hant: "[2, 2, 2, 2, 2]回合"
        },
        accuracy: 100,
        target: {
          ja: "敵役全体",
          en: "All Enemies",
          ko: "적 전체",
          zh_hant: "所有敵方"
        },
        description: {
          ja: "ACT1封印",
          en: "Seal Act 1",
          ko: "ACT1 봉인",
          zh_hant: "ACT1封印"
        },
        descriptionExtra: nil
      }
    ]
  }
}
