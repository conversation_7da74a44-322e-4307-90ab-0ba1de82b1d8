# frozen_string_literal: true

MEMOIR_4000474 = {
  basicInfo: {
    cardID: "4000474",
    rarity: 4,
    charas: [
      401,
      405
    ],
    name: {
      ja: "飛び込もう、星々の海へ",
      en: "Sea of Stars",
      ko: "뛰어들자, 별의 바다로",
      zh_hant: "飛身躍入星辰大海"
    },
    profile: {
      ja: "数百万の朝と夜を繰り返す世界。\n決して交わってはならない神々の邂逅、そしてその先にあるものは……？\n『エリュシオン 神世の章』より",
      en: "A world where countless mornings and nights repeat. An encounter of gods who must never cross paths. What lies ahead...? From \"Elysion - Chapter of the Gods.\"",
      ko: "수백만 번의 아침과 밤이 반복되는 세상.\n결코 어울려서는 안 되는 신들의 해후, 그리고 그 너머에 있는 것은...?\n[엘리시온 신들의 장]에서",
      zh_hant: "歷經數百萬個日夜交替的世界。\n絕不能相遇的女神們邂逅了彼此，她們究竟會迎來怎樣的未來……？\n取自『Elysion 神世之章』"
    },
    released: {
      ja: 1709622000,
      ww: 1712041200
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 23151,
    icon: 0,
    type: {
      ja: "開幕時",
      en: "At Start",
      ko: "개막",
      zh_hant: "開幕時"
    },
    params: [
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "キラめき回復([22, 24, 27, 29, 32])",
          en: "Brilliance Recovery ([22, 24, 27, 29, 32])",
          ko: "반짝임 회복([22, 24, 27, 29, 32])",
          zh_hant: "回復光芒（[22, 24, 27, 29, 32]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 347,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]回",
          en: "[1, 1, 1, 1, 2] Time(s)",
          ko: "[1, 1, 1, 1, 2]회",
          zh_hant: "[1, 1, 1, 1, 2]次"
        },
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "上位不屈",
          en: "Greater Fortitude",
          ko: "상위 불굴",
          zh_hant: "高級不屈"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン開始時",
          en: "Start of Turn",
          ko: "턴 시작 시",
          zh_hant: "回合開始時"
        },
        id: 1
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        3,
        3,
        3,
        3,
        2
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 396,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 1]回",
          en: "[1, 1, 1, 1, 1] Time(s)",
          ko: "[1, 1, 1, 1, 1]회",
          zh_hant: "[1, 1, 1, 1, 1]次"
        },
        accuracy: 100,
        target: {
          ja: "HP割合が低い2体の味方",
          en: "2 Allies with Lowest HP",
          ko: "HP 비율이 낮은 아군 2명",
          zh_hant: "剩餘HP較低的2名我方"
        },
        description: {
          ja: "上位祝福[HP回復]([50, 50, 60, 75, 100])",
          en: "Greater Blessing [HP Recovery] ([50, 50, 60, 75, 100])",
          ko: "상위 축복 [HP 회복]([50, 50, 60, 75, 100])",
          zh_hant: "高級祝福[回復HP]（[50, 50, 60, 75, 100]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 6,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "HP割合が低い2体の味方",
          en: "2 Allies with Lowest HP",
          ko: "HP 비율이 낮은 아군 2명",
          zh_hant: "剩餘HP較低的2名我方"
        },
        description: {
          ja: "HP回復([50, 50, 60, 75, 100])",
          en: "HP Recovery ([50, 50, 60, 75, 100])",
          ko: "HP 회복([50, 50, 60, 75, 100])",
          zh_hant: "回復HP（[50, 50, 60, 75, 100]）"
        },
        descriptionExtra: {
          ja: "回復量は対象の最大HPに依存する",
          en: "Recovered Amount Based on Target's Max HP",
          ko: "회복량은 대상의 최대 HP에 따라 변동",
          zh_hant: "回復量將依據回復對象的最大HP而定"
        }
      }
    ]
  }
}
