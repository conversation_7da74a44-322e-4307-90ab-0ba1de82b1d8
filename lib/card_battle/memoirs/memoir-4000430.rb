# frozen_string_literal: true

MEMOIR_4000430 = {
  basicInfo: {
    cardID: "4000430",
    rarity: 4,
    charas: [
      103,
      407,
      301
    ],
    name: {
      ja: "Special day memories ５月",
      en: "Memories of a Special Day May",
      ko: "Special day memories 5월",
      zh_hant: "Special day memories 5月"
    },
    profile: {
      ja: "舞台少女たちの特別な日の思い出は、いつまでもキラめき続ける。",
      en: "The Stage Girls' memories of their special day will always sparkle.",
      ko: "무대소녀들의 특별한 날의 추억은 언제까지나 끝없이 반짝인다.",
      zh_hant: "舞台少女們在特別的日子裡共度的時光，將化為回憶並永遠地閃耀。"
    },
    released: {
      ja: 1714489200,
      ww: 1717167600
    }
  },
  stat: {
    total: 2023,
    atk: 770,
    hp: 3370,
    pdef: 300,
    mdef: 300
  },
  skill: {
    id: 23284,
    icon: 0,
    type: {
      ja: "開幕時",
      en: "At Start",
      ko: "개막",
      zh_hant: "開幕時"
    },
    params: [
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "キラめき回復([28, 31, 34, 37, 40])",
          en: "Brilliance Recovery ([28, 31, 34, 37, 40])",
          ko: "반짝임 회복([28, 31, 34, 37, 40])",
          zh_hant: "回復光芒（[28, 31, 34, 37, 40]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 347,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]回",
          en: "[1, 1, 1, 1, 2] Time(s)",
          ko: "[1, 1, 1, 1, 2]회",
          zh_hant: "[1, 1, 1, 1, 2]次"
        },
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "上位不屈",
          en: "Greater Fortitude",
          ko: "상위 불굴",
          zh_hant: "高級不屈"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン開始時",
          en: "Start of Turn",
          ko: "턴 시작 시",
          zh_hant: "回合開始時"
        },
        id: 1
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        3,
        3,
        3,
        3,
        2
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 379,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[2, 2, 2, 2, 2]ターン",
          en: "[2, 2, 2, 2, 2] Turn(s)",
          ko: "[2, 2, 2, 2, 2]턴",
          zh_hant: "[2, 2, 2, 2, 2]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方全体",
          en: "All Allies",
          ko: "아군 전체",
          zh_hant: "所有我方"
        },
        description: {
          ja: "上位すばやさアップ([100, 100, 100, 100, 100])",
          en: "Greater Agility Up ([100, 100, 100, 100, 100])",
          ko: "상위 민첩 증가([100, 100, 100, 100, 100])",
          zh_hant: "[高級]提升敏捷（[100, 100, 100, 100, 100]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
