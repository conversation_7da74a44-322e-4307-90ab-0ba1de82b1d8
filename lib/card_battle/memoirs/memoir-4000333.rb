# frozen_string_literal: true

MEMOIR_4000333 = {
  basicInfo: {
    cardID: "4000333",
    rarity: 4,
    charas: [
      103,
      305
    ],
    name: {
      ja: "イシュタルの誘惑",
      en: "<PERSON><PERSON><PERSON>'s Temptation",
      ko: "이슈타르의 유혹",
      zh_hant: "伊絲塔的誘惑"
    },
    profile: {
      ja: "「――ねえギルガメッシュ。いま一度あなたに問うていいかしら？」\nイシュタルは妖艶な笑みを浮かべ、ギルガメッシュに粗暴で甘美な愛を囁く。",
      en: "\"Hey, Gilgamesh. Can I ask you something?\" <PERSON><PERSON><PERSON> has a bewitching smile when she whispers sweet love to <PERSON><PERSON><PERSON>.",
      ko: "[――저기, 길가메시. 다시 한 번 네게 물어봐도 될까?]\n이슈타르는 요염한 미소를 지으며 길가메시에게 거칠고 감미로운 사랑을 속삭인다.",
      zh_hant: "「――吉爾伽美什。妾身可以再問你一次嗎？」\n伊絲塔臉上帶著妖豔的笑容，對吉爾伽美什輕聲道出粗暴又甜膩的愛語。"
    },
    released: {
      ja: 1670655600,
      ww: 1674802800
    }
  },
  stat: {
    total: 900,
    atk: 500,
    hp: 0,
    pdef: 0,
    mdef: 0
  },
  skill: {
    id: 10118,
    icon: 22,
    type: {
      ja: "永続",
      en: "Passive",
      ko: "영구",
      zh_hant: "永續"
    },
    params: [
      {
        icon: 22,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "クリティカル威力アップ([21, 23, 25, 27, 30])",
          en: "Critical Up ([21, 23, 25, 27, 30])",
          ko: "크리티컬 위력 증가([21, 23, 25, 27, 30])",
          zh_hant: "提升會心威力（[21, 23, 25, 27, 30]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 20,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "クリティカル率アップ([11, 12, 13, 14, 16])",
          en: "Dexterity Up ([11, 12, 13, 14, 16])",
          ko: "크리티컬 확률 증가([11, 12, 13, 14, 16])",
          zh_hant: "提升會心率（[11, 12, 13, 14, 16]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "味方の1番目のACT実行前",
          en: "Before 1st Ally Act",
          ko: "아군의 첫 번째 ACT 실행 전",
          zh_hant: "在我方發動第1個ACT前"
        },
        id: 1001
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        3,
        3,
        3,
        3,
        2
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 249,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "味方の1番目のACT実行者",
          en: "1st Acting Ally",
          ko: "아군의 첫 번째 ACT 실행자",
          zh_hant: "最先發動ACT的我方"
        },
        description: {
          ja: "回数マイナス効果減少([1, 1, 1, 1, 2])",
          en: "Count. Neg. Effects Reduction ([1, 1, 1, 1, 2])",
          ko: "횟수 마이너스 효과 감소([1, 1, 1, 1, 2])",
          zh_hant: "減少次數性負面效果（[1, 1, 1, 1, 2]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
