# frozen_string_literal: true

MEMOIR_4000381 = {
  basicInfo: {
    cardID: "4000381",
    rarity: 4,
    charas: [
      405
    ],
    name: {
      ja: "One special day やちよ",
      en: "One Special Day Yachiyo",
      ko: "One special day 야치요",
      zh_hant: "One special day 八千代"
    },
    profile: {
      ja: "特別な日には、特別な衣裳に特別の笑顔。そして、いつものみんなと。",
      en: "A special costume and a special smile for a special occasion with her dear ones.",
      ko: "특별한 날에는 특별한 의상에 특별한 미소. 그리고, 평소와 같은 모두와 함께.",
      zh_hant: "在這個特別的日子裡要換上特別的服裝，並帶著特別的笑容與大家一同度過。"
    },
    released: {
      ja: 1699455600,
      ww: 1699455600
    }
  },
  stat: {
    total: 2023,
    atk: 770,
    hp: 3370,
    pdef: 300,
    mdef: 300
  },
  skill: {
    id: 23151,
    icon: 0,
    type: {
      ja: "開幕時",
      en: "At Start",
      ko: "개막",
      zh_hant: "開幕時"
    },
    params: [
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "キラめき回復([22, 24, 27, 29, 32])",
          en: "Brilliance Recovery ([22, 24, 27, 29, 32])",
          ko: "반짝임 회복([22, 24, 27, 29, 32])",
          zh_hant: "回復光芒（[22, 24, 27, 29, 32]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 347,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]回",
          en: "[1, 1, 1, 1, 2] Time(s)",
          ko: "[1, 1, 1, 1, 2]회",
          zh_hant: "[1, 1, 1, 1, 2]次"
        },
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "上位不屈",
          en: "Greater Fortitude",
          ko: "상위 불굴",
          zh_hant: "高級不屈"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン終了時",
          en: "End of Turn",
          ko: "턴 종료 시",
          zh_hant: "回合結束時"
        },
        id: 2
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        3,
        3,
        3,
        3,
        2
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 362,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[2, 2, 2, 2, 3]ターン",
          en: "[2, 2, 2, 2, 3] Turn(s)",
          ko: "[2, 2, 2, 2, 3]턴",
          zh_hant: "[2, 2, 2, 2, 3]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方全体",
          en: "All Allies",
          ko: "아군 전체",
          zh_hant: "所有我方"
        },
        description: {
          ja: "上位AP減少",
          en: "Greater AP Down",
          ko: "상위 AP 감소",
          zh_hant: "高級AP減少"
        },
        descriptionExtra: nil
      }
    ]
  }
}
