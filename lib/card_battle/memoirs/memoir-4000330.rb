# frozen_string_literal: true

MEMOIR_4000330 = {
  basicInfo: {
    cardID: "4000330",
    rarity: 4,
    charas: [
      104,
      501
    ],
    name: {
      ja: "オスカルのエスコート",
      en: "Dancing with <PERSON>",
      ko: "오스칼의 에스코트",
      zh_hant: "奧斯卡的護衛"
    },
    profile: {
      ja: "「ふふ……今夜はみんなの視線が痛いわね。オスカル、あなたと踊っているせいかしら？」\nマリーをエスコートするオスカル。麗しい２人の姿に舞踏会の視線は釘付け。",
      en: "\"Hmm... It seems we attracted quite a bit of attention tonight. Is it because I'm dancing with you, <PERSON>?\"\nOscar accompanies <PERSON> as everyone at the ball cannot take their eyes off of them.",
      ko: "[후후... 오늘 밤은 모두의 시선이 따갑군요. 오스칼, 당신과 춤추고 있어서일까요?]\n마리를 에스코트하는 오스칼. 아름다운 두 사람의 모습에 무도회의 모든 시선이 집중됐다.",
      zh_hant: "「呵呵……今晚大家都在狠狠地盯著我呢。奧斯卡，這是因為我在跟妳跳舞的緣故吧？」\n奧斯卡正在護衛瑪麗王妃。兩人美麗的姿態吸引了舞會上下所有的視線。"
    },
    released: {
      ja: 1668351600,
      ww: 1671202800
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 20020,
    icon: 89,
    type: {
      ja: "開幕時",
      en: "At Start",
      ko: "개막",
      zh_hant: "開幕時"
    },
    params: [
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "キラめき回復([28, 31, 34, 37, 40])",
          en: "Brilliance Recovery ([28, 31, 34, 37, 40])",
          ko: "반짝임 회복([28, 31, 34, 37, 40])",
          zh_hant: "回復光芒（[28, 31, 34, 37, 40]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      3,
      3,
      3,
      3,
      2
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "敵の1番目のACT実行前",
          en: "Before 1st Enemy Act",
          ko: "적의 첫 번째 ACT 실행 전",
          zh_hant: "在敵方發動第1個ACT前"
        },
        id: 2001
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        2,
        2,
        2,
        2,
        1
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 273,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]回",
          en: "[1, 1, 1, 1, 2] Time(s)",
          ko: "[1, 1, 1, 1, 2]회",
          zh_hant: "[1, 1, 1, 1, 2]次"
        },
        accuracy: 100,
        target: {
          ja: "敵役の1番目のACT実行者",
          en: "1st Acting Enemy",
          ko: "적의 첫 번째 ACT 실행자",
          zh_hant: "最先發動ACT的敵方"
        },
        description: {
          ja: "災難[キラめき減少]([100, 100, 100, 100, 100])",
          en: "Disaster [Brilliance Reduction] ([100, 100, 100, 100, 100])",
          ko: "재난 [반짝임 감소]([100, 100, 100, 100, 100])",
          zh_hant: "災難[光芒減少]([100, 100, 100, 100, 100])"
        },
        descriptionExtra: nil
      }
    ]
  }
}
