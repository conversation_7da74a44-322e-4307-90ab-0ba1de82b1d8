# frozen_string_literal: true

MEMOIR_4000400 = {
  basicInfo: {
    cardID: "4000400",
    rarity: 4,
    charas: [
      205
    ],
    name: {
      ja: "最強の音楽 ロック＆ゆゆ子",
      en: "Strongest Music LOCK & Yuyuko",
      ko: "최강의 음악 록 & 유유코",
      zh_hant: "最強音樂 LOCK&悠悠子"
    },
    profile: {
      ja: "最強の音楽を奏でる最強のバンド・RAISE A SUILENと舞台少女が夢の共演！\nマイペースなゆゆ子をロックが指南して、予測不能な２人のセッション！",
      en: "A dream collaboration between RAISE A SUILEN, the most powerful band playing the most powerful music, and Stage Girls! LOCK gives <PERSON><PERSON><PERSON> some pointers, and the two have an unpredictable session!",
      ko: "최강의 음악을 연주하는 최강의 밴드 RAISE A SUILEN과 무대소녀가 꿈의 합동 공연!\n마이페이스인 유유코를 록이 지도, 예측 불가능한 두 사람의 세션!",
      zh_hant: "演奏出最強音樂的最強樂團——RAISE A SUILEN與舞台少女的夢幻共演！\nLOCK正在指導總是我行我素的悠悠子，出人意表二人組即將展開合奏！"
    },
    released: {
      ja: 1691737200,
      ww: 1694934000
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 12374,
    icon: 29,
    type: {
      ja: "永続",
      en: "Passive",
      ko: "영구",
      zh_hant: "永續"
    },
    params: [
      {
        icon: 29,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "毎ターンキラめき回復([10, 10, 10, 10, 15])",
          en: "Brilliance Regen ([10, 10, 10, 10, 15])",
          ko: "매 턴마다 반짝임 회복([10, 10, 10, 10, 15])",
          zh_hant: "每回合回復光芒（[10, 10, 10, 10, 15]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 14,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "すばやさアップ([25, 25, 25, 25, 35])",
          en: "Agility Up ([25, 25, 25, 25, 35])",
          ko: "민첩 증가([25, 25, 25, 25, 35])",
          zh_hant: "提升敏捷（[25, 25, 25, 25, 35]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "味方の1番目のACT実行前",
          en: "Before 1st Ally Act",
          ko: "아군의 첫 번째 ACT 실행 전",
          zh_hant: "在我方發動第1個ACT前"
        },
        id: 1001
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        3,
        3,
        3,
        3,
        2
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 300,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "味方の1番目のACT実行者",
          en: "1st Acting Ally",
          ko: "아군의 첫 번째 ACT 실행자",
          zh_hant: "最先發動ACT的我方"
        },
        description: {
          ja: "回数マイナス効果解除",
          en: "Dispel Count. Neg. Effects",
          ko: "횟수 마이너스 효과 해제",
          zh_hant: "解除次數性負面效果"
        },
        descriptionExtra: nil
      },
      {
        icon: 250,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]ターン",
          en: "[1, 1, 1, 1, 2] Turn(s)",
          ko: "[1, 1, 1, 1, 2]턴",
          zh_hant: "[1, 1, 1, 1, 2]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方の1番目のACT実行者",
          en: "1st Acting Ally",
          ko: "아군의 첫 번째 ACT 실행자",
          zh_hant: "最先發動ACT的我方"
        },
        description: {
          ja: "回数マイナス効果耐性アップ([50, 50, 60, 80, 100])",
          en: "Count. Neg. Effects Resistance Up ([50, 50, 60, 80, 100])",
          ko: "횟수 마이너스 효과 저항 증가([50, 50, 60, 80, 100])",
          zh_hant: "提升次數性負面效果耐性（[50, 50, 60, 80, 100]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 318,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]回",
          en: "[1, 1, 1, 1, 2] Time(s)",
          ko: "[1, 1, 1, 1, 2]회",
          zh_hant: "[1, 1, 1, 1, 2]次"
        },
        accuracy: 100,
        target: {
          ja: "味方の1番目のACT実行者",
          en: "1st Acting Ally",
          ko: "아군의 첫 번째 ACT 실행자",
          zh_hant: "最先發動ACT的我方"
        },
        description: {
          ja: "ACTブースト[錯乱]",
          en: "Act Boost [Daze]",
          ko: "ACT 부스트[착란]",
          zh_hant: "ACT加成[錯亂]"
        },
        descriptionExtra: nil
      }
    ]
  }
}
