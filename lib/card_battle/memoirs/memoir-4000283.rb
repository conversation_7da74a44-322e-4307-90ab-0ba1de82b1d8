# frozen_string_literal: true

MEMOIR_4000283 = {
  basicInfo: {
    cardID: "4000283",
    rarity: 4,
    charas: [
      407
    ],
    name: {
      ja: "幸福な時間",
      en: "Magical Time",
      ko: "행복한 시간",
      zh_hant: "幸福的時光"
    },
    profile: {
      ja: "「詩呂、上着取って？」\n幼さの残る声でおねだりするステラにそっと上着を着せてあげる詩呂。\n不安を押し退けてシークフェルトの名を背負う主への、あたたかい眼差しとともに。",
      en: "\"<PERSON><PERSON>, can you get me my coat?\"\n<PERSON><PERSON> helps <PERSON> into her coat at her childlike request. She can see <PERSON> is trying to put on a strong front that's worthy of the <PERSON><PERSON><PERSON> name, so she watches on fondly.",
      ko: "[시로, 겉옷 좀 줄래?]\n순진무구한 목소리로 어리광을 부리는 스텔라에게 살며시 겉옷을 입혀주는 시로.\n불안한 마음을 억누르고 시크펠트의 이름을 짊어진 주인을 향한 따뜻한 눈빛과 함께.",
      zh_hant: "「詩呂，可以替我拿外套嗎？」\n聽到史黛拉帶著稚嫩的聲音撒嬌說道後，詩呂默默替她穿上外套，\n並向戰勝了不安、背負著席格菲特之名的主人投向溫暖的眼神。"
    },
    released: {
      ja: 1657177200,
      ww: 1661151600
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 20020,
    icon: 89,
    type: {
      ja: "開幕時",
      en: "At Start",
      ko: "개막",
      zh_hant: "開幕時"
    },
    params: [
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "キラめき回復([28, 31, 34, 37, 40])",
          en: "Brilliance Recovery ([28, 31, 34, 37, 40])",
          ko: "반짝임 회복([28, 31, 34, 37, 40])",
          zh_hant: "回復光芒（[28, 31, 34, 37, 40]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "敵の3番目のACT実行前",
          en: "Before 3rd Enemy Act",
          ko: "적의 세 번째 ACT 실행 전",
          zh_hant: "在敵方發動第3個ACT前"
        },
        id: 2003
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        3,
        3,
        3,
        3,
        2
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 241,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]ターン",
          en: "[1, 1, 1, 1, 2] Turn(s)",
          ko: "[1, 1, 1, 1, 2]턴",
          zh_hant: "[1, 1, 1, 1, 2]回合"
        },
        accuracy: 100,
        target: {
          ja: "敵役の3番目のACT実行者",
          en: "3rd Acting Enemy",
          ko: "적의 세 번째 ACT 실행자",
          zh_hant: "第3名發動ACT的敵方"
        },
        description: {
          ja: "凍傷([7500, 7500, 8000, 8500, 10000])",
          en: "Frostbite ([7500, 7500, 8000, 8500, 10000])",
          ko: "동상([7500, 7500, 8000, 8500, 10000])",
          zh_hant: "凍傷（[7500, 7500, 8000, 8500, 10000]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
