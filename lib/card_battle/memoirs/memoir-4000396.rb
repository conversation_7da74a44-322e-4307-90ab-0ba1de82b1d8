# frozen_string_literal: true

MEMOIR_4000396 = {
  basicInfo: {
    cardID: "4000396",
    rarity: 4,
    charas: [
      105,
      106
    ],
    name: {
      ja: "穏やかな夕暮れ",
      en: "Peaceful Dusk",
      ko: "잔잔한 석양",
      zh_hant: "恬靜的黃昏"
    },
    profile: {
      ja: "「天堂さん、手伝ってもらって悪いわね」\n「いえ、お役に立てて良かったです」\n夕日に染まる廊下で、どちらからともなく歩幅が合う２人。\n穏やかな時間が流れている。",
      en: "\"Sorry for asking you to help, Tendo-san.\"\n\"Not at all. I'm glad I could be of help.\"\nThe two of them stride together along the corridor tinged with sunset on a peaceful and calm day.",
      ko: "[텐도, 수고롭게 해서 미안해]\n[아뇨, 도움이 되어 다행이에요]\n석양으로 물들어가는 복도에서 어느새 보폭을 맞춘 두 사람.\n잔잔한 시간이 흐르고 있다.",
      zh_hant: "「天堂同學，麻煩妳幫忙，真是不好意思」\n「不會，我很高興能幫上忙」\n在被夕陽染上了色彩的走廊上，兩人自然而然地配合著彼此的步伐。\n她們共度了一段恬靜的時光。"
    },
    released: {
      ja: 1688972400,
      ww: 1691391600
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 12167,
    icon: 29,
    type: {
      ja: "永続",
      en: "Passive",
      ko: "영구",
      zh_hant: "永續"
    },
    params: [
      {
        icon: 29,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "毎ターンキラめき回復([10, 10, 10, 10, 20])",
          en: "Brilliance Regen ([10, 10, 10, 10, 20])",
          ko: "매 턴마다 반짝임 회복([10, 10, 10, 10, 20])",
          zh_hant: "每回合回復光芒（[10, 10, 10, 10, 20]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 14,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "すばやさアップ([10, 10, 10, 10, 12])",
          en: "Agility Up ([10, 10, 10, 10, 12])",
          ko: "민첩 증가([10, 10, 10, 10, 12])",
          zh_hant: "提升敏捷（[10, 10, 10, 10, 12]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン終了時",
          en: "End of Turn",
          ko: "턴 종료 시",
          zh_hant: "回合結束時"
        },
        id: 2
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        2,
        2,
        2,
        2,
        1
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 222,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "敵役全体",
          en: "All Enemies",
          ko: "적 전체",
          zh_hant: "所有敵方"
        },
        description: {
          ja: "キラめき減少([10, 10, 15, 20, 25])",
          en: "Brilliance Reduction ([10, 10, 15, 20, 25])",
          ko: "반짝임 감소([10, 10, 15, 20, 25])",
          zh_hant: "減少光芒（[10, 10, 15, 20, 25]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
