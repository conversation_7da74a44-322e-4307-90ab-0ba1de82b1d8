# frozen_string_literal: true

MEMOIR_4000302 = {
  basicInfo: {
    cardID: "4000302",
    rarity: 4,
    charas: [
      401,
      402
    ],
    name: {
      ja: "『エーデル』最高峰",
      en: "Top \"Edel\"",
      ko: "[에델] 최고봉",
      zh_hant: "『高貴皇君』的巔峰"
    },
    profile: {
      ja: "「感じるだろう？　私の温もりを……」\n全てを薙ぎ払い、王者は頂点に立つ。しかし、その心の内は――\n『少女☆歌劇 レヴュースタァライト -The LIVE エーデル- Delight』より",
      en: "\"Can you feel my warmth...?\"\nSweeping everything away, the king stands at the top. But inside their heart... From \"Revue Starlight -The LIVE Edel- Delight.\"",
      ko: "[느껴지지? 내 온기가...]\n모든 것을 뿌리치고 왕은 정점에 선다. 허나, 그 마음속에는――\n[소녀☆가극 레뷰 스타라이트 -The LIVE 에델- Delight]에서",
      zh_hant: "「能感受到吧？感受到我的溫暖……」\n王者驅除了一切並立於巔峰。可是，其內心卻——\n取自『少女☆歌劇 Revue Starlight -The LIVE 高貴皇君- Delight』"
    },
    released: {
      ja: 1662534000,
      ww: 1666076400
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 20020,
    icon: 89,
    type: {
      ja: "開幕時",
      en: "At Start",
      ko: "개막",
      zh_hant: "開幕時"
    },
    params: [
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "キラめき回復([28, 31, 34, 37, 40])",
          en: "Brilliance Recovery ([28, 31, 34, 37, 40])",
          ko: "반짝임 회복([28, 31, 34, 37, 40])",
          zh_hant: "回復光芒（[28, 31, 34, 37, 40]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      5,
      5,
      5,
      5,
      4
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン終了時",
          en: "End of Turn",
          ko: "턴 종료 시",
          zh_hant: "回合結束時"
        },
        id: 2
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        4,
        4,
        4,
        4,
        3
      ],
      recastTurns: [
        4,
        4,
        4,
        4,
        3
      ]
    },
    params: [
      {
        icon: 265,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "後ろから2体の敵役",
          en: "2 Rear Enemies",
          ko: "뒤에서 2명의 적",
          zh_hant: "後面2名敵方"
        },
        description: {
          ja: "起死回生減少([1, 1, 1, 1, 2])",
          en: "Revive Reduction ([1, 1, 1, 1, 2])",
          ko: "기사회생 감소([1, 1, 1, 1, 2])",
          zh_hant: "減少起死回生（[1, 1, 1, 1, 2]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
