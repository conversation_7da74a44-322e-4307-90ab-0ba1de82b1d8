# frozen_string_literal: true

MEMOIR_4000349 = {
  basicInfo: {
    cardID: "4000349",
    rarity: 4,
    charas: [
      409,
      410
    ],
    name: {
      ja: "笑顔ではいチーズ☆",
      en: "Say Cheese☆",
      ko: "웃는 얼굴로 치즈☆",
      zh_hant: "保持笑容，來，笑一個☆"
    },
    profile: {
      ja: "「笑顔でもう一枚いくよ！　はいチーズ☆」\nステラの別荘ではしゃぐみんくに強引に引き寄せられるクイナ。\n笑顔はぎこちないけれど、心の内はきっと――",
      en: "\"I'll take another one—smile! Say, cheese!☆\" <PERSON><PERSON> draws <PERSON><PERSON> to herself at <PERSON>'s villa. <PERSON><PERSON> may look uncomfortable, but deep within she is smiling too.",
      ko: "[웃는 얼굴로 한 장 더 찍을게! 자, 치즈☆]\n스텔라의 별장에서 들뜬 밍크에게 강제로 끌려오는 쿠이나.\n어색한 미소를 띄고 있지만 마음 속으로는 분명――",
      zh_hant: "「保持笑容，再拍一張囉！來，笑一個☆」\n玖伊奈被明久硬拉到史黛拉家的別墅陪大家一起歡鬧。\n她臉上的笑容雖然很生硬，但她的內心一定――"
    },
    released: {
      ja: 1674630000,
      ww: 1676703600
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 20020,
    icon: 89,
    type: {
      ja: "開幕時",
      en: "At Start",
      ko: "개막",
      zh_hant: "開幕時"
    },
    params: [
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "キラめき回復([28, 31, 34, 37, 40])",
          en: "Brilliance Recovery ([28, 31, 34, 37, 40])",
          ko: "반짝임 회복([28, 31, 34, 37, 40])",
          zh_hant: "回復光芒（[28, 31, 34, 37, 40]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン終了時",
          en: "End of Turn",
          ko: "턴 종료 시",
          zh_hant: "回合結束時"
        },
        id: 2
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        2,
        2,
        2,
        2,
        1
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 157,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[2, 2, 2, 2, 3]ターン",
          en: "[2, 2, 2, 2, 3] Turn(s)",
          ko: "[2, 2, 2, 2, 3]턴",
          zh_hant: "[2, 2, 2, 2, 3]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方全体",
          en: "All Allies",
          ko: "아군 전체",
          zh_hant: "所有我方"
        },
        description: {
          ja: "AP減少",
          en: "AP Down",
          ko: "AP 감소",
          zh_hant: "AP減少"
        },
        descriptionExtra: nil
      }
    ]
  }
}
