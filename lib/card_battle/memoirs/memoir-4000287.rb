# frozen_string_literal: true

MEMOIR_4000287 = {
  basicInfo: {
    cardID: "4000287",
    rarity: 4,
    charas: [
      101,
      301
    ],
    name: {
      ja: "運命と真実のレヴュー",
      en: "Revue of Fate and Truth",
      ko: "운명과 진실의 레뷰",
      zh_hant: "命運與真實的Revue"
    },
    profile: {
      ja: "「あるよ、あるある！　あるある、あるよ！　私の舞台には――私の、すべてが！」\nレヴュー・フロンティアを経て、再び舞台へ戻ってきたあるる。\n華恋とあるる、２つのキラめきが今ぶつかり合う。",
      en: "\"There are! There are, there are! My stage—my everything!\"\n<PERSON><PERSON><PERSON> is back on the stage after confronting Revue Frontier. <PERSON> and <PERSON><PERSON><PERSON>—their brilliance clashes on the stage.",
      ko: "[있어, 있단 말이야! 있단 말이야, 있다고! 내 무대에는―― 내 모든 것이!]\n레뷰 프론티어를 겪고 다시금 무대로 돌아온 아루루.\n카렌과 아루루, 두 사람의 반짝임이 지금 맞부딪친다.",
      zh_hant: "「有喔，絕對有！一定有，絕對有！我的舞台上一定有著——我的一切！」\n經歷過Revue・芙羅提亞後，艾露露再次回到了舞台。\n華戀與艾露露的兩道光芒，如今正互相碰撞著。"
    },
    released: {
      ja: 1658260800,
      ww: 1660680000
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 10123,
    icon: 20,
    type: {
      ja: "永続",
      en: "Passive",
      ko: "영구",
      zh_hant: "永續"
    },
    params: [
      {
        icon: 20,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "クリティカル率アップ([11, 12, 13, 14, 16])",
          en: "Dexterity Up ([11, 12, 13, 14, 16])",
          ko: "크리티컬 확률 증가([11, 12, 13, 14, 16])",
          zh_hant: "提升會心率（[11, 12, 13, 14, 16]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 39,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "有利属性ダメージアップ([8, 9, 10, 11, 12])",
          en: "Effective Element Dmg Up ([8, 9, 10, 11, 12])",
          ko: "유리한 속성 대미지 증가([8, 9, 10, 11, 12])",
          zh_hant: "提升有利屬性傷害（[8, 9, 10, 11, 12]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      3,
      3,
      3,
      3,
      2
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン開始時",
          en: "Start of Turn",
          ko: "턴 시작 시",
          zh_hant: "回合開始時"
        },
        id: 1
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        2,
        2,
        2,
        2,
        1
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 22,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]ターン",
          en: "[1, 1, 1, 1, 2] Turn(s)",
          ko: "[1, 1, 1, 1, 2]턴",
          zh_hant: "[1, 1, 1, 1, 2]回合"
        },
        accuracy: 100,
        target: {
          ja: "前から5体の味方",
          en: "5 Front Allies",
          ko: "앞에서 5명의 아군",
          zh_hant: "前面5名我方"
        },
        description: {
          ja: "クリティカル威力アップ([20, 20, 25, 30, 40])",
          en: "Critical Up ([20, 20, 25, 30, 40])",
          ko: "크리티컬 위력 증가([20, 20, 25, 30, 40])",
          zh_hant: "提升會心威力（[20, 20, 25, 30, 40]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
