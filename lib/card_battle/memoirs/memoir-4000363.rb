# frozen_string_literal: true

MEMOIR_4000363 = {
  basicInfo: {
    cardID: "4000363",
    rarity: 4,
    charas: [
      108
    ],
    name: {
      ja: "One special day 双葉",
      en: "One Special Day Futaba",
      ko: "One special day 후타바",
      zh_hant: "One special day 雙葉"
    },
    profile: {
      ja: "特別な日には、特別な衣裳に特別の笑顔。そして、いつものみんなと。",
      en: "A special costume and a special smile for a special occasion with her dear ones.",
      ko: "특별한 날에는 특별한 의상에 특별한 미소. 그리고, 평소와 같은 모두와 함께.",
      zh_hant: "在這個特別的日子裡要換上特別的服裝，並帶著特別的笑容與大家一同度過。"
    },
    released: {
      ja: 1681657200,
      ww: 1681657200
    }
  },
  stat: {
    total: 2023,
    atk: 770,
    hp: 3370,
    pdef: 300,
    mdef: 300
  },
  skill: {
    id: 12300,
    icon: 29,
    type: {
      ja: "永続",
      en: "Passive",
      ko: "영구",
      zh_hant: "永續"
    },
    params: [
      {
        icon: 29,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "毎ターンキラめき回復([10, 12, 14, 16, 20])",
          en: "Brilliance Regen ([10, 12, 14, 16, 20])",
          ko: "매 턴마다 반짝임 회복([10, 12, 14, 16, 20])",
          zh_hant: "每回合回復光芒（[10, 12, 14, 16, 20]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 22,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "クリティカル威力アップ([10, 11, 12, 13, 15])",
          en: "Critical Up ([10, 11, 12, 13, 15])",
          ko: "크리티컬 위력 증가([10, 11, 12, 13, 15])",
          zh_hant: "提升會心威力（[10, 11, 12, 13, 15]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      3,
      3,
      3,
      3,
      2
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン開始時",
          en: "Start of Turn",
          ko: "턴 시작 시",
          zh_hant: "回合開始時"
        },
        id: 1
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        2,
        2,
        2,
        2,
        1
      ],
      recastTurns: [
        2,
        2,
        2,
        2,
        1
      ]
    },
    params: [
      {
        icon: 111,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[2, 2, 2, 2, 3]ターン",
          en: "[2, 2, 2, 2, 3] Turn(s)",
          ko: "[2, 2, 2, 2, 3]턴",
          zh_hant: "[2, 2, 2, 2, 3]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方全体",
          en: "All Allies",
          ko: "아군 전체",
          zh_hant: "所有我方"
        },
        description: {
          ja: "与ダメージアップ([10, 10, 11, 13, 15])",
          en: "Dmg Up ([10, 10, 11, 13, 15])",
          ko: "가하는 대미지 증가([10, 10, 11, 13, 15])",
          zh_hant: "提升造成的傷害（[10, 10, 11, 13, 15]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 20,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[2, 2, 2, 2, 3]ターン",
          en: "[2, 2, 2, 2, 3] Turn(s)",
          ko: "[2, 2, 2, 2, 3]턴",
          zh_hant: "[2, 2, 2, 2, 3]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方全体",
          en: "All Allies",
          ko: "아군 전체",
          zh_hant: "所有我方"
        },
        description: {
          ja: "クリティカル率アップ([10, 10, 12, 16, 20])",
          en: "Dexterity Up ([10, 10, 12, 16, 20])",
          ko: "크리티컬 확률 증가([10, 10, 12, 16, 20])",
          zh_hant: "提升會心率（[10, 10, 12, 16, 20]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "味方の宙属性",
          en: "Space Allies",
          ko: "아군 우주 속성",
          zh_hant: "宙屬性的我方"
        },
        description: {
          ja: "キラめき回復([10, 10, 11, 13, 15])",
          en: "Brilliance Recovery ([10, 10, 11, 13, 15])",
          ko: "반짝임 회복([10, 10, 11, 13, 15])",
          zh_hant: "回復光芒（[10, 10, 11, 13, 15]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 22,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[2, 2, 2, 2, 3]ターン",
          en: "[2, 2, 2, 2, 3] Turn(s)",
          ko: "[2, 2, 2, 2, 3]턴",
          zh_hant: "[2, 2, 2, 2, 3]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方の宙属性",
          en: "Space Allies",
          ko: "아군 우주 속성",
          zh_hant: "宙屬性的我方"
        },
        description: {
          ja: "クリティカル威力アップ([10, 10, 12, 16, 20])",
          en: "Critical Up ([10, 10, 12, 16, 20])",
          ko: "크리티컬 위력 증가([10, 10, 12, 16, 20])",
          zh_hant: "提升會心威力（[10, 10, 12, 16, 20]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
