# frozen_string_literal: true

MEMOIR_4000360 = {
  basicInfo: {
    cardID: "4000360",
    rarity: 4,
    charas: [
      107
    ],
    name: {
      ja: "ふたりの脚本会議",
      en: "Script Meeting",
      ko: "두 사람의 각본 회의",
      zh_hant: "兩人的劇本會議"
    },
    profile: {
      ja: "第１００回聖翔祭に向け、準備を進める聖翔音楽学園の面々。\nA組とB組、力を合わせて最高の戯曲『スタァライト』を魅せるために。",
      en: "The members of Seisho Music Academy are preparing for the 100th Seisho Festival. Group A and Group B join forces to perform the best \"Starlight\".",
      ko: "제100회 세이쇼제에 앞서 준비를 진행하는 세이쇼 음악학교의 모두들.\nA반과 B반이 힘을 합쳐 최고의 희곡 [스타라이트]를 선보이기 위해.",
      zh_hant: "為了在第100屆聖翔祭呈獻最完美的戲碼『Starlight』，\n聖翔音樂學院的A班跟B班正攜手合力、密鑼緊鼓地進行準備工作。"
    },
    released: {
      ja: 1678777200,
      ww: 1681196400
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 22850,
    icon: 89,
    type: {
      ja: "開幕時",
      en: "At Start",
      ko: "개막",
      zh_hant: "開幕時"
    },
    params: [
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "キラめき回復([22, 24, 27, 29, 32])",
          en: "Brilliance Recovery ([22, 24, 27, 29, 32])",
          ko: "반짝임 회복([22, 24, 27, 29, 32])",
          zh_hant: "回復光芒（[22, 24, 27, 29, 32]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 29,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 1]ターン",
          en: "[1, 1, 1, 1, 1] Turn(s)",
          ko: "[1, 1, 1, 1, 1]턴",
          zh_hant: "[1, 1, 1, 1, 1]回合"
        },
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "毎ターンキラめき回復([10, 12, 14, 17, 20])",
          en: "Brilliance Regen ([10, 12, 14, 17, 20])",
          ko: "매 턴마다 반짝임 회복([10, 12, 14, 17, 20])",
          zh_hant: "每回合回復光芒（[10, 12, 14, 17, 20]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      3,
      3,
      3,
      3,
      2
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン開始時",
          en: "Start of Turn",
          ko: "턴 시작 시",
          zh_hant: "回合開始時"
        },
        id: 1
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        2,
        2,
        2,
        2,
        1
      ],
      recastTurns: [
        2,
        2,
        2,
        2,
        1
      ]
    },
    params: [
      {
        icon: 287,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "敵役全体",
          en: "All Enemies",
          ko: "적 전체",
          zh_hant: "所有敵方"
        },
        description: {
          ja: "変換[起死回生→急所]([1, 1, 1, 1, 2])",
          en: "[Change [Revive→Weak Spot]] ([1, 1, 1, 1, 2])",
          ko: "변환[기사회생→급소]([1, 1, 1, 1, 2])",
          zh_hant: "變換[起死回生→要害]（[1, 1, 1, 1, 2]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
