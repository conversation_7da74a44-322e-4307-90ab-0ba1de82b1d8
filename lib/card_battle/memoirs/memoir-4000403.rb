# frozen_string_literal: true

MEMOIR_4000403 = {
  basicInfo: {
    cardID: "4000403",
    rarity: 4,
    charas: [
      408,
      409
    ],
    name: {
      ja: "ピエロのたしなみ",
      en: "Clown's Etiquette",
      ko: "피에로의 소양",
      zh_hant: "小丑的特技"
    },
    profile: {
      ja: "「さすがみんく、もう覚えちゃったね。上手上手！」\nピエロ役に向けて、これまで乗ったことがなかった一輪車に挑戦するみんく。上達が早いのは天賦の才か、良子の指導力の賜物か……？",
      en: "\"Woah, Minku! You've already learned this much!\"\n<PERSON><PERSON><PERSON> rides a unicycle for the first time to prepare for her role as clown. Is she just naturally talented or has <PERSON><PERSON><PERSON>'s efforts to guide her finally paid off?",
      ko: "[역시 밍크야, 벌써 외워 버렸구나. 정말 대단해!]\n피에로 역 준비를 위해 한 번도 타본 적 없는 외발자전거에 도전하는 밍크. 성장이 빠른 건 천부적 재능인가, 아니면 료코의 지도력 덕분인 것인가...",
      zh_hant: "「真不愧是明久，這麼快就學會了。真棒真棒！」\n為了揣摩小丑的演技，明久首次挑戰騎乘單輪車。她能快速學會騎單輪車，究竟是本身天賦異稟，還是得益於良子的教導有方呢……？"
    },
    released: {
      ja: 1694847600,
      ww: 1697266800
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 12392,
    icon: 15,
    type: {
      ja: "永続",
      en: "Passive",
      ko: "영구",
      zh_hant: "永續"
    },
    params: [
      {
        icon: 15,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "すばやさダウン([20, 22, 24, 26, 30])",
          en: "Agility Down ([20, 22, 24, 26, 30])",
          ko: "민첩 감소([20, 22, 24, 26, 30])",
          zh_hant: "降低敏捷（[20, 22, 24, 26, 30]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      2,
      2,
      2,
      2,
      1
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン開始時",
          en: "Start of Turn",
          ko: "턴 시작 시",
          zh_hant: "回合開始時"
        },
        id: 1
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        1,
        1,
        1,
        1,
        0
      ],
      recastTurns: [
        4,
        4,
        4,
        4,
        3
      ]
    },
    params: [
      {
        icon: 14,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[5, 5, 5, 5, 6]ターン",
          en: "[5, 5, 5, 5, 6] Turn(s)",
          ko: "[5, 5, 5, 5, 6]턴",
          zh_hant: "[5, 5, 5, 5, 6]回合"
        },
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "すばやさアップ([22, 22, 24, 26, 30])",
          en: "Agility Up ([22, 22, 24, 26, 30])",
          ko: "민첩 증가([22, 22, 24, 26, 30])",
          zh_hant: "提升敏捷（[22, 22, 24, 26, 30]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
