# frozen_string_literal: true

MEMOIR_4000355 = {
  basicInfo: {
    cardID: "4000355",
    rarity: 4,
    charas: [
      102
    ],
    name: {
      ja: "ミルキィホームズ シャーロック＆ひかり",
      en: "<PERSON><PERSON> Holmes Sherlock & Hikari",
      ko: "밀키홈즈 셜록 & 히카리",
      zh_hant: "少女福爾摩斯 夏洛克&光"
    },
    profile: {
      ja: "お揃いの衣裳を纏ったシャロとひかり。\nふたりで難事件もスピード解決！？",
      en: "<PERSON><PERSON><PERSON> and <PERSON><PERSON> in matching costumes. Together they can speedily solve even the most difficult cases!",
      ko: "같은 의상을 걸친 샤로와 히카리.\n둘이서 어려운 사건들도 빠르게 해결?!",
      zh_hant: "身穿同款服裝的夏洛與光。\n只要兩人聯手，即使是棘手案件也能神速破案！？"
    },
    released: {
      ja: 1675666800,
      ww: 1678690800
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 22850,
    icon: 89,
    type: {
      ja: "開幕時",
      en: "At Start",
      ko: "개막",
      zh_hant: "開幕時"
    },
    params: [
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "キラめき回復([22, 24, 27, 29, 32])",
          en: "Brilliance Recovery ([22, 24, 27, 29, 32])",
          ko: "반짝임 회복([22, 24, 27, 29, 32])",
          zh_hant: "回復光芒（[22, 24, 27, 29, 32]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 29,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 1]ターン",
          en: "[1, 1, 1, 1, 1] Turn(s)",
          ko: "[1, 1, 1, 1, 1]턴",
          zh_hant: "[1, 1, 1, 1, 1]回合"
        },
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "毎ターンキラめき回復([10, 12, 14, 17, 20])",
          en: "Brilliance Regen ([10, 12, 14, 17, 20])",
          ko: "매 턴마다 반짝임 회복([10, 12, 14, 17, 20])",
          zh_hant: "每回合回復光芒（[10, 12, 14, 17, 20]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      3,
      3,
      3,
      3,
      2
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン開始時",
          en: "Start of Turn",
          ko: "턴 시작 시",
          zh_hant: "回合開始時"
        },
        id: 1
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        2,
        2,
        2,
        2,
        1
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 295,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "前から5体の味方",
          en: "5 Front Allies",
          ko: "앞에서 5명의 아군",
          zh_hant: "前面5名我方"
        },
        description: {
          ja: "変換[回数マイナス効果→起死回生]([1, 1, 1, 1, 2])",
          en: "Change [Count. Neg. Effects→Revive] ([1, 1, 1, 1, 2])",
          ko: "변환[횟수 마이너스 효과→기사회생]([1, 1, 1, 1, 2])",
          zh_hant: "變換[次數性負面效果→起死回生]（[1, 1, 1, 1, 2]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
