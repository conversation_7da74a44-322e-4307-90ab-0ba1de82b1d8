# frozen_string_literal: true

MEMOIR_4000284 = {
  basicInfo: {
    cardID: "4000284",
    rarity: 4,
    charas: [
      406,
      408
    ],
    name: {
      ja: "２人のレヴュー",
      en: "Revue of Two",
      ko: "두 사람의 레뷰",
      zh_hant: "兩人的Revue"
    },
    profile: {
      ja: "「舞台少女としての生存を賭けて──歌って、踊って、生き残りましょう」\n地下舞台へ誘われたステラと良子は、レヴューへの参加を決意する。",
      en: "\"Our lives as Stage Girls are on the line—let's sing, dance, and battle for survival.\"\nHaving been invited to the underground stage, <PERSON> and <PERSON><PERSON><PERSON> resolve to battle in a Revue.",
      ko: "[무대소녀로서의 생존을 걸고── 노래하고, 춤추고, 살아남자]\n지하 무대에 초대받은 스텔라와 료코는 레뷰에 참가할 것을 결심한다.",
      zh_hant: "「讓我們賭上作為舞台少女的生命——一起高歌、起舞並生存下去吧」\n被邀請至地下舞台的史黛拉與良子決定要參加Revue。"
    },
    released: {
      ja: 1657177200,
      ww: 1661151600
    }
  },
  stat: {
    total: 900,
    atk: 500,
    hp: 0,
    pdef: 0,
    mdef: 0
  },
  skill: {
    id: 10118,
    icon: 22,
    type: {
      ja: "永続",
      en: "Passive",
      ko: "영구",
      zh_hant: "永續"
    },
    params: [
      {
        icon: 22,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "クリティカル威力アップ([21, 23, 25, 27, 30])",
          en: "Critical Up ([21, 23, 25, 27, 30])",
          ko: "크리티컬 위력 증가([21, 23, 25, 27, 30])",
          zh_hant: "提升會心威力（[21, 23, 25, 27, 30]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 20,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "クリティカル率アップ([11, 12, 13, 14, 16])",
          en: "Dexterity Up ([11, 12, 13, 14, 16])",
          ko: "크리티컬 확률 증가([11, 12, 13, 14, 16])",
          zh_hant: "提升會心率（[11, 12, 13, 14, 16]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "味方の3番目のACT実行前",
          en: "Before 3rd Ally Act",
          ko: "아군의 세 번째 ACT 실행 전",
          zh_hant: "在我方發動第3個ACT前"
        },
        id: 1003
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        3,
        3,
        3,
        3,
        2
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 249,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "味方の3番目のACT実行者",
          en: "3rd Acting Ally",
          ko: "아군의 세 번째 ACT 실행자",
          zh_hant: "第3名發動ACT的我方"
        },
        description: {
          ja: "回数マイナス効果減少([1, 1, 1, 1, 2])",
          en: "Count. Neg. Effects Reduction ([1, 1, 1, 1, 2])",
          ko: "횟수 마이너스 효과 감소([1, 1, 1, 1, 2])",
          zh_hant: "減少次數性負面效果（[1, 1, 1, 1, 2]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
