# frozen_string_literal: true

MEMOIR_4000454 = {
  basicInfo: {
    cardID: "4000454",
    rarity: 4,
    charas: [
      107
    ],
    name: {
      ja: "華やかBirthday なな",
      en: "Gorgeous Birthday Nana",
      ko: "화려한 Birthday 나나",
      zh_hant: "華麗璀璨的生日 奈奈"
    },
    profile: {
      ja: "華やかな衣裳ととびきりの表情を纏い、舞台少女は誕生日もキラめいて。",
      en: "Dressed in glamorous costumes and with extraordinary expressions, Stage Girls also sparkle on their birthday.",
      ko: "화려한 의상과 눈부신 표정을 짓는 무대소녀는 생일에도 빛이 난다.",
      zh_hant: "身穿華麗衣裳，表情神采飛揚。即使在生日當天，舞台少女還是那麼璀璨耀眼。"
    },
    released: {
      ja: 1720710000,
      ww: 1720710000
    }
  },
  stat: {
    total: 2024,
    atk: 770,
    hp: 3380,
    pdef: 300,
    mdef: 300
  },
  skill: {
    id: 12619,
    icon: 379,
    type: {
      ja: "永続",
      en: "Passive",
      ko: "영구",
      zh_hant: "永續"
    },
    params: [
      {
        icon: 379,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "上位すばやさアップ([20, 22, 24, 26, 30])",
          en: "Greater Agility Up ([20, 22, 24, 26, 30])",
          ko: "상위 민첩 증가([20, 22, 24, 26, 30])",
          zh_hant: "[高級]提升敏捷（[20, 22, 24, 26, 30]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      3,
      3,
      3,
      3,
      2
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン開始時",
          en: "Start of Turn",
          ko: "턴 시작 시",
          zh_hant: "回合開始時"
        },
        id: 1
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        2,
        2,
        2,
        2,
        1
      ],
      recastTurns: [
        2,
        2,
        2,
        2,
        1
      ]
    },
    params: [
      {
        icon: 379,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]ターン",
          en: "[1, 1, 1, 1, 2] Turn(s)",
          ko: "[1, 1, 1, 1, 2]턴",
          zh_hant: "[1, 1, 1, 1, 2]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方全体",
          en: "All Allies",
          ko: "아군 전체",
          zh_hant: "所有我方"
        },
        description: {
          ja: "上位すばやさアップ([15, 15, 16, 17, 20])",
          en: "Greater Agility Up ([15, 15, 16, 17, 20])",
          ko: "상위 민첩 증가([15, 15, 16, 17, 20])",
          zh_hant: "[高級]提升敏捷（[15, 15, 16, 17, 20]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
