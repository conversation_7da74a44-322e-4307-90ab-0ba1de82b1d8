# frozen_string_literal: true

MEMOIR_4000399 = {
  basicInfo: {
    cardID: "4000399",
    rarity: 4,
    charas: [
      201
    ],
    name: {
      ja: "最強の音楽 レイヤ＆珠緒",
      en: "Strongest Music LAYER & Tamao",
      ko: "최강의 음악 레이야 & 타마오",
      zh_hant: "最強音樂 LAYER&珠緒"
    },
    profile: {
      ja: "最強の音楽を奏でる最強のバンド・RAISE A SUILENと舞台少女が夢の共演！\n背中合わせにベースを弾き歌うレイヤと珠緒。重低音がオーディエンスの心に響く。",
      en: "A dream collaboration between RAISE A SUILEN, the most powerful band playing the most powerful music, and Stage Girls! LAYER and <PERSON><PERSON> play the bass back to back and sing. The heavy bass sound echoes in the audience's hearts.",
      ko: "최강의 음악을 연주하는 최강의 밴드 RAISE A SUILEN과 무대소녀가 꿈의 합동 공연!\n등을 맞대고 베이스를 연주하며 노래하는 레이야와 타마오. 중저음이 청중들의 마음에 울려 퍼진다.",
      zh_hant: "演奏出最強音樂的最強樂團——RAISE A SUILEN與舞台少女的夢幻共演！\nLAYER與珠緒兩人背靠背，邊唱歌邊彈著手中的貝斯。那沉穩的重低音叩響了觀眾們的心。"
    },
    released: {
      ja: 1691564400,
      ww: 1694761200
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 12374,
    icon: 29,
    type: {
      ja: "永続",
      en: "Passive",
      ko: "영구",
      zh_hant: "永續"
    },
    params: [
      {
        icon: 29,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "毎ターンキラめき回復([10, 10, 10, 10, 15])",
          en: "Brilliance Regen ([10, 10, 10, 10, 15])",
          ko: "매 턴마다 반짝임 회복([10, 10, 10, 10, 15])",
          zh_hant: "每回合回復光芒（[10, 10, 10, 10, 15]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 14,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "すばやさアップ([25, 25, 25, 25, 35])",
          en: "Agility Up ([25, 25, 25, 25, 35])",
          ko: "민첩 증가([25, 25, 25, 25, 35])",
          zh_hant: "提升敏捷（[25, 25, 25, 25, 35]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "味方の1番目のACT実行前",
          en: "Before 1st Ally Act",
          ko: "아군의 첫 번째 ACT 실행 전",
          zh_hant: "在我方發動第1個ACT前"
        },
        id: 1001
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        3,
        3,
        3,
        3,
        2
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 10005,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "味方の1番目のACT実行者",
          en: "1st Acting Ally",
          ko: "아군의 첫 번째 ACT 실행자",
          zh_hant: "最先發動ACT的我方"
        },
        description: {
          ja: "継続マイナス効果解除",
          en: "Dispel Cont. Neg. Effects",
          ko: "지속 마이너스 효과 해제",
          zh_hant: "解除持續性負面效果"
        },
        descriptionExtra: nil
      },
      {
        icon: 26,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]ターン",
          en: "[1, 1, 1, 1, 2] Turn(s)",
          ko: "[1, 1, 1, 1, 2]턴",
          zh_hant: "[1, 1, 1, 1, 2]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方の1番目のACT実行者",
          en: "1st Acting Ally",
          ko: "아군의 첫 번째 ACT 실행자",
          zh_hant: "最先發動ACT的我方"
        },
        description: {
          ja: "継続マイナス効果耐性アップ([50, 50, 60, 80, 100])",
          en: "Cont. Neg. Effects Resistance Up ([50, 50, 60, 80, 100])",
          ko: "지속 마이너스 효과 저항 증가([50, 50, 60, 80, 100])",
          zh_hant: "提升持續性負面效果耐性（[50, 50, 60, 80, 100]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 313,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]回",
          en: "[1, 1, 1, 1, 2] Time(s)",
          ko: "[1, 1, 1, 1, 2]회",
          zh_hant: "[1, 1, 1, 1, 2]次"
        },
        accuracy: 100,
        target: {
          ja: "味方の1番目のACT実行者",
          en: "1st Acting Ally",
          ko: "아군의 첫 번째 ACT 실행자",
          zh_hant: "最先發動ACT的我方"
        },
        description: {
          ja: "ACTブースト[慢心]",
          en: "Act Boost [Impudence]",
          ko: "ACT 부스트[방심]",
          zh_hant: "ACT加成[傲慢]"
        },
        descriptionExtra: nil
      }
    ]
  }
}
