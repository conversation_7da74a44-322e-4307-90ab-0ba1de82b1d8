# frozen_string_literal: true

MEMOIR_4000398 = {
  basicInfo: {
    cardID: "4000398",
    rarity: 4,
    charas: [
      101,
      102
    ],
    name: {
      ja: "あんたのキラめきで",
      en: "With Your Brilliance",
      ko: "당신의 반짝임으로",
      zh_hant: "用妳的光芒"
    },
    profile: {
      ja: "――『劇場版 少女☆歌劇 レヴュースタァライト』より",
      en: "From [Revue Starlight: The Movie]",
      ko: "──[극장판 소녀☆가극 레뷰 스타라이트]에서",
      zh_hant: "——取自『劇場版 少女☆歌劇 Revue Starlight』"
    },
    released: {
      ja: 1690124400,
      ww: 1690124400
    }
  },
  stat: {
    total: 10500,
    atk: 2500,
    hp: 40000,
    pdef: 2000,
    mdef: 2000
  },
  skill: {
    id: 12365,
    icon: 8,
    type: {
      ja: "永続",
      en: "Passive",
      ko: "영구",
      zh_hant: "永續"
    },
    params: [
      {
        icon: 8,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "味方の風属性",
          en: "Wind Allies",
          ko: "아군 바람 속성",
          zh_hant: "風屬性的我方"
        },
        description: {
          ja: "ACTパワーアップ([7, 7, 8, 9, 10])",
          en: "Act Power Up ([7, 7, 8, 9, 10])",
          ko: "ACT 파워 증가([7, 7, 8, 9, 10])",
          zh_hant: "提升ACT力量（[7, 7, 8, 9, 10]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 29,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "毎ターンキラめき回復([7, 7, 8, 9, 10])",
          en: "Brilliance Regen ([7, 7, 8, 9, 10])",
          ko: "매 턴마다 반짝임 회복([7, 7, 8, 9, 10])",
          zh_hant: "每回合回復光芒（[7, 7, 8, 9, 10]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン開始時",
          en: "Start of Turn",
          ko: "턴 시작 시",
          zh_hant: "回合開始時"
        },
        id: 1
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        3,
        3,
        3,
        3,
        2
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 39,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[2, 2, 2, 2, 3]ターン",
          en: "[2, 2, 2, 2, 3] Turn(s)",
          ko: "[2, 2, 2, 2, 3]턴",
          zh_hant: "[2, 2, 2, 2, 3]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方の風属性",
          en: "Wind Allies",
          ko: "아군 바람 속성",
          zh_hant: "風屬性的我方"
        },
        description: {
          ja: "有利属性ダメージアップ([15, 15, 20, 25, 30])",
          en: "Effective Element Dmg Up ([15, 15, 20, 25, 30])",
          ko: "유리한 속성 대미지 증가([15, 15, 20, 25, 30])",
          zh_hant: "提升有利屬性傷害（[15, 15, 20, 25, 30]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 22,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[2, 2, 2, 2, 3]ターン",
          en: "[2, 2, 2, 2, 3] Turn(s)",
          ko: "[2, 2, 2, 2, 3]턴",
          zh_hant: "[2, 2, 2, 2, 3]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方の風属性",
          en: "Wind Allies",
          ko: "아군 바람 속성",
          zh_hant: "風屬性的我方"
        },
        description: {
          ja: "クリティカル威力アップ([15, 15, 20, 25, 30])",
          en: "Critical Up ([15, 15, 20, 25, 30])",
          ko: "크리티컬 위력 증가([15, 15, 20, 25, 30])",
          zh_hant: "提升會心威力（[15, 15, 20, 25, 30]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
