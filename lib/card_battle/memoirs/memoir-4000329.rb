# frozen_string_literal: true

MEMOIR_4000329 = {
  basicInfo: {
    cardID: "4000329",
    rarity: 4,
    charas: [
      104,
      105
    ],
    name: {
      ja: "やさしい眼差し",
      en: "Kind Glance",
      ko: "상냥한 눈빛",
      zh_hant: "溫和的眼神"
    },
    profile: {
      ja: "運命を乗り越え結ばれた２人。\nこの幸せが永遠に続けばいいと思っていた――",
      en: "Having overcome all odds, they're finally united together, but alas how long will their happiness last...",
      ko: "운명을 초월해 이어진 두 사람.\n이 행복이 영원히 이어지길 바랐다――",
      zh_hant: "跨越命運而相連的2人。\n本以為這份幸福會一直持續下去——"
    },
    released: {
      ja: 1667718000,
      ww: 1670569200
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 10123,
    icon: 20,
    type: {
      ja: "永続",
      en: "Passive",
      ko: "영구",
      zh_hant: "永續"
    },
    params: [
      {
        icon: 20,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "クリティカル率アップ([11, 12, 13, 14, 16])",
          en: "Dexterity Up ([11, 12, 13, 14, 16])",
          ko: "크리티컬 확률 증가([11, 12, 13, 14, 16])",
          zh_hant: "提升會心率（[11, 12, 13, 14, 16]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 39,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "有利属性ダメージアップ([8, 9, 10, 11, 12])",
          en: "Effective Element Dmg Up ([8, 9, 10, 11, 12])",
          ko: "유리한 속성 대미지 증가([8, 9, 10, 11, 12])",
          zh_hant: "提升有利屬性傷害（[8, 9, 10, 11, 12]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      3,
      3,
      3,
      3,
      2
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン開始時",
          en: "Start of Turn",
          ko: "턴 시작 시",
          zh_hant: "回合開始時"
        },
        id: 1
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        2,
        2,
        2,
        2,
        1
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 22,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]ターン",
          en: "[1, 1, 1, 1, 2] Turn(s)",
          ko: "[1, 1, 1, 1, 2]턴",
          zh_hant: "[1, 1, 1, 1, 2]回合"
        },
        accuracy: 100,
        target: {
          ja: "前から5体の味方",
          en: "5 Front Allies",
          ko: "앞에서 5명의 아군",
          zh_hant: "前面5名我方"
        },
        description: {
          ja: "クリティカル威力アップ([20, 20, 25, 30, 40])",
          en: "Critical Up ([20, 20, 25, 30, 40])",
          ko: "크리티컬 위력 증가([20, 20, 25, 30, 40])",
          zh_hant: "提升會心威力（[20, 20, 25, 30, 40]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
