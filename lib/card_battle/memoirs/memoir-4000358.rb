# frozen_string_literal: true

MEMOIR_4000358 = {
  basicInfo: {
    cardID: "4000358",
    rarity: 4,
    charas: [
      101
    ],
    name: {
      ja: "ミルキィホームズ ネロ＆華恋",
      en: "<PERSON><PERSON> Nero & Karen",
      ko: "밀키홈즈 네로 & 카렌",
      zh_hant: "少女福爾摩斯 妮洛&華戀"
    },
    profile: {
      ja: "お揃いの衣裳を纏ったネロと華恋。\n糖分補給もバッチリでいざ、怪盗を追え！",
      en: "<PERSON> and <PERSON> in matching costumes. Now that they've had their sugar fix, it's time to chase the thieves!",
      ko: "같은 의상을 걸친 네로와 카렌.\n당분 보충도 충분하니 자, 괴도를 쫓자!",
      zh_hant: "身穿同款服裝的妮洛與華戀。\n已充分補充糖分，馬上追捕怪盜吧！"
    },
    released: {
      ja: 1676703600,
      ww: 1679727600
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 22850,
    icon: 89,
    type: {
      ja: "開幕時",
      en: "At Start",
      ko: "개막",
      zh_hant: "開幕時"
    },
    params: [
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "キラめき回復([22, 24, 27, 29, 32])",
          en: "Brilliance Recovery ([22, 24, 27, 29, 32])",
          ko: "반짝임 회복([22, 24, 27, 29, 32])",
          zh_hant: "回復光芒（[22, 24, 27, 29, 32]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 29,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 1]ターン",
          en: "[1, 1, 1, 1, 1] Turn(s)",
          ko: "[1, 1, 1, 1, 1]턴",
          zh_hant: "[1, 1, 1, 1, 1]回合"
        },
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "毎ターンキラめき回復([10, 12, 14, 17, 20])",
          en: "Brilliance Regen ([10, 12, 14, 17, 20])",
          ko: "매 턴마다 반짝임 회복([10, 12, 14, 17, 20])",
          zh_hant: "每回合回復光芒（[10, 12, 14, 17, 20]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      3,
      3,
      3,
      3,
      2
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン終了時",
          en: "End of Turn",
          ko: "턴 종료 시",
          zh_hant: "回合結束時"
        },
        id: 2
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        2,
        2,
        2,
        2,
        1
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "味方全体",
          en: "All Allies",
          ko: "아군 전체",
          zh_hant: "所有我方"
        },
        description: {
          ja: "キラめき回復([10, 10, 11, 13, 15])",
          en: "Brilliance Recovery ([10, 10, 11, 13, 15])",
          ko: "반짝임 회복([10, 10, 11, 13, 15])",
          zh_hant: "回復光芒（[10, 10, 11, 13, 15]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 178,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[2, 2, 2, 2, 2]ターン",
          en: "[2, 2, 2, 2, 2] Turn(s)",
          ko: "[2, 2, 2, 2, 2]턴",
          zh_hant: "[2, 2, 2, 2, 2]回合"
        },
        accuracy: 100,
        target: {
          ja: "敵役全体",
          en: "All Enemies",
          ko: "적 전체",
          zh_hant: "所有敵方"
        },
        description: {
          ja: "ACT3封印",
          en: "Seal Act 3",
          ko: "ACT3 봉인",
          zh_hant: "ACT3封印"
        },
        descriptionExtra: nil
      }
    ]
  }
}
