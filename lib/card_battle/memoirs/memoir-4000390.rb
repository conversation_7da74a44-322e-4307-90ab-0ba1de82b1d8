# frozen_string_literal: true

MEMOIR_4000390 = {
  basicInfo: {
    cardID: "4000390",
    rarity: 4,
    charas: [
      108,
      109
    ],
    name: {
      ja: "列車に揺られて",
      en: "Riding the Train",
      ko: "열차에 몸을 맡기고",
      zh_hant: "搖晃的列車"
    },
    profile: {
      ja: "「なあ、香子――って、寝ちまったのか」\nいつの間にか隣で寝息を立てる香子を起こさないよう、そっと窓の外に目をやる双葉。車窓に広がる故郷の景色は、ふたりの帰りを歓迎しているようだ。",
      en: "\"Hey, Kaoruko... Are you sleeping?\"\n<PERSON><PERSON><PERSON> quietly looks out the window so as not to wake up <PERSON><PERSON><PERSON><PERSON>, who is sleeping next to her. The view of her hometown spread out from the car window seems to welcome them back.",
      ko: "[있잖아, 카오루코── 뭐야, 잠들었네]\n어느샌가 옆에서 곤히 잠든 카오루코가 깨지 않게 살며시 창밖으로 눈을 돌리는 후타바. 차창 밖으로 펼쳐진 고향의 풍경은 두 사람이 돌아온 것을 환영하는 듯하다.",
      zh_hant: "「對了，香子――啊，睡著了啊」\n為了不吵醒不知何時墜入夢鄉的香子，雙葉緩緩地將目光移向窗外。車窗外無邊的故鄉景色，似乎正在歡迎兩人歸來。"
    },
    released: {
      ja: 1682060400,
      ww: 1684220400
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 22850,
    icon: 89,
    type: {
      ja: "開幕時",
      en: "At Start",
      ko: "개막",
      zh_hant: "開幕時"
    },
    params: [
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "キラめき回復([22, 24, 27, 29, 32])",
          en: "Brilliance Recovery ([22, 24, 27, 29, 32])",
          ko: "반짝임 회복([22, 24, 27, 29, 32])",
          zh_hant: "回復光芒（[22, 24, 27, 29, 32]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 29,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 1]ターン",
          en: "[1, 1, 1, 1, 1] Turn(s)",
          ko: "[1, 1, 1, 1, 1]턴",
          zh_hant: "[1, 1, 1, 1, 1]回合"
        },
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "毎ターンキラめき回復([10, 12, 14, 17, 20])",
          en: "Brilliance Regen ([10, 12, 14, 17, 20])",
          ko: "매 턴마다 반짝임 회복([10, 12, 14, 17, 20])",
          zh_hant: "每回合回復光芒（[10, 12, 14, 17, 20]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      3,
      3,
      3,
      3,
      2
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン開始時",
          en: "Start of Turn",
          ko: "턴 시작 시",
          zh_hant: "回合開始時"
        },
        id: 1
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        2,
        2,
        2,
        2,
        1
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 251,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]ターン",
          en: "[1, 1, 1, 1, 2] Turn(s)",
          ko: "[1, 1, 1, 1, 2]턴",
          zh_hant: "[1, 1, 1, 1, 2]回合"
        },
        accuracy: 100,
        target: {
          ja: "前から5体の味方",
          en: "5 Front Allies",
          ko: "앞에서 5명의 아군",
          zh_hant: "前面5名我方"
        },
        description: {
          ja: "キラめき獲得アップ([20, 20, 20, 20, 30])",
          en: "Brilliance Recovery Increase ([20, 20, 20, 20, 30])",
          ko: "반짝임 획득 증가([20, 20, 20, 20, 30])",
          zh_hant: "提升光芒獲得量（[20, 20, 20, 20, 30]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
