# frozen_string_literal: true

MEMOIR_4000291 = {
  basicInfo: {
    cardID: "4000291",
    rarity: 4,
    charas: [
      101,
      102
    ],
    name: {
      ja: "お揃いのカーディガン",
      en: "Matching Cardigans",
      ko: "똑같은 가디건",
      zh_hant: "同款的開襟外套"
    },
    profile: {
      ja: "「わあ、きれいだね、ひかりちゃん！」\nお揃いのカーディガンを身に纏い、イルミネーションを眺める華恋とひかり。いつもよりもっと仲良く過ごせそう……！？",
      en: "\"Wow, isn't it beautiful, <PERSON><PERSON>-chan?\"\n<PERSON><PERSON><PERSON> and <PERSON><PERSON> are looking at the lights in matching cardigans. Maybe it'll bring the two of them even closer...!?",
      ko: "[우와, 예쁘네, 히카리!]\n똑같은 카디건을 몸에 걸치고 일루미네이션을 바라보는 카렌과 히카리. 평소보다도 더 사이좋게 지낼 수 있을 것만 같은데...?!",
      zh_hant: "「哇，好漂亮喔，小光！」\n華戀與光穿著同款的開襟外套，一起觀賞燈飾。她們似乎比平時變得更要好了……？"
    },
    released: {
      ja: 1659942000,
      ww: 1662447600
    }
  },
  stat: {
    total: 800,
    atk: 0,
    hp: 0,
    pdef: 800,
    mdef: 800
  },
  skill: {
    id: 20020,
    icon: 89,
    type: {
      ja: "開幕時",
      en: "At Start",
      ko: "개막",
      zh_hant: "開幕時"
    },
    params: [
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "キラめき回復([28, 31, 34, 37, 40])",
          en: "Brilliance Recovery ([28, 31, 34, 37, 40])",
          ko: "반짝임 회복([28, 31, 34, 37, 40])",
          zh_hant: "回復光芒（[28, 31, 34, 37, 40]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      3,
      3,
      3,
      3,
      2
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "味方の3番目のACT実行前",
          en: "Before 3rd Ally Act",
          ko: "아군의 세 번째 ACT 실행 전",
          zh_hant: "在我方發動第3個ACT前"
        },
        id: 1003
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        2,
        2,
        2,
        2,
        1
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 26,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]ターン",
          en: "[1, 1, 1, 1, 2] Turn(s)",
          ko: "[1, 1, 1, 1, 2]턴",
          zh_hant: "[1, 1, 1, 1, 2]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方の3番目のACT実行者",
          en: "3rd Acting Ally",
          ko: "아군의 세 번째 ACT 실행자",
          zh_hant: "第3名發動ACT的我方"
        },
        description: {
          ja: "継続マイナス効果耐性アップ([100, 100, 100, 100, 100])",
          en: "Cont. Neg. Effects Resistance Up ([100, 100, 100, 100, 100])",
          ko: "지속 마이너스 효과 저항 증가([100, 100, 100, 100, 100])",
          zh_hant: "提升持續性負面效果耐性（[100, 100, 100, 100, 100]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
