# frozen_string_literal: true

MEMOIR_4000401 = {
  basicInfo: {
    cardID: "4000401",
    rarity: 4,
    charas: [
      202
    ],
    name: {
      ja: "最強の音楽 マスキング＆いちえ",
      en: "Strongest Music MASKING & Ichie",
      ko: "최강의 음악 마스킹 & 이치에",
      zh_hant: "最強音樂 MASKING&一愛"
    },
    profile: {
      ja: "最強の音楽を奏でる最強のバンド・RAISE A SUILENと舞台少女が夢の共演！\nかわいいもの好きのマスキングとかわいいいちえの凸凹コンビが心地良いビートを刻む！",
      en: "A dream collaboration between RAISE A SUILEN, the most powerful band playing the most powerful music, and Stage Girls! MASKING loves cute things, and <PERSON><PERSON><PERSON> is cute—this odd duo keeps the rhythm flowing!",
      ko: "최강의 음악을 연주하는 최강의 밴드 RAISE A SUILEN과 무대소녀가 꿈의 합동 공연!\n귀여운 것을 좋아하는 마스킹과 귀여운 이치에, 언밸런스 콤비가 기분 좋은 비트를 만들어낸다!",
      zh_hant: "演奏出最強音樂的最強樂團——RAISE A SUILEN與舞台少女的夢幻共演！\n喜歡可愛事物的MASKING與可愛的一愛將成為彼此互補的最佳拍檔，並敲打出張力十足的完美節奏！"
    },
    released: {
      ja: 1692370800,
      ww: 1695279600
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 12374,
    icon: 29,
    type: {
      ja: "永続",
      en: "Passive",
      ko: "영구",
      zh_hant: "永續"
    },
    params: [
      {
        icon: 29,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "毎ターンキラめき回復([10, 10, 10, 10, 15])",
          en: "Brilliance Regen ([10, 10, 10, 10, 15])",
          ko: "매 턴마다 반짝임 회복([10, 10, 10, 10, 15])",
          zh_hant: "每回合回復光芒（[10, 10, 10, 10, 15]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 14,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "すばやさアップ([25, 25, 25, 25, 35])",
          en: "Agility Up ([25, 25, 25, 25, 35])",
          ko: "민첩 증가([25, 25, 25, 25, 35])",
          zh_hant: "提升敏捷（[25, 25, 25, 25, 35]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "味方の1番目のACT実行前",
          en: "Before 1st Ally Act",
          ko: "아군의 첫 번째 ACT 실행 전",
          zh_hant: "在我方發動第1個ACT前"
        },
        id: 1001
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        3,
        3,
        3,
        3,
        2
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 26,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]ターン",
          en: "[1, 1, 1, 1, 2] Turn(s)",
          ko: "[1, 1, 1, 1, 2]턴",
          zh_hant: "[1, 1, 1, 1, 2]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方全体",
          en: "All Allies",
          ko: "아군 전체",
          zh_hant: "所有我方"
        },
        description: {
          ja: "継続マイナス効果耐性アップ([50, 50, 60, 80, 100])",
          en: "Cont. Neg. Effects Resistance Up ([50, 50, 60, 80, 100])",
          ko: "지속 마이너스 효과 저항 증가([50, 50, 60, 80, 100])",
          zh_hant: "提升持續性負面效果耐性（[50, 50, 60, 80, 100]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 250,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]ターン",
          en: "[1, 1, 1, 1, 2] Turn(s)",
          ko: "[1, 1, 1, 1, 2]턴",
          zh_hant: "[1, 1, 1, 1, 2]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方全体",
          en: "All Allies",
          ko: "아군 전체",
          zh_hant: "所有我方"
        },
        description: {
          ja: "回数マイナス効果耐性アップ([50, 50, 60, 80, 100])",
          en: "Count. Neg. Effects Resistance Up ([50, 50, 60, 80, 100])",
          ko: "횟수 마이너스 효과 저항 증가([50, 50, 60, 80, 100])",
          zh_hant: "提升次數性負面效果耐性（[50, 50, 60, 80, 100]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 288,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]回",
          en: "[1, 1, 1, 1, 2] Time(s)",
          ko: "[1, 1, 1, 1, 2]회",
          zh_hant: "[1, 1, 1, 1, 2]次"
        },
        accuracy: 100,
        target: {
          ja: "味方全体",
          en: "All Allies",
          ko: "아군 전체",
          zh_hant: "所有我方"
        },
        description: {
          ja: "無敵の再生者([100, 100, 100, 100, 100])",
          en: "Invincible Rebirth ([100, 100, 100, 100, 100])",
          ko: "무적의 재생자([100, 100, 100, 100, 100])",
          zh_hant: "無敵重生者（[100, 100, 100, 100, 100]）"
        },
        descriptionExtra: {
          ja: "回復量は対象の最大HPに依存する",
          en: "Recovered Amount Based on Target's Max HP",
          ko: "회복량은 대상의 최대 HP에 따라 변동",
          zh_hant: "回復量將依據回復對象的最大HP而定"
        }
      }
    ]
  }
}
