# frozen_string_literal: true

MEMOIR_4000278 = {
  basicInfo: {
    cardID: "4000278",
    rarity: 4,
    charas: [
      201,
      204
    ],
    name: {
      ja: "死神と吊られた男のレヴュー",
      en: "Revue of Death and Hanged Man",
      ko: "사신과 매달린 사람의 레뷰",
      zh_hant: "死神與倒吊人的Revue"
    },
    profile: {
      ja: "死神と吊られた男のレヴューに魅せられ、少女えるのペンが走り出す。\n小さな胸を鳴らし、未だ見ぬ戯曲の完成を目指して。",
      en: "Charmed by the Revue of Death and Hanged Man, young <PERSON>'s pen cannot stop writing. With her heart banging in her chest, she seeks to finish the play that hasn't taken form yet.",
      ko: "사신과 매달린 사람의 레뷰에 매료되어 소녀 엘의 펜이 춤춘다.\n두근대는 가슴으로 아직 본 적 없는 희곡의 완성을 꿈꾸며.",
      zh_hant: "在見到死神與倒吊人的Revue後，少女艾露開始振筆直書。\n嬌小的胸懷情緒高漲，目標是要完成前所未聞的戲碼。"
    },
    released: {
      ja: 1655650800,
      ww: 1657656000
    }
  },
  stat: {
    total: 900,
    atk: 500,
    hp: 0,
    pdef: 0,
    mdef: 0
  },
  skill: {
    id: 10080,
    icon: 20,
    type: {
      ja: "永続",
      en: "Passive",
      ko: "영구",
      zh_hant: "永續"
    },
    params: [
      {
        icon: 20,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "クリティカル率アップ([14, 15, 17, 18, 20])",
          en: "Dexterity Up ([14, 15, 17, 18, 20])",
          ko: "크리티컬 확률 증가([14, 15, 17, 18, 20])",
          zh_hant: "提升會心率（[14, 15, 17, 18, 20]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      7,
      7,
      7,
      7,
      6
    ],
    attribute: 2,
    execution: {
      executeTiming: {
        description: {
          ja: "敵の1番目のACT実行前",
          en: "Before 1st Enemy Act",
          ko: "적의 첫 번째 ACT 실행 전",
          zh_hant: "在敵方發動第1個ACT前"
        },
        id: 2001
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        5,
        5,
        5,
        5,
        4
      ],
      recastTurns: [
        5,
        5,
        5,
        5,
        4
      ]
    },
    params: [
      {
        icon: 1,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "敵役の1番目のACT実行者",
          en: "1st Acting Enemy",
          ko: "적의 첫 번째 ACT 실행자",
          zh_hant: "最先發動ACT的敵方"
        },
        description: {
          ja: "風属性攻撃(威力[240, 240, 280, 320, 360])",
          en: "Wind Dmg ([240, 240, 280, 320, 360])",
          ko: "바람 속성 공격(위력 [240, 240, 280, 320, 360])",
          zh_hant: "風屬性攻擊（威力[240, 240, 280, 320, 360]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
