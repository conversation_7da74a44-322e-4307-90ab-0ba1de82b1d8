# frozen_string_literal: true

MEMOIR_4000348 = {
  basicInfo: {
    cardID: "4000348",
    rarity: 4,
    charas: [
      802
    ],
    name: {
      ja: "XVII 星【逆位置】",
      en: "XVII Star [Reverse]",
      ko: "XVII 별[역위치]",
      zh_hant: "XVII 星星【逆位】"
    },
    profile: {
      ja: "絶望、幻滅、高すぎる理想",
      en: "Despair, Disillusionment, Unachievable Ideals",
      ko: "절망, 환멸, 지나치게 높은 이상",
      zh_hant: "絕望、幻滅、理想過高"
    },
    released: {
      ja: 1674370800,
      ww: 1676876400
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 22831,
    icon: 29,
    type: {
      ja: "開幕時",
      en: "At Start",
      ko: "개막",
      zh_hant: "開幕時"
    },
    params: [
      {
        icon: 29,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 1]ターン",
          en: "[1, 1, 1, 1, 1] Turn(s)",
          ko: "[1, 1, 1, 1, 1]턴",
          zh_hant: "[1, 1, 1, 1, 1]回合"
        },
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "毎ターンキラめき回復([28, 31, 34, 37, 40])",
          en: "Brilliance Regen ([28, 31, 34, 37, 40])",
          ko: "매 턴마다 반짝임 회복([28, 31, 34, 37, 40])",
          zh_hant: "每回合回復光芒（[28, 31, 34, 37, 40]）"
        },
        descriptionExtra: {
          ja: "解除不可",
          en: "Unremovable",
          ko: "해제 불가",
          zh_hant: "無法解除"
        }
      },
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "キラめき回復([28, 31, 34, 37, 40])",
          en: "Brilliance Recovery ([28, 31, 34, 37, 40])",
          ko: "반짝임 회복([28, 31, 34, 37, 40])",
          zh_hant: "回復光芒（[28, 31, 34, 37, 40]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      3,
      3,
      3,
      3,
      2
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン開始時",
          en: "Start of Turn",
          ko: "턴 시작 시",
          zh_hant: "回合開始時"
        },
        id: 1
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        2,
        2,
        2,
        2,
        1
      ],
      recastTurns: [
        2,
        2,
        2,
        2,
        1
      ]
    },
    params: [
      {
        icon: 249,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "後ろから5体の味方",
          en: "5 Rear Allies",
          ko: "뒤에서 5명의 아군",
          zh_hant: "後面5名我方"
        },
        description: {
          ja: "回数マイナス効果減少([1, 1, 1, 1, 2])",
          en: "Count. Neg. Effects Reduction ([1, 1, 1, 1, 2])",
          ko: "횟수 마이너스 효과 감소([1, 1, 1, 1, 2])",
          zh_hant: "減少次數性負面效果（[1, 1, 1, 1, 2]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 250,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]ターン",
          en: "[1, 1, 1, 1, 2] Turn(s)",
          ko: "[1, 1, 1, 1, 2]턴",
          zh_hant: "[1, 1, 1, 1, 2]回合"
        },
        accuracy: 100,
        target: {
          ja: "後ろから5体の味方",
          en: "5 Rear Allies",
          ko: "뒤에서 5명의 아군",
          zh_hant: "後面5名我方"
        },
        description: {
          ja: "回数マイナス効果耐性アップ([50, 50, 55, 60, 70])",
          en: "Count. Neg. Effects Resistance Up ([50, 50, 55, 60, 70])",
          ko: "횟수 마이너스 효과 저항 증가([50, 50, 55, 60, 70])",
          zh_hant: "提升次數性負面效果耐性（[50, 50, 55, 60, 70]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
