# frozen_string_literal: true

MEMOIR_4000480 = {
  basicInfo: {
    cardID: "4000480",
    rarity: 4,
    charas: [
      107,
      503
    ],
    name: {
      ja: "『ソロモンの指輪』",
      en: "\"King <PERSON>'s Ring\"",
      ko: "[솔로몬의 반지]",
      zh_hant: "『所羅門的戒指』"
    },
    profile: {
      ja: "世界のあらゆる問に解を授けたソロモン王のもとへ、シバ国から女王が訪れる。シバの女王はソロモン王の手に輝く指輪を賭け、３つの問を出す。",
      en: "King <PERSON>, who had answered all the questions of the world, is visited by the queen of Sheba. She wagers his ring against three riddles.",
      ko: "세계의 모든 물음에 해답을 내려준 솔로몬 왕에게 시바 왕국의 여왕이 그를 방문한다. 시바의 여왕은 솔로몬 왕의 손에서 빛나는 반지를 걸고, 세 가지 수수께끼를 낸다.",
      zh_hant: "來自示巴王國的女王親自拜訪了能解答世上任何問題的所羅門王。示巴女王以所羅門王手上閃耀著的戒指為賭注，提出了三個謎題。"
    },
    released: {
      ja: 1713855600,
      ww: 1716447600
    }
  },
  stat: {
    total: 1630,
    atk: 350,
    hp: 4000,
    pdef: 600,
    mdef: 600
  },
  skill: {
    id: 23151,
    icon: 0,
    type: {
      ja: "開幕時",
      en: "At Start",
      ko: "개막",
      zh_hant: "開幕時"
    },
    params: [
      {
        icon: 89,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "キラめき回復([22, 24, 27, 29, 32])",
          en: "Brilliance Recovery ([22, 24, 27, 29, 32])",
          ko: "반짝임 회복([22, 24, 27, 29, 32])",
          zh_hant: "回復光芒（[22, 24, 27, 29, 32]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 347,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]回",
          en: "[1, 1, 1, 1, 2] Time(s)",
          ko: "[1, 1, 1, 1, 2]회",
          zh_hant: "[1, 1, 1, 1, 2]次"
        },
        accuracy: 100,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "上位不屈",
          en: "Greater Fortitude",
          ko: "상위 불굴",
          zh_hant: "高級不屈"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "敵の1番目のACT実行前",
          en: "Before 1st Enemy Act",
          ko: "적의 첫 번째 ACT 실행 전",
          zh_hant: "在敵方發動第1個ACT前"
        },
        id: 2001
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        3,
        3,
        3,
        3,
        2
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 441,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[1, 1, 1, 1, 2]ターン",
          en: "[1, 1, 1, 1, 2] Turn(s)",
          ko: "[1, 1, 1, 1, 2]턴",
          zh_hant: "[1, 1, 1, 1, 2]回合"
        },
        accuracy: 100,
        target: {
          ja: "敵役の1番目のACT実行者",
          en: "1st Acting Enemy",
          ko: "적의 첫 번째 ACT 실행자",
          zh_hant: "最先發動ACT的敵方"
        },
        description: {
          ja: "上位スランプ",
          en: "Greater Slump",
          ko: "상위 슬럼프",
          zh_hant: "高級萎靡"
        },
        descriptionExtra: nil
      }
    ]
  }
}
