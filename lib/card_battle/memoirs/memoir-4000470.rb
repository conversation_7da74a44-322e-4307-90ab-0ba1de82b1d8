# frozen_string_literal: true

MEMOIR_4000470 = {
  basicInfo: {
    cardID: "4000470",
    rarity: 4,
    charas: [
      101
    ],
    name: {
      ja: "ひかりに負けたくない",
      en: "I Don't Want to Lose to Hi<PERSON>",
      ko: "히카리에게 지고 싶지 않아",
      zh_hant: "不想輸給光"
    },
    profile: {
      ja: "――『劇場版 少女☆歌劇 レヴュースタァライト』より",
      en: "From [Revue Starlight: The Movie]",
      ko: "──[극장판 소녀☆가극 레뷰 스타라이트]에서",
      zh_hant: "——取自『劇場版 少女☆歌劇 Revue Starlight』"
    },
    released: {
      ja: 1705158000,
      ww: 1705158000
    }
  },
  stat: {
    total: 10500,
    atk: 2500,
    hp: 40000,
    pdef: 2000,
    mdef: 2000
  },
  skill: {
    id: 12461,
    icon: 391,
    type: {
      ja: "永続",
      en: "Passive",
      ko: "영구",
      zh_hant: "永續"
    },
    params: [
      {
        icon: 364,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "味方の宙属性",
          en: "Space Allies",
          ko: "아군 우주 속성",
          zh_hant: "宙屬性的我方"
        },
        description: {
          ja: "上位クリティカル威力アップ([7, 7, 8, 9, 10])",
          en: "Greater Critical Up ([7, 7, 8, 9, 10])",
          ko: "상위 크리티컬 위력 증가([7, 7, 8, 9, 10])",
          zh_hant: "[高級]提升會心威力（[7, 7, 8, 9, 10]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 391,
        type: "normal",
        hits: nil,
        duration: nil,
        accuracy: nil,
        target: {
          ja: "自身",
          en: "Self",
          ko: "자신",
          zh_hant: "自己"
        },
        description: {
          ja: "上位毎ターンキラめき回復([7, 7, 8, 9, 10])",
          en: "Greater Brilliance Regen ([7, 7, 8, 9, 10])",
          ko: "상위 매 턴마다 반짝임 회복([7, 7, 8, 9, 10])",
          zh_hant: "高級每回合光芒回復（[7, 7, 8, 9, 10]）"
        },
        descriptionExtra: nil
      }
    ]
  },
  activeSkill: {
    cost: [
      4,
      4,
      4,
      4,
      3
    ],
    attribute: 99,
    execution: {
      executeTiming: {
        description: {
          ja: "ターン開始時",
          en: "Start of Turn",
          ko: "턴 시작 시",
          zh_hant: "回合開始時"
        },
        id: 1
      },
      executeLimitCounts: [
        1,
        1,
        1,
        1,
        2
      ],
      firstExecutableTurns: [
        3,
        3,
        3,
        3,
        2
      ],
      recastTurns: [
        3,
        3,
        3,
        3,
        2
      ]
    },
    params: [
      {
        icon: 365,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[2, 2, 2, 2, 3]ターン",
          en: "[2, 2, 2, 2, 3] Turn(s)",
          ko: "[2, 2, 2, 2, 3]턴",
          zh_hant: "[2, 2, 2, 2, 3]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方の宙属性",
          en: "Space Allies",
          ko: "아군 우주 속성",
          zh_hant: "宙屬性的我方"
        },
        description: {
          ja: "上位有利属性ダメージアップ([10, 10, 12, 15, 20])",
          en: "Greater Effective Element Dmg Up ([10, 10, 12, 15, 20])",
          ko: "상위 유리한 속성 대미지 증가([10, 10, 12, 15, 20])",
          zh_hant: "[高級]提升有利屬性傷害（[10, 10, 12, 15, 20]）"
        },
        descriptionExtra: nil
      },
      {
        icon: 364,
        type: "normal",
        hits: nil,
        duration: {
          ja: "[2, 2, 2, 2, 3]ターン",
          en: "[2, 2, 2, 2, 3] Turn(s)",
          ko: "[2, 2, 2, 2, 3]턴",
          zh_hant: "[2, 2, 2, 2, 3]回合"
        },
        accuracy: 100,
        target: {
          ja: "味方の宙属性",
          en: "Space Allies",
          ko: "아군 우주 속성",
          zh_hant: "宙屬性的我方"
        },
        description: {
          ja: "上位クリティカル威力アップ([10, 10, 12, 15, 20])",
          en: "Greater Critical Up ([10, 10, 12, 15, 20])",
          ko: "상위 크리티컬 위력 증가([10, 10, 12, 15, 20])",
          zh_hant: "[高級]提升會心威力（[10, 10, 12, 15, 20]）"
        },
        descriptionExtra: nil
      }
    ]
  }
}
