require_relative 'card_battle/version'

# Database system
require_relative 'card_battle/database/database_manager'
require_relative 'card_battle/database/stage_girls_database'
require_relative 'card_battle/database/memoirs_database'
require_relative 'card_battle/database/accessories_database'
require_relative 'card_battle/database/migration_manager'

# Database-backed classes (new default)
require_relative 'card_battle/database_card_database'
require_relative 'card_battle/database_stage_girl_database'

# Load card data files
Dir[File.join(__dir__, 'card_battle/cards/*.rb')].each { |file| require file }

module CardBattle
  class Error < StandardError; end

  # Revue Starlight: ReLive Battle System

  # Database convenience methods
  def self.initialize_databases
    Database::DatabaseManager.initialize_databases
  end

  def self.stage_girls_db
    Database::DatabaseManager.stage_girls_db
  end

  def self.memoirs_db
    Database::DatabaseManager.memoirs_db
  end

  def self.accessories_db
    Database::DatabaseManager.accessories_db
  end

  def self.close_databases
    Database::DatabaseManager.close_all_databases
  end

  def self.root
    File.dirname(__dir__)
  end

  # Factory methods for database-backed classes
  def self.card_database
    DatabaseCardDatabase.new
  end

  def self.stage_girl_database
    DatabaseStageGirlDatabase.new
  end
end