require_relative 'card_battle/version'
require_relative 'card_battle/effect'
require_relative 'card_battle/skill'
require_relative 'card_battle/card'
require_relative 'card_battle/equipment_card'
require_relative 'card_battle/enhanced_effects'
require_relative 'card_battle/attribute_mechanics'
require_relative 'card_battle/artwork_manager'
require_relative 'card_battle/stage_girl'
require_relative 'card_battle/pvp_match'
require_relative 'card_battle/player'
require_relative 'card_battle/game'
require_relative 'card_battle/authentic_pvp_battle'
require_relative 'card_battle/battle_unit'
require_relative 'card_battle/battle_act'

# Database system
require_relative 'card_battle/database/database_manager'
require_relative 'card_battle/database/stage_girls_database'
require_relative 'card_battle/database/memoirs_database'
require_relative 'card_battle/database/accessories_database'
require_relative 'card_battle/database/migration_manager'

# Load school-specific cards
Dir[File.join(__dir__, 'card_battle/schools/*.rb')].each { |file| require file }
Dir[File.join(__dir__, 'card_battle/cards/*.rb')].each { |file| require file }
Dir[File.join(__dir__, 'card_battle/effects/*.rb')].each { |file| require file }

module CardBattle
  class Error < StandardError; end

  # Enhanced TCG with multi-mana system, RPS mechanics, and equipment
  VERSION = "2.0.0"

  # Database convenience methods
  def self.initialize_databases
    Database::DatabaseManager.initialize_databases
  end

  def self.stage_girls_db
    Database::DatabaseManager.stage_girls_db
  end

  def self.memoirs_db
    Database::DatabaseManager.memoirs_db
  end

  def self.accessories_db
    Database::DatabaseManager.accessories_db
  end

  def self.close_databases
    Database::DatabaseManager.close_all_databases
  end

  def self.root
    File.dirname(__dir__)
  end
end