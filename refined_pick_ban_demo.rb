#!/usr/bin/env ruby
# frozen_string_literal: true

require_relative 'lib/card_battle/authentic_pick_ban_system'

# Refined Pick-Ban Demo showcasing {name-id} format
class RefinedPickBanDemo
  def self.run
    puts "=" * 60
    puts "🎯 REFINED PICK-BAN SYSTEM DEMONSTRATION"
    puts "=" * 60
    puts "Enhanced with specific {name-id} format for precise character selection"
    puts "Example: meifan-4030036, karen-1010015, hikari-1020020"
    puts
    
    show_available_cards_sample
    puts
    
    puts "Choose demonstration mode:"
    puts "1. Interactive Pick-Ban (Full Manual Control)"
    puts "2. Automated Demo (Pre-configured Strategic Selection)"
    puts "3. <PERSON> Browser (Explore Available Cards)"
    puts "4. Exit"
    print "Select (1-4): "
    
    choice = gets.chomp
    
    case choice
    when "1"
      run_interactive_demo
    when "2"
      run_automated_demo
    when "3"
      browse_available_cards
    when "4"
      puts "Thank you for exploring the refined pick-ban system! 🌟"
    else
      puts "Invalid choice. Running automated demo..."
      run_automated_demo
    end
  end

  def self.show_available_cards_sample
    puts "📚 AVAILABLE CARD SAMPLE:"
    cards = CardBattle::AuthenticPickBanSystem::AVAILABLE_CARDS
    
    # Show a few examples from each character
    sample_chars = ['<PERSON>', '<PERSON>fan', 'Hikari', '<PERSON>', '<PERSON>']
    sample_chars.each do |char|
      if cards[:grouped_by_character][char]
        char_cards = cards[:grouped_by_character][char].first(3)
        puts "#{char}: #{char_cards.join(', ')}"
      end
    end
    puts "... and #{cards[:all_cards].size - 15} more cards available"
  end

  def self.run_interactive_demo
    puts "\n🎮 INTERACTIVE PICK-BAN MODE"
    puts "You will manually select specific card IDs for rosters, bans, and teams"
    puts
    
    pick_ban = CardBattle::AuthenticPickBanSystem.new("Player 1", "Player 2")
    final_teams = pick_ban.start_pick_ban_match
    
    puts "\n🏆 PICK-BAN RESULTS:"
    puts "Player 1 final team: #{final_teams[0].join(', ')}"
    puts "Player 2 final team: #{final_teams[1].join(', ')}"
    puts
    puts "✅ Ready for authentic 5v5 battle with refined card selection!"
  end

  def self.run_automated_demo
    puts "\n🤖 AUTOMATED REFINED PICK-BAN DEMO"
    puts "Strategic card ID selections with precise costume targeting"
    puts
    
    # Demonstrate the refined system with specific card IDs
    team_alpha_roster = [
      "meifan-4030036",   # The example from user request
      "karen-1010015",    # Pajama Party Karen
      "maya-1050033",     # Top Star Maya
      "akira-4010008",    # Siegfeld Akira
      "hikari-1020020",   # Stage Girl Hikari
      "claudine-1040028", # Phantom Claudine
      "tamao-2010014",    # Rinmeikan Tamao
      "ryoko-4080005",    # Siegfeld Ryoko
      "karen-1010025",    # Scheherazade Karen (duplicate character)
      "stella-4060005"    # Siegfeld Stella
    ]
    
    team_beta_roster = [
      "hikari-1020004",   # Different Hikari costume
      "futaba-1080036",   # Seisho Futaba
      "ichie-2020022",    # Rinmeikan Ichie
      "aruru-3010017",    # Frontier Aruru
      "nana-1070025",     # Seisho Nana
      "koharu-5010008",   # Seiran Koharu
      "michiru-4020036",  # Siegfeld Michiru
      "hikari-1020017",   # Another Hikari (duplicate character)
      "futaba-1080027",   # Different Futaba costume
      "yachiyo-4050036"   # Siegfeld Yachiyo
    ]
    
    puts "📋 STEP 1: ROSTER SELECTION (Refined Card IDs)"
    puts "Team Alpha roster (10 specific cards):"
    team_alpha_roster.each_with_index do |card, i|
      char_name = card.split('-').first.capitalize
      costume_id = card.split('-').last
      puts "  #{i+1}. #{card} (#{char_name} - Costume #{costume_id})"
    end
    
    puts "\nTeam Beta roster (10 specific cards):"
    team_beta_roster.each_with_index do |card, i|
      char_name = card.split('-').first.capitalize
      costume_id = card.split('-').last
      puts "  #{i+1}. #{card} (#{char_name} - Costume #{costume_id})"
    end
    
    # Strategic bans targeting specific costumes
    team_alpha_bans = ["hikari-1020004", "michiru-4020036"]  # Ban specific Hikari and Michiru
    team_beta_bans = ["meifan-4030036", "karen-1010015"]     # Ban the example Meifan and Karen
    
    puts "\n🚫 STEP 2: BAN PHASE (Precise Costume Targeting)"
    puts "Team Alpha strategically bans:"
    team_alpha_bans.each { |card| puts "  🚫 #{card}" }
    puts "Team Beta strategically bans:"
    team_beta_bans.each { |card| puts "  🚫 #{card}" }
    
    # Calculate remaining
    team_alpha_remaining = team_alpha_roster - team_beta_bans
    team_beta_remaining = team_beta_roster - team_alpha_bans
    
    puts "\n📋 REMAINING AFTER BANS:"
    puts "Team Alpha (#{team_alpha_remaining.size} cards):"
    team_alpha_remaining.each { |card| puts "  ✅ #{card}" }
    puts "Team Beta (#{team_beta_remaining.size} cards):"
    team_beta_remaining.each { |card| puts "  ✅ #{card}" }
    
    # Final team selection
    team_alpha_final = team_alpha_remaining.first(5)
    team_beta_final = team_beta_remaining.first(5)
    
    puts "\n⚔️ STEP 3: FINAL TEAM COMPOSITION (5v5)"
    puts "Team Alpha final lineup:"
    team_alpha_final.each { |card| puts "  🌟 #{card}" }
    puts "Team Beta final lineup:"
    team_beta_final.each { |card| puts "  🌟 #{card}" }
    
    puts "\n🏆 REFINED PICK-BAN COMPLETE!"
    puts "✅ Precise costume control achieved"
    puts "✅ Strategic duplicate character usage"
    puts "✅ Specific card ID targeting (meifan-4030036 style)"
    puts "✅ Ready for authentic 5v5 battle!"
  end

  def self.browse_available_cards
    puts "\n📚 CARD BROWSER"
    cards = CardBattle::AuthenticPickBanSystem::AVAILABLE_CARDS
    
    puts "Available characters and their costumes:"
    cards[:grouped_by_character].each do |char, char_cards|
      puts "\n#{char} (#{char_cards.size} costumes):"
      char_cards.each_with_index do |card_id, index|
        costume_id = card_id.split('-').last
        puts "  #{index + 1}. #{card_id} (Costume #{costume_id})"
      end
    end
    
    puts "\n📊 SUMMARY:"
    puts "Total available cards: #{cards[:all_cards].size}"
    puts "Total characters: #{cards[:grouped_by_character].keys.size}"
    puts "Average costumes per character: #{(cards[:all_cards].size.to_f / cards[:grouped_by_character].keys.size).round(1)}"
  end
end

# Run the demo if this file is executed directly
if __FILE__ == $0
  RefinedPickBanDemo.run
end
