#!/usr/bin/env ruby
# frozen_string_literal: true

require_relative 'lib/card_battle'
require_relative 'lib/card_battle/authentic_pvp_battle'
require_relative 'lib/card_battle/battle_unit'
require_relative 'lib/card_battle/battle_act'

puts "🎭 TESTING AUTHENTIC REVUE STARLIGHT PVP SYSTEM"
puts "=" * 60

# Initialize the stage girl database
CardBattle::StageGirlDatabase.initialize_database

# Create two teams of 5 stage girls each
puts "\n📋 CREATING TEAMS..."

# Get stage girl collections first
all_stage_girls = CardBattle::StageGirlDatabase.get_all_stage_girls
stage_girl_map = all_stage_girls.map { |sg| [sg.name, sg] }.to_h

puts "Available stage girls: #{stage_girl_map.keys.join(', ')}"

# Use available stage girls for teams
available_names = stage_girl_map.keys
team1_girls = available_names.first(5)
team2_girls = available_names.last(5)

puts "Selected Team 1: #{team1_girls.join(', ')}"
puts "Selected Team 2: #{team2_girls.join(', ')}"

# Build teams
team1_stage_girls = team1_girls.map { |name| stage_girl_map[name] }.compact
team2_stage_girls = team2_girls.map { |name| stage_girl_map[name] }.compact

puts "Team 1 (Seisho): #{team1_stage_girls.map(&:name).join(', ')}"
puts "Team 2 (Mixed): #{team2_stage_girls.map(&:name).join(', ')}"

# Select costumes for each stage girl
puts "\n👗 SELECTING COSTUMES..."

[team1_stage_girls, team2_stage_girls].flatten.each do |stage_girl|
  available_costumes = stage_girl.available_costumes
  if available_costumes.any?
    # Select a random costume
    selected_costume = available_costumes.sample
    stage_girl.select_costume(selected_costume.id)
    puts "#{stage_girl.name}: #{selected_costume.name}"
  else
    puts "#{stage_girl.name}: No costumes available!"
  end
end

# Ensure we have exactly 5 members per team
if team1_stage_girls.size < 5
  puts "⚠️  Warning: Team 1 only has #{team1_stage_girls.size} members"
end

if team2_stage_girls.size < 5
  puts "⚠️  Warning: Team 2 only has #{team2_stage_girls.size} members"
end

# Pad teams if necessary
while team1_stage_girls.size < 5
  backup_girl = all_stage_girls.sample
  backup_girl.select_costume(backup_girl.available_costumes.sample.id) if backup_girl.available_costumes.any?
  team1_stage_girls << backup_girl
end

while team2_stage_girls.size < 5
  backup_girl = all_stage_girls.sample
  backup_girl.select_costume(backup_girl.available_costumes.sample.id) if backup_girl.available_costumes.any?
  team2_stage_girls << backup_girl
end

# Take exactly 5 members
team1_stage_girls = team1_stage_girls.first(5)
team2_stage_girls = team2_stage_girls.first(5)

puts "\n⚔️  STARTING AUTHENTIC PVP BATTLE..."
puts "Team sizes: #{team1_stage_girls.size} vs #{team2_stage_girls.size}"

begin
  # Create and run the battle
  battle = CardBattle::AuthenticPvPBattle.new(team1_stage_girls, team2_stage_girls)
  
  puts "\n🎯 BATTLE SIMULATION (First 3 turns)..."
  
  # Run a limited simulation to test the system
  3.times do |turn|
    break unless battle.instance_variable_get(:@battle_state) == :active
    
    puts "\n" + "="*50
    battle.execute_turn
    battle.send(:check_victory_conditions)
    battle.send(:advance_turn)
  end
  
  puts "\n✅ AUTHENTIC PVP SYSTEM TEST COMPLETED"
  puts "\n📊 BATTLE STATISTICS:"
  
  team1 = battle.team1
  team2 = battle.team2
  
  puts "\nTeam 1 Status:"
  team1.each do |unit|
    status = unit.alive? ? "ALIVE" : "DEFEATED"
    puts "  #{unit.name}: #{unit.current_hp}/#{unit.max_hp} HP, Brilliance: #{unit.brilliance}/100 [#{status}]"
  end
  
  puts "\nTeam 2 Status:"
  team2.each do |unit|
    status = unit.alive? ? "ALIVE" : "DEFEATED"
    puts "  #{unit.name}: #{unit.current_hp}/#{unit.max_hp} HP, Brilliance: #{unit.brilliance}/100 [#{status}]"
  end
  
  puts "\n🎭 AUTHENTIC MECHANICS DEMONSTRATED:"
  puts "✅ 5v5 team composition"
  puts "✅ AGI-based turn order with memoir tiebreakers"
  puts "✅ 6 ACT points per turn system"
  puts "✅ Random move availability (5-6 moves per turn)"
  puts "✅ Brilliance bar system for Climax ACTs"
  puts "✅ Authentic attribute effectiveness (Flower>Wind>Snow>Flower, etc.)"
  puts "✅ Real card data integration (ACT costs, power values, targeting)"
  puts "✅ Position-based targeting (front/middle/back roles)"
  
rescue => e
  puts "\n❌ ERROR DURING BATTLE: #{e.message}"
  puts e.backtrace.first(5)
  
  puts "\n🔧 DEBUGGING INFO:"
  puts "Team 1 stage girls: #{team1_stage_girls.map(&:name)}"
  puts "Team 2 stage girls: #{team2_stage_girls.map(&:name)}"
  
  team1_stage_girls.each do |sg|
    puts "#{sg.name}: costume=#{sg.selected_costume&.name}, available=#{sg.available?}"
  end
end

puts "\n🎯 NEXT STEPS FOR FULL IMPLEMENTATION:"
puts "1. 🔢 Implement exact damage calculation formulas"
puts "2. 🎲 Add dexterity-based critical hit system"
puts "3. 🛡️  Integrate memoir and accessory effects"
puts "4. ✨ Complete status effect system"
puts "5. 🎪 Add Finish ACT mechanics (2 consecutive Climax turns)"
puts "6. 🎨 Integrate with actual UI/artwork system"
puts "7. 🧠 Improve AI decision making"
puts "8. 📊 Add detailed battle statistics and replay system"

puts "\n🎭 Ready for your feedback and additional mechanic details!"
