#!/usr/bin/env ruby

require_relative 'lib/card_battle'

puts "🎭 REVUE STARLIGHT: RELIVE - QUICK PLAYTEST"
puts "=" * 60

# Simple mock stage girl class for testing
class MockStageGirl
  attr_reader :name, :school, :selected_costume

  def initialize(name, school)
    @name = name
    @school = school
    @selected_costume = MockCostume.new
  end

  def get_element
    :star
  end
end

class MockCostume
  attr_reader :name
  
  def initialize
    @name = "School Uniform"
  end
end

def create_simple_team(team_name, girl_names, team_number)
  puts "\n👥 Creating #{team_name}..."
  team = []

  girl_names.each_with_index do |girl_name, index|
    # Create mock stage girl
    stage_girl = MockStageGirl.new(girl_name, "<PERSON>isho")
    puts "  #{girl_name}: Ready for battle"
    team << stage_girl
  end

  team
end

def display_team_status(team, team_name)
  puts "\n#{team_name} Status:"
  team.each do |unit|
    status = unit.alive? ? "ALIVE" : "DEFEATED"
    brilliance_bar = "█" * (unit.brilliance / 10) + "░" * (10 - unit.brilliance / 10)
    puts "  #{unit.name}: #{unit.current_hp}/#{unit.max_hp} HP | Brilliance: #{unit.brilliance}/100 [#{brilliance_bar}] [#{status}]"
  end
end

def display_available_acts(unit)
  puts "\n  Available ACTs for #{unit.name}:"
  
  begin
    act1 = unit.act1
    act2 = unit.act2  
    act3 = unit.act3
    climax = unit.climax_act
    
    puts "    ACT1: #{act1.name} (Cost: #{act1.cost}, Power: #{act1.power}, Hits: #{act1.hit_count})"
    puts "    ACT2: #{act2.name} (Cost: #{act2.cost}, Power: #{act2.power}, Hits: #{act2.hit_count})"
    puts "    ACT3: #{act3.name} (Cost: #{act3.cost}, Power: #{act3.power}, Hits: #{act3.hit_count})"
    
    if unit.brilliance >= 100
      puts "    CLIMAX: #{climax.name} (Cost: #{climax.cost}, Power: #{climax.power}, Hits: #{climax.hit_count}) ⭐"
    else
      puts "    CLIMAX: #{climax.name} (Requires 100 Brilliance - Current: #{unit.brilliance})"
    end
  rescue => e
    puts "    [Error loading ACTs: #{e.message}]"
  end
end

def run_quick_battle
  puts "\n⚔️  STARTING QUICK BATTLE..."
  
  # Create teams (5v5 required for authentic battle)
  team1 = create_simple_team("Team Seisho", ["Karen", "Hikari", "Mahiru", "Claudine", "Maya"], 1)
  team2 = create_simple_team("Team Siegfeld", ["Junna", "Nana", "Futaba", "Kaoruko", "Banana"], 2)
  
  # Initialize battle
  battle = CardBattle::AuthenticPvPBattle.new(team1, team2)
  
  puts "\n🎭 BATTLE BEGINS!"
  puts "Team sizes: #{team1.size} vs #{team2.size}"
  
  # Show initial team status
  display_team_status(battle.team1, "Team Seisho")
  display_team_status(battle.team2, "Team Siegfeld")
  
  # Run 3 turns for demonstration
  3.times do |turn|
    puts "\n" + "=" * 50
    puts "🎯 TURN #{turn + 1}"
    puts "=" * 50
    
    begin
      # Execute one turn
      battle.execute_turn
      
      # Show updated status
      display_team_status(battle.team1, "Team Seisho")
      display_team_status(battle.team2, "Team Siegfeld")
      
      # Check for victory
      if battle.battle_over?
        winner = battle.winner
        puts "\n🏆 BATTLE OVER! #{winner} WINS!"
        break
      end
      
    rescue => e
      puts "❌ Error during turn: #{e.message}"
      puts "Continuing to next turn..."
    end
  end
  
  puts "\n✅ QUICK BATTLE DEMONSTRATION COMPLETE!"
end

def show_real_card_data
  puts "\n📋 REAL CARD DATA DEMONSTRATION:"
  puts "-" * 40
  
  sample_girls = ["Karen", "Hikari", "Mahiru"]
  
  sample_girls.each_with_index do |girl_name, index|
    puts "\n🎭 #{girl_name}:"
    
    begin
      stage_girl = MockStageGirl.new(girl_name, "Seisho")
      unit = CardBattle::BattleUnit.new(stage_girl, 1, index)
      display_available_acts(unit)

    rescue => e
      puts "  [Error: #{e.message}]"
    end
  end
end

def interactive_menu
  loop do
    puts "\n🎭 REVUE STARLIGHT QUICK PLAYTEST MENU"
    puts "=" * 45
    puts "1. Quick Battle (5v5)"
    puts "2. Show Real Card Data"
    puts "3. Team Status Demo"
    puts "4. Exit"
    print "\nSelect option (1-4): "
    
    choice = gets.chomp
    
    case choice
    when "1"
      run_quick_battle
    when "2"
      show_real_card_data
    when "3"
      stage_girls = create_simple_team("Demo Team", ["Karen", "Hikari", "Mahiru"], 1)
      # Create battle units for display
      battle_units = stage_girls.map.with_index { |sg, i| CardBattle::BattleUnit.new(sg, 1, i) }
      display_team_status(battle_units, "Demo Team")
      battle_units.each { |unit| display_available_acts(unit) }
    when "4"
      puts "\n🎭 Thanks for playtesting! Goodbye!"
      break
    else
      puts "❌ Invalid choice. Please select 1-4."
    end
  end
end

# Main execution
begin
  puts "\n🎮 QUICK PLAYTEST READY!"
  puts "This simplified script demonstrates:"
  puts "  ✅ Real card data loading (573 cards)"
  puts "  ✅ Authentic ACT names from karth.top"
  puts "  ✅ 5v5 authentic battles"
  puts "  ✅ Brilliance system"
  puts "  ✅ HP tracking"
  puts "  ✅ Turn-based combat"
  puts "  ✅ Authentic damage formula"
  
  interactive_menu
  
rescue => e
  puts "\n❌ PLAYTEST ERROR: #{e.message}"
  puts "Backtrace:"
  puts e.backtrace.first(5)
  puts "\n🔧 Make sure all card files are properly loaded."
end
