module CardBattle
  class Effect
    TYPES = %i[buff debuff special].freeze
    DURATIONS = %i[instant turn permanent].freeze

    attr_reader :type, :duration, :value, :description

    def initialize(type:, duration: :instant, value: 0, description: '')
      validate_type!(type)
      validate_duration!(duration)
      
      @type = type
      @duration = duration
      @value = value
      @description = description
    end

    def apply(target, source)
      case type
      when :buff
        apply_buff(target)
      when :debuff
        apply_debuff(target)
      when :special
        apply_special(target, source)
      end
    end

    def apply_damage_modifier(base_damage)
      case type
      when :buff
        base_damage + value
      when :debuff
        [base_damage - value, 0].max
      else
        base_damage
      end
    end

    private

    def validate_type!(type)
      unless TYPES.include?(type)
        raise ArgumentError, "Invalid effect type: #{type}. Must be one of #{TYPES.join(', ')}"
      end
    end

    def validate_duration!(duration)
      unless DURATIONS.include?(duration)
        raise ArgumentError, "Invalid duration: #{duration}. Must be one of #{DURATIONS.join(', ')}"
      end
    end

    def apply_buff(target)
      case duration
      when :instant
        target.add_temporary_buff(value)
      when :turn
        target.add_turn_buff(value)
      when :permanent
        target.add_permanent_buff(value)
      end
    end

    def apply_debuff(target)
      case duration
      when :instant
        target.add_temporary_debuff(value)
      when :turn
        target.add_turn_debuff(value)
      when :permanent
        target.add_permanent_debuff(value)
      end
    end

    def apply_special(target, source)
      # Special effects are implemented by subclasses
      raise NotImplementedError, "Special effects must be implemented by subclasses"
    end
  end
end 