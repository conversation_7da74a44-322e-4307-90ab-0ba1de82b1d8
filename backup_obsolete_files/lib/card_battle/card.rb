module CardBattle
  class Card
    ROLES = %i[leader unit entry equipment].freeze  # Added equipment role
    RARITIES = %i[common rare epic legendary].freeze
    SCHOOLS = %i[seisho frontier rinmeikan siegfeld seiran].freeze

    attr_reader :mana_cost, :name, :role, :rarity, :effects, :skills, :school, :school_effects
    attr_accessor :maneuver, :targeting, :element, :mana_requirements, :usage_count, :fatigue_turns

    def initialize(mana_cost:, name: nil, role: :unit, rarity: :common, effects: [], skills: [], school: nil, school_effects: [],
                   maneuver: :rock, targeting: :single_target, element: nil, mana_requirements: {})
      validate_mana_cost!(mana_cost)
      validate_role!(role)
      validate_rarity!(rarity)
      validate_school!(school) if school

      @mana_cost = mana_cost
      @name = name || "Card #{mana_cost}"
      @role = role
      @rarity = rarity
      @effects = effects
      @skills = skills
      @school = school
      @school_effects = school_effects

      # RPS mechanics
      @maneuver = maneuver
      @targeting = targeting
      @element = element

      # Multi-mana requirements (e.g., { magic: 2, attack: 1 })
      @mana_requirements = mana_requirements

      # Usage tracking for fatigue system
      @usage_count = 0
      @fatigue_turns = 0

      # State tracking
      @status_effects = []
      @damage_history = []
    end

    def damage
      base_damage = mana_cost.zero? ? 0 : mana_cost
      # Apply effects that modify damage
      effects.reduce(base_damage) { |dmg, effect| effect.apply_damage_modifier(dmg) }
    end

    def playable?(available_mana: nil, player: nil)
      # Multi-mana requirements check (prioritize this over legacy mana)
      if player && !@mana_requirements.empty?
        @mana_requirements.each do |type, amount|
          return false unless player.mana_available?(type, amount)
        end
      elsif available_mana && mana_cost > 0
        # Legacy mana check only if no multi-mana requirements
        return false if available_mana < mana_cost
      end

      # Fatigue check
      return false if fatigued?

      true
    end

    def can_target?(target_card, targeting_type = @targeting)
      case targeting_type
      when :single_target
        target_card.is_a?(Card)
      when :multiple_targets
        target_card.is_a?(Array) && target_card.size > 1
      when :area_of_effect
        true  # AoE can target anything
      else
        false
      end
    end

    def leader?
      role == :leader
    end

    def unit?
      role == :unit
    end

    def entry?
      role == :entry
    end

    def equipment?
      role == :equipment
    end

    # Status effect methods
    def stunned?
      @status_effects.include?(:stunned)
    end

    def defensive_stance?
      @status_effects.include?(:defensive_stance)
    end

    def fatigued?
      @fatigue_turns > 0
    end

    def adaptive?
      @status_effects.include?(:adaptive)
    end

    # Apply status effects
    def apply_status_effect(effect, duration = 1)
      @status_effects << effect
      case effect
      when :fatigue
        @fatigue_turns = duration
      end
    end

    def remove_status_effect(effect)
      @status_effects.delete(effect)
    end

    # Fatigue system
    def apply_fatigue(turns)
      @fatigue_turns = turns
      apply_status_effect(:fatigue, turns)
    end

    def reduce_fatigue
      @fatigue_turns = [@fatigue_turns - 1, 0].max
      remove_status_effect(:fatigue) if @fatigue_turns == 0
    end

    # Usage tracking
    def increment_usage
      @usage_count += 1
      RPSMechanics.apply_fatigue(self, @usage_count)
    end

    # Damage history for adaptive mechanics
    def record_damage(damage_type, amount)
      @damage_history << { type: damage_type, amount: amount, turn: Time.now }
      # Keep only last 10 damage records
      @damage_history = @damage_history.last(10)
    end

    # Combo boost application
    def apply_combo_boost(boost_type, value)
      case boost_type
      when :attack
        # Temporarily increase attack power
        add_temporary_effect(Effects::AttackBoostEffect.new(value: value, duration: :turn))
      when :defense
        # Temporarily increase defense
        add_temporary_effect(Effects::DefenseBoostEffect.new(value: value, duration: :turn))
      end
    end

    # Effect application methods (for compatibility with existing effect system)
    def add_temporary_buff(value)
      # Add temporary buff effect
    end

    def add_turn_buff(value)
      # Add turn-based buff effect
    end

    def add_permanent_buff(value)
      # Add permanent buff effect
    end

    def add_temporary_debuff(value)
      # Add temporary debuff effect
    end

    def add_turn_debuff(value)
      # Add turn-based debuff effect
    end

    def add_permanent_debuff(value)
      # Add permanent debuff effect
    end

    def add_temporary_effect(effect)
      @effects << effect if effect
    end

    def apply_effects(target, source)
      # Apply regular effects
      effects.each { |effect| effect.apply(target, source) }

      # Apply school effects if both cards are from the same school
      if school && target.respond_to?(:school) && target.school == school
        school_effects.each { |effect| effect.apply(target, source) }
      end
    end

    def use_skill(skill_index, target, source)
      return unless skills[skill_index]
      skills[skill_index].use(target, source)
    end

    def school_bonus?(other_card)
      school && other_card.school == school
    end

    private

    def validate_mana_cost!(cost)
      unless cost.between?(0, 10)
        raise ArgumentError, "Mana cost must be between 0 and 10, got #{cost}"
      end
    end

    def validate_role!(role)
      unless ROLES.include?(role)
        raise ArgumentError, "Invalid role: #{role}. Must be one of #{ROLES.join(', ')}"
      end
    end

    def validate_rarity!(rarity)
      unless RARITIES.include?(rarity)
        raise ArgumentError, "Invalid rarity: #{rarity}. Must be one of #{RARITIES.join(', ')}"
      end
    end

    def validate_school!(school)
      unless SCHOOLS.include?(school)
        raise ArgumentError, "Invalid school: #{school}. Must be one of #{SCHOOLS.join(', ')}"
      end
    end
  end
end