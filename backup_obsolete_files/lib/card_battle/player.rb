module CardBattle
  class Player
    DEFAULT_HEALTH = 30
    MAX_MANA_SLOTS = 10
    MAX_HAND_SIZE = 7  # Updated for 1v1 optimal hand size
    INITIAL_HAND_SIZE = 5  # Updated for better starting options
    MAX_TEAM_SIZE = 6  # Updated for 6 cards per team
    MIN_DECK_SIZE = 40  # Updated minimum deck size
    MAX_DECK_SIZE = 60  # Maximum deck size for variety
    MAX_SCHOOL_CARDS = 15  # Maximum cards from a single school
    MIN_SCHOOL_CARDS = 5   # Minimum cards from a single school for school bonus

    # Multi-mana resource system
    MANA_TYPES = %i[magic attack defense healing buff debuff stun reflect].freeze
    BASE_MANA_PER_TYPE = 10  # Base maximum for each mana type
    STARTING_MANA_PERCENTAGE = 0.5  # Start with 50% of base mana

    attr_reader :health, :mana_slots, :mana_pools, :deck, :hand, :team, :buffs, :debuffs, :primary_school, :chain_combo_meter, :equipment

    def initialize(primary_school: nil)
      @health = DEFAULT_HEALTH
      @mana_slots = 0
      @current_mana = 0  # Legacy mana for backward compatibility

      # Initialize multi-mana resource pools
      @mana_pools = {}
      MANA_TYPES.each do |type|
        @mana_pools[type] = {
          current: (BASE_MANA_PER_TYPE * STARTING_MANA_PERCENTAGE).to_i,
          max: BASE_MANA_PER_TYPE
        }
      end

      @deck = []
      @hand = []
      @team = []
      @equipment = []  # Equipment cards
      @buffs = { temporary: [], turn: [], permanent: [] }
      @debuffs = { temporary: [], turn: [], permanent: [] }
      @primary_school = primary_school
      @chain_combo_meter = 0  # Start with 0% chain combo meter
      @synergy_bonuses = {}  # Track card synergies

      draw_initial_hand
    end

    def deck_size
      @deck.size
    end

    def hand_size
      @hand.size
    end

    def team_size
      @team.size
    end

    def equipment_size
      @equipment.size
    end

    # Multi-mana resource management
    def mana_available?(type, amount)
      @mana_pools[type] && @mana_pools[type][:current] >= amount
    end

    def spend_mana(type, amount)
      return false unless mana_available?(type, amount)
      @mana_pools[type][:current] -= amount
      true
    end

    def gain_mana(type, amount)
      return false unless @mana_pools[type]
      @mana_pools[type][:current] = [@mana_pools[type][:current] + amount, @mana_pools[type][:max]].min
      true
    end

    def steal_mana(opponent, type, amount)
      return false unless opponent.mana_available?(type, amount)
      stolen = [opponent.mana_pools[type][:current], amount].min
      opponent.mana_pools[type][:current] -= stolen
      gain_mana(type, stolen)
      stolen
    end

    def distribute_mana(distributions)
      # distributions is a hash like { magic: 2, attack: 1 }
      distributions.each do |type, amount|
        gain_mana(type, amount)
      end
    end

    def start_turn
      increase_mana_slots
      refill_mana
      refill_multi_mana  # Refill multi-mana pools each turn
      draw_card
      update_effects
      update_chain_combo_meter
    end

    def take_damage(amount)
      reduced_damage = calculate_reduced_damage(amount)
      @health = [@health - reduced_damage, 0].max
    end

    def play_card(card, position = nil)
      validate_card_play(card)

      case card.role
      when :leader
        play_leader_card(card)
      when :unit
        play_unit_card(card, position)
      when :entry
        play_entry_card(card)
      end

      @current_mana -= card.mana_cost
      @hand.delete(card)
      card
    end

    def use_skill(card, skill_index, target)
      return false unless card_in_team?(card)
      success = card.use_skill(skill_index, target, self)
      if success
        # Build chain combo meter based on successful skill usage
        increase_chain_combo_meter(5)
      end
      success
    end

    # Chain combo system
    def increase_chain_combo_meter(amount)
      @chain_combo_meter = [@chain_combo_meter + amount, 100].min
    end

    def can_use_chain_combo?
      @chain_combo_meter >= 50  # Can use combo at 50% meter
    end

    def use_chain_combo(combo_type)
      return false unless can_use_chain_combo?
      @chain_combo_meter -= 50  # Consume 50% meter for combo
      execute_chain_combo(combo_type)
      true
    end

    # Equipment system
    def equip_item(equipment_card)
      return false if @equipment.size >= 3  # Max 3 equipment slots
      @equipment << equipment_card
      @hand.delete(equipment_card)
      equipment_card.on_equip(self) if equipment_card.respond_to?(:on_equip)
      true
    end

    def unequip_item(equipment_card)
      return false unless @equipment.include?(equipment_card)
      @equipment.delete(equipment_card)
      equipment_card.on_unequip(self) if equipment_card.respond_to?(:on_unequip)
      true
    end

    def add_temporary_buff(value)
      @buffs[:temporary] << value
    end

    def add_turn_buff(value)
      @buffs[:turn] << value
    end

    def add_permanent_buff(value)
      @buffs[:permanent] << value
    end

    def add_temporary_debuff(value)
      @debuffs[:temporary] << value
    end

    def add_turn_debuff(value)
      @debuffs[:turn] << value
    end

    def add_permanent_debuff(value)
      @debuffs[:permanent] << value
    end

    def dead?
      @health.zero?
    end

    def build_deck(cards)
      validate_deck!(cards)
      @deck = cards.shuffle
      draw_initial_hand
    end

    def school_bonus_active?
      return false unless primary_school

      school_cards = @team.count { |card| card.school == primary_school }
      school_cards >= MIN_SCHOOL_CARDS
    end

    private

    def create_deck
      DEFAULT_DECK_COSTS.map { |cost| Card.new(mana_cost: cost) }.shuffle
    end

    def draw_initial_hand
      INITIAL_HAND_SIZE.times { draw_card }
    end

    def draw_card
      return take_damage(1) if @deck.empty?

      card = @deck.pop
      @hand << card unless hand_size >= MAX_HAND_SIZE
      card
    end

    def increase_mana_slots
      @mana_slots = [@mana_slots + 1, MAX_MANA_SLOTS].min
    end

    def refill_mana
      @current_mana = @mana_slots
    end

    def refill_multi_mana
      # Refill multi-mana pools each turn (partial refill to maintain balance)
      MANA_TYPES.each do |type|
        refill_amount = [@mana_pools[type][:max] / 4, 2].max  # Refill 25% or minimum 2
        @mana_pools[type][:current] = [@mana_pools[type][:current] + refill_amount, @mana_pools[type][:max]].min
      end
    end

    def validate_card_play(card)
      unless @hand.include?(card)
        raise Error, 'Card not in hand'
      end

      unless card.playable?(available_mana: @current_mana, player: self)
        # Provide more detailed error message for multi-mana requirements
        if !card.mana_requirements.empty?
          missing_mana = []
          card.mana_requirements.each do |type, amount|
            unless mana_available?(type, amount)
              missing_mana << "#{type}: need #{amount}, have #{@mana_pools[type][:current]}"
            end
          end
          raise Error, "Not enough mana: #{missing_mana.join(', ')}"
        else
          raise Error, 'Not enough mana'
        end
      end

      case card.role
      when :leader
        validate_leader_play(card)
      when :unit
        validate_unit_play(card, nil)
      when :entry
        validate_entry_play(card)
      when :equipment
        validate_equipment_play(card)
      end
    end

    def validate_leader_play(card)
      if team.any?(&:leader?)
        raise Error, 'Team already has a leader'
      end
    end

    def validate_unit_play(card, position)
      if team_size >= MAX_TEAM_SIZE
        raise Error, 'Team is full'
      end
      if position && position >= MAX_TEAM_SIZE
        raise Error, 'Invalid position'
      end
    end

    def validate_entry_play(card)
      if team_size >= MAX_TEAM_SIZE
        raise Error, 'Team is full'
      end
    end

    def validate_equipment_play(card)
      if equipment_size >= 3
        raise Error, 'Equipment slots are full'
      end
    end

    def play_leader_card(card)
      @team << card
    end

    def play_unit_card(card, position)
      if position
        @team.insert(position, card)
      else
        @team << card
      end
    end

    def play_entry_card(card)
      @team << card
    end

    def card_in_team?(card)
      @team.include?(card)
    end

    def calculate_reduced_damage(amount)
      total_reduction = calculate_total_buffs - calculate_total_debuffs

      # Apply school bonus if active
      if school_bonus_active?
        total_reduction += 1  # School bonus provides +1 damage reduction
      end

      [amount - total_reduction, 0].max
    end

    def calculate_total_buffs
      @buffs.values.flatten.sum
    end

    def calculate_total_debuffs
      @debuffs.values.flatten.sum
    end

    def update_effects
      @buffs[:temporary].clear
      @debuffs[:temporary].clear

      @team.each do |card|
        card.skills.each(&:reduce_cooldown)
      end
    end

    def validate_deck!(cards)
      unless cards.size >= MIN_DECK_SIZE && cards.size <= MAX_DECK_SIZE
        raise Error, "Deck must contain between #{MIN_DECK_SIZE} and #{MAX_DECK_SIZE} cards"
      end

      if primary_school
        school_cards = cards.count { |card| card.school == primary_school }
        unless school_cards >= MIN_SCHOOL_CARDS
          raise Error, "Deck must contain at least #{MIN_SCHOOL_CARDS} cards from #{primary_school}"
        end
        unless school_cards <= MAX_SCHOOL_CARDS
          raise Error, "Deck cannot contain more than #{MAX_SCHOOL_CARDS} cards from #{primary_school}"
        end
      end

      # Validate team composition
      leaders = cards.count(&:leader?)
      unless leaders == 1
        raise Error, "Deck must contain exactly 1 leader card"
      end

      # Validate card costs
      cards.each do |card|
        unless card.mana_cost.between?(0, 10)
          raise Error, "Invalid mana cost for card #{card.name}: #{card.mana_cost}"
        end
      end
    end

    private

    # Chain combo execution
    def execute_chain_combo(combo_type)
      case combo_type
      when :offensive_burst
        # Deal damage to all opponents
        @team.each { |card| card.apply_combo_boost(:attack, 2) }
      when :defensive_wall
        # Boost all team defenses
        @team.each { |card| card.apply_combo_boost(:defense, 2) }
      when :resource_surge
        # Gain mana across all types
        MANA_TYPES.each { |type| gain_mana(type, 3) }
      end
    end

    def update_chain_combo_meter
      # Gradually increase combo meter each turn
      increase_chain_combo_meter(2)
    end
  end
end