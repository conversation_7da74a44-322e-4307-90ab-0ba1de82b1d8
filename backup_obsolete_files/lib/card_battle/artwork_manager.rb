module CardBattle
  # Manages the integration of Revue Starlight artwork with cards
  class ArtworkManager
    ARTWORK_BASE_PATH = 'revueStarlightReliveArt'
    
    # Character mapping from card names to artwork directories
    CHARACTER_MAPPING = {
      '<PERSON>' => '1010037-karen',
      'Hi<PERSON>' => '1020034-hikari',
      'Mahiru' => '1030034-mahiru',
      'Claudine' => '1040037-claudine',
      '<PERSON>' => '1050033-maya',
      'Junna' => '1060036-junna',
      'Nana' => '1070032-nana',
      'Futa<PERSON>' => '1080027-futaba',
      'Kaoruko' => '1090033-kaoruko',
      'Tam<PERSON>' => '2010034-tamao',
      'Ichie' => '2020036-ichie',
      'Fumi' => '2030036-fumi',
      'Rui' => '2040036-rui',
      'Yuyu<PERSON>' => '2050036-yuyuko',
      'Aruru' => '3010035-aruru',
      'Misora' => '3020036-misora',
      'Lalafin' => '3030036-lalafin',
      'Tsukasa' => '3040036-tsukasa',
      'Shi<PERSON>ha' => '3050036-shizuha',
      '<PERSON>' => '4010033-akira',
      'Michiru' => '4020036-michiru',
      'Meifan' => '4030036-meifan',
      'Shiori' => '4040032-shiori',
      'Yachiyo' => '4050036-yachiyo',
      'Stella' => '4060005-stella',
      'Shiro' => '4070032-shiro',
      'Ryoko' => '4080032-ryoko',
      'Minku' => '4090032-minku',
      'Kuina' => '4100006-kuina',
      'Koharu' => '5010011-koharu',
      'Suzu' => '5020009-suzu',
      'Hisame' => '5030009-hisame'
    }.freeze

    # School to character mapping
    SCHOOL_CHARACTERS = {
      seisho: ['Karen', 'Hikari', 'Mahiru', 'Claudine', 'Maya', 'Junna', 'Nana', 'Futaba', 'Kaoruko'],
      rinmeikan: ['Tamao', 'Ichie', 'Fumi', 'Rui', 'Yuyuko'],
      frontier: ['Aruru', 'Misora', 'Lalafin', 'Tsukasa', 'Shizuha'],
      siegfeld: ['Akira', 'Michiru', 'Meifan', 'Shiori', 'Yachiyo', 'Stella', 'Shiro', 'Ryoko', 'Minku', 'Kuina'],
      seiran: ['Koharu', 'Suzu', 'Hisame']
    }.freeze

    def self.get_character_artwork_path(character_name, card_id = nil)
      character_dir = CHARACTER_MAPPING[character_name]
      return nil unless character_dir

      base_path = File.join(ARTWORK_BASE_PATH, 'cards', character_dir)
      
      if card_id
        # Specific card artwork
        artwork_file = "#{card_id}_cg.png"
        File.join(base_path, artwork_file)
      else
        # Character portrait
        portrait_id = character_dir.split('-').first[0..2]  # Extract first 3 digits
        portrait_file = "chara_portrait_#{portrait_id}.png"
        File.join(base_path, portrait_file)
      end
    end

    def self.get_memoir_artwork_path(memoir_id)
      File.join(ARTWORK_BASE_PATH, 'memoirs', "#{memoir_id}_cg.png")
    end

    def self.get_random_character_for_school(school)
      characters = SCHOOL_CHARACTERS[school]
      return nil unless characters
      characters.sample
    end

    def self.get_available_artworks_for_character(character_name)
      character_dir = CHARACTER_MAPPING[character_name]
      return [] unless character_dir

      artwork_dir = File.join(ARTWORK_BASE_PATH, 'cards', character_dir)
      return [] unless Dir.exist?(artwork_dir)

      Dir.glob(File.join(artwork_dir, '*_cg.png')).map do |file|
        File.basename(file, '_cg.png')
      end
    end

    def self.assign_artwork_to_card(card, character_name = nil, specific_artwork = nil)
      # Auto-assign character based on school if not specified
      if character_name.nil? && card.school
        character_name = get_random_character_for_school(card.school)
      end

      return unless character_name

      if specific_artwork
        artwork_path = get_character_artwork_path(character_name, specific_artwork)
      else
        # Use portrait for leaders, random artwork for others
        if card.leader?
          artwork_path = get_character_artwork_path(character_name)
        else
          available_artworks = get_available_artworks_for_character(character_name)
          if available_artworks.any?
            random_artwork = available_artworks.sample
            artwork_path = get_character_artwork_path(character_name, random_artwork)
          else
            artwork_path = get_character_artwork_path(character_name)
          end
        end
      end

      # Add artwork metadata to card
      card.instance_variable_set(:@artwork_path, artwork_path)
      card.instance_variable_set(:@character_name, character_name)
      
      artwork_path
    end

    def self.create_themed_deck(school, deck_size = 40)
      characters = SCHOOL_CHARACTERS[school]
      return [] unless characters

      deck = []
      
      # Create leader card
      leader_character = characters.sample
      leader_card = create_character_card(leader_character, :leader, school)
      deck << leader_card

      # Create unit cards
      (deck_size - 1).times do
        character = characters.sample
        role = [:unit, :entry].sample
        card = create_character_card(character, role, school)
        deck << card
      end

      deck
    end

    def self.create_character_card(character_name, role, school)
      # Create enhanced card with character theme
      card_data = get_character_card_data(character_name, role)
      
      card = Card.new(
        mana_cost: card_data[:mana_cost],
        name: "#{character_name} - #{card_data[:title]}",
        role: role,
        rarity: card_data[:rarity],
        school: school,
        maneuver: card_data[:maneuver],
        targeting: card_data[:targeting],
        element: card_data[:element],
        mana_requirements: card_data[:mana_requirements],
        effects: card_data[:effects],
        skills: card_data[:skills]
      )

      # Assign artwork
      assign_artwork_to_card(card, character_name)
      
      card
    end

    def self.get_character_card_data(character_name, role)
      # Character-specific card data based on their personalities and abilities
      base_data = {
        mana_cost: role == :leader ? 6 : rand(2..5),
        rarity: role == :leader ? :legendary : [:common, :rare, :epic].sample,
        maneuver: [:flower, :wind, :ice, :dream, :moon, :space, :cloud].sample,
        targeting: [:single_target, :multiple_targets, :area_of_effect].sample,
        element: [:fire, :water, :earth, :air, :light, :dark].sample,
        effects: [],
        skills: []
      }

      # Character-specific customizations
      case character_name
      when 'Karen'
        base_data.merge!(
          title: 'Starlight',
          maneuver: :flower,
          element: :fire,
          mana_requirements: { attack: 2, magic: 1 }
        )
      when 'Hikari'
        base_data.merge!(
          title: 'Top Star',
          maneuver: :wind,
          element: :light,
          mana_requirements: { magic: 3 }
        )
      when 'Maya'
        base_data.merge!(
          title: 'Tendo',
          maneuver: :ice,
          element: :dark,
          mana_requirements: { attack: 2, debuff: 1 }
        )
      when 'Claudine'
        base_data.merge!(
          title: 'Saijo',
          maneuver: :flower,
          element: :fire,
          mana_requirements: { attack: 3 }
        )
      when 'Nana'
        base_data.merge!(
          title: 'Banana',
          maneuver: :moon,
          element: :light,
          mana_requirements: { healing: 2, magic: 1 }
        )
      when 'Junna'
        base_data.merge!(
          title: 'Hoshimi',
          maneuver: :space,
          element: :air,
          mana_requirements: { magic: 2, buff: 1 }
        )
      when 'Mahiru'
        base_data.merge!(
          title: 'Tsuyuzaki',
          maneuver: :dream,
          element: :earth,
          mana_requirements: { defense: 2, healing: 1 }
        )
      when 'Futaba'
        base_data.merge!(
          title: 'Isurugi',
          maneuver: :cloud,
          element: :earth,
          mana_requirements: { defense: 3 }
        )
      when 'Kaoruko'
        base_data.merge!(
          title: 'Hanayagi',
          maneuver: :ice,
          element: :water,
          mana_requirements: { magic: 2, debuff: 1 }
        )
      else
        # Default customization for other characters
        base_data.merge!(
          title: 'Stage Girl',
          mana_requirements: { magic: 1, attack: 1 }
        )
      end

      base_data
    end

    def self.display_card_with_artwork(card)
      artwork_path = card.instance_variable_get(:@artwork_path)
      character_name = card.instance_variable_get(:@character_name)
      
      puts "=" * 50
      puts "#{card.name}"
      puts "Role: #{card.role.to_s.capitalize} | School: #{card.school}"
      puts "Maneuver: #{card.maneuver} | Element: #{card.element}"
      puts "Mana Cost: #{card.mana_cost} | Requirements: #{card.mana_requirements}"
      
      if character_name
        puts "Character: #{character_name}"
      end
      
      if artwork_path && File.exist?(artwork_path)
        puts "Artwork: #{artwork_path}"
        puts "✨ Beautiful artwork available!"
      else
        puts "⚠️  Artwork not found"
      end
      
      puts "=" * 50
    end
  end
end
