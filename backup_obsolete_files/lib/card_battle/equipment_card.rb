module CardBattle
  class EquipmentCard < Card
    attr_reader :equipment_type, :durability, :max_durability, :probability_effects

    def initialize(mana_cost:, name:, equipment_type:, durability: 3, effects: [], skills: [],
                   probability_effects: [], mana_requirements: {})
      super(
        mana_cost: mana_cost,
        name: name,
        role: :equipment,
        rarity: :common,
        effects: effects,
        skills: skills,
        mana_requirements: mana_requirements
      )

      @equipment_type = equipment_type
      @max_durability = durability
      @durability = durability
      @probability_effects = probability_effects
      @equipped_to = nil
    end

    def on_equip(player)
      @equipped_to = player
      apply_equip_effects(player)
    end

    def on_unequip(player)
      remove_equip_effects(player)
      @equipped_to = nil
    end

    def use_equipment(target = nil)
      return false if @durability <= 0

      success = trigger_probability_effects(target)
      reduce_durability if success
      success
    end

    def broken?
      @durability <= 0
    end

    def repair(amount = 1)
      @durability = [@durability + amount, @max_durability].min
    end

    private

    def apply_equip_effects(player)
      effects.each { |effect| effect.apply(player, self) }
    end

    def remove_equip_effects(player)
      # Remove temporary effects applied by this equipment
      effects.each { |effect| effect.remove(player, self) if effect.respond_to?(:remove) }
    end

    def trigger_probability_effects(target)
      @probability_effects.each do |prob_effect|
        if rand < prob_effect[:chance]
          prob_effect[:effect].call(target, @equipped_to)
          return true
        end
      end
      false
    end

    def reduce_durability(amount = 1)
      @durability = [@durability - amount, 0].max
      if broken?
        on_unequip(@equipped_to) if @equipped_to
      end
    end
  end

  # Specific equipment implementations
  module Equipment
    class LuckyCharm < EquipmentCard
      def initialize
        super(
          mana_cost: 2,
          name: "Lucky Charm",
          equipment_type: :accessory,
          durability: 5,
          mana_requirements: { magic: 1 },
          probability_effects: [
            {
              chance: 0.3,
              effect: ->(target, player) {
                if target.respond_to?(:apply_combo_boost)
                  target.apply_combo_boost(:attack, 2)
                  puts "Lucky Charm activated! Double attack damage!"
                end
              }
            }
          ]
        )
      end
    end

    class PoisonDagger < EquipmentCard
      def initialize
        super(
          mana_cost: 3,
          name: "Poison Dagger",
          equipment_type: :weapon,
          durability: 4,
          mana_requirements: { attack: 2 },
          probability_effects: [
            {
              chance: 0.5,
              effect: ->(target, player) {
                if target.respond_to?(:apply_status_effect)
                  target.apply_status_effect(:poison, 3)
                  puts "Poison Dagger inflicted poison!"
                end
              }
            }
          ]
        )
      end
    end

    class PrecisionBow < EquipmentCard
      def initialize
        super(
          mana_cost: 4,
          name: "Precision Bow",
          equipment_type: :weapon,
          durability: 6,
          mana_requirements: { attack: 2 },
          probability_effects: [
            {
              chance: 0.75,  # Base hit chance
              effect: ->(target, player) {
                # Conditional probability: 100% if target is stunned
                hit_chance = target.respond_to?(:stunned?) && target.stunned? ? 1.0 : 0.75
                if rand < hit_chance
                  damage = target.respond_to?(:stunned?) && target.stunned? ? 6 : 4
                  target.record_damage(:piercing, damage) if target.respond_to?(:record_damage)
                  puts "Precision Bow hit for #{damage} damage!"
                  true
                else
                  puts "Precision Bow missed!"
                  false
                end
              }
            }
          ]
        )
      end
    end

    class AdaptiveArmor < EquipmentCard
      def initialize
        super(
          mana_cost: 5,
          name: "Adaptive Armor",
          equipment_type: :armor,
          durability: 8,
          mana_requirements: { defense: 3 }
        )
        @damage_types_received = []
      end

      def on_equip(player)
        super(player)
        player.apply_status_effect(:adaptive) if player.respond_to?(:apply_status_effect)
      end

      def calculate_defense_bonus
        # +1 defense for each different damage type received in last 3 turns
        recent_damage = @damage_types_received.last(3)
        unique_types = recent_damage.uniq.size
        unique_types
      end

      def record_damage_received(damage_type)
        @damage_types_received << damage_type
        @damage_types_received = @damage_types_received.last(10)  # Keep last 10
      end
    end

    class FireballScroll < EquipmentCard
      def initialize
        super(
          mana_cost: 3,
          name: "Fireball Scroll",
          equipment_type: :consumable,
          durability: 1,  # Single use
          mana_requirements: { magic: 3 },
          probability_effects: [
            {
              chance: 1.0,  # Always activates
              effect: ->(target, player) {
                # Variable damage: 2-5 fire damage
                damage = rand(2..5)
                target.record_damage(:fire, damage) if target.respond_to?(:record_damage)
                puts "Fireball deals #{damage} fire damage!"
              }
            }
          ]
        )
      end
    end

    class ComboGloves < EquipmentCard
      def initialize
        super(
          mana_cost: 4,
          name: "Combo Gloves",
          equipment_type: :accessory,
          durability: 6,
          mana_requirements: { buff: 2, magic: 1 }
        )
        @last_action_type = nil
      end

      def trigger_combo_effect(action_type, target)
        if @last_action_type == :stun && action_type == :attack
          # Combo: stun followed by attack
          bonus_damage = 2
          target.record_damage(:combo, bonus_damage) if target.respond_to?(:record_damage)
          puts "Combo Gloves: Stun-Attack combo deals +#{bonus_damage} damage!"
        end
        @last_action_type = action_type
      end
    end
  end
end
