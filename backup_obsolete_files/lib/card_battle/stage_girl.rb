module CardBattle
  # Represents a Stage Girl character with multiple costume options
  class StageGirl
    attr_reader :name, :school, :base_stats, :available_costumes, :selected_costume
    attr_accessor :banned

    def initialize(name:, school:, base_stats: {})
      @name = name
      @school = school
      @base_stats = base_stats
      @available_costumes = []
      @selected_costume = nil
      @banned = false
    end

    def add_costume(costume)
      @available_costumes << costume
      costume.stage_girl = self
    end

    def select_costume(costume_id)
      costume = @available_costumes.find { |c| c.id == costume_id }
      if costume
        @selected_costume = costume
        true
      else
        false
      end
    end

    def selected_costume_name
      @selected_costume ? @selected_costume.name : "No Costume Selected"
    end

    def get_skills
      @selected_costume ? @selected_costume.skills : []
    end

    def get_stats
      if @selected_costume
        combined_stats = @base_stats.dup
        @selected_costume.stat_modifiers.each do |stat, modifier|
          combined_stats[stat] = (combined_stats[stat] || 0) + modifier
        end
        combined_stats
      else
        @base_stats
      end
    end

    def get_maneuver
      @selected_costume ? @selected_costume.maneuver : :flower
    end

    def get_element
      @selected_costume ? @selected_costume.element : nil
    end

    def get_targeting
      @selected_costume ? @selected_costume.targeting : :single_target
    end

    def get_mana_requirements
      @selected_costume ? @selected_costume.mana_requirements : {}
    end

    def banned?
      @banned
    end

    def available?
      !@banned && @selected_costume
    end

    def to_card
      return nil unless available?

      Card.new(
        mana_cost: @selected_costume.mana_cost,
        name: "#{@name} - #{@selected_costume.name}",
        role: @selected_costume.role,
        rarity: @selected_costume.rarity,
        school: @school,
        maneuver: get_maneuver,
        targeting: get_targeting,
        element: get_element,
        mana_requirements: get_mana_requirements,
        effects: @selected_costume.effects,
        skills: get_skills
      ).tap do |card|
        # Add stage girl metadata
        card.instance_variable_set(:@stage_girl, self)
        card.instance_variable_set(:@costume, @selected_costume)
        
        # Assign artwork
        ArtworkManager.assign_artwork_to_card(card, @name, @selected_costume.artwork_id)
      end
    end
  end

  # Represents a costume that a Stage Girl can wear
  class Costume
    attr_reader :id, :name, :rarity, :role, :mana_cost, :stat_modifiers, :skills, :effects
    attr_reader :maneuver, :targeting, :element, :mana_requirements, :artwork_id
    attr_accessor :stage_girl

    def initialize(id:, name:, rarity: :common, role: :unit, mana_cost: 3, 
                   stat_modifiers: {}, skills: [], effects: [],
                   maneuver: :flower, targeting: :single_target, element: nil,
                   mana_requirements: {}, artwork_id: nil)
      @id = id
      @name = name
      @rarity = rarity
      @role = role
      @mana_cost = mana_cost
      @stat_modifiers = stat_modifiers
      @skills = skills
      @effects = effects
      @maneuver = maneuver
      @targeting = targeting
      @element = element
      @mana_requirements = mana_requirements
      @artwork_id = artwork_id
      @stage_girl = nil
    end

    def full_name
      @stage_girl ? "#{@stage_girl.name} - #{@name}" : @name
    end

    def school
      @stage_girl ? @stage_girl.school : nil
    end
  end

  # Database of all Stage Girls and their costumes
  class StageGirlDatabase
    class << self
      def initialize_database
        @stage_girls = {}
        @costumes = {}
        load_stage_girls_data
        load_costumes_data
      end

      def get_stage_girl(name)
        @stage_girls[name]
      end

      def get_all_stage_girls
        @stage_girls.values
      end

      def get_stage_girls_by_school(school)
        @stage_girls.values.select { |sg| sg.school == school }
      end

      def get_costume(id)
        @costumes[id]
      end

      def create_player_collection
        # Each player gets one of each stage girl
        collection = {}
        @stage_girls.each do |name, template|
          # Create a copy for the player
          stage_girl = StageGirl.new(
            name: template.name,
            school: template.school,
            base_stats: template.base_stats.dup
          )
          
          # Add all available costumes
          template.available_costumes.each do |costume_template|
            costume = Costume.new(
              id: costume_template.id,
              name: costume_template.name,
              rarity: costume_template.rarity,
              role: costume_template.role,
              mana_cost: costume_template.mana_cost,
              stat_modifiers: costume_template.stat_modifiers.dup,
              skills: costume_template.skills.dup,
              effects: costume_template.effects.dup,
              maneuver: costume_template.maneuver,
              targeting: costume_template.targeting,
              element: costume_template.element,
              mana_requirements: costume_template.mana_requirements.dup,
              artwork_id: costume_template.artwork_id
            )
            stage_girl.add_costume(costume)
          end
          
          collection[name] = stage_girl
        end
        collection
      end

      private

      def load_stage_girls_data
        @stage_girls = {}
        
        # Load all characters from the artwork manager
        ArtworkManager::CHARACTER_MAPPING.each do |character_name, _|
          school = determine_school_for_character(character_name)
          
          stage_girl = StageGirl.new(
            name: character_name,
            school: school,
            base_stats: {
              health: 100,
              attack: 50,
              defense: 30,
              speed: 40
            }
          )
          
          @stage_girls[character_name] = stage_girl
        end
      end

      def load_costumes_data
        @costumes = {}
        
        # For each stage girl, create multiple costume options
        @stage_girls.each do |name, stage_girl|
          create_costumes_for_stage_girl(stage_girl)
        end
      end

      def determine_school_for_character(character_name)
        ArtworkManager::SCHOOL_CHARACTERS.each do |school, characters|
          return school if characters.include?(character_name)
        end
        :unknown
      end

      def create_costumes_for_stage_girl(stage_girl)
        # Get available artworks for this character
        available_artworks = ArtworkManager.get_available_artworks_for_character(stage_girl.name)
        
        # Create base costume (always available)
        base_costume = create_base_costume(stage_girl)
        stage_girl.add_costume(base_costume)
        
        # Create additional costumes based on available artwork
        available_artworks.each_with_index do |artwork_id, index|
          costume = create_themed_costume(stage_girl, artwork_id, index)
          stage_girl.add_costume(costume)
        end
        
        # Ensure at least 3 costumes per character
        while stage_girl.available_costumes.size < 3
          costume = create_random_costume(stage_girl, stage_girl.available_costumes.size)
          stage_girl.add_costume(costume)
        end
      end

      def create_base_costume(stage_girl)
        Costume.new(
          id: "#{stage_girl.name.downcase}_base",
          name: "School Uniform",
          rarity: :common,
          role: :unit,
          mana_cost: 3,
          stat_modifiers: {},
          maneuver: :flower,
          targeting: :single_target,
          element: determine_base_element(stage_girl),
          mana_requirements: { magic: 1, attack: 1 },
          skills: [create_basic_skill(stage_girl)],
          effects: []
        )
      end

      def create_themed_costume(stage_girl, artwork_id, index)
        themes = [:performance, :battle, :formal, :casual, :special]
        theme = themes[index % themes.size]
        
        Costume.new(
          id: "#{stage_girl.name.downcase}_#{artwork_id}",
          name: "#{theme.to_s.capitalize} Outfit",
          rarity: [:common, :rare, :epic].sample,
          role: [:unit, :entry, :leader].sample,
          mana_cost: 2 + index,
          stat_modifiers: generate_stat_modifiers(theme),
          maneuver: [:flower, :wind, :ice, :dream, :moon, :space, :cloud].sample,
          targeting: [:single_target, :multiple_targets, :area_of_effect].sample,
          element: determine_themed_element(stage_girl, theme),
          mana_requirements: generate_mana_requirements(theme),
          skills: [create_themed_skill(stage_girl, theme)],
          effects: generate_themed_effects(theme),
          artwork_id: artwork_id
        )
      end

      def create_random_costume(stage_girl, index)
        Costume.new(
          id: "#{stage_girl.name.downcase}_random_#{index}",
          name: "Variant #{index + 1}",
          rarity: [:common, :rare].sample,
          role: :unit,
          mana_cost: 2 + (index % 4),
          stat_modifiers: { attack: index * 5, defense: index * 3 },
          maneuver: [:flower, :wind, :ice, :dream, :moon, :space, :cloud].sample,
          targeting: :single_target,
          element: [:fire, :water, :earth, :air, :light, :dark].sample,
          mana_requirements: { magic: 1 + index, attack: 1 },
          skills: [create_basic_skill(stage_girl)],
          effects: []
        )
      end

      def determine_base_element(stage_girl)
        case stage_girl.school
        when :seisho then :light
        when :rinmeikan then :earth
        when :frontier then :fire
        when :siegfeld then :dark
        when :seiran then :water
        else :air
        end
      end

      def determine_themed_element(stage_girl, theme)
        case theme
        when :performance then :light
        when :battle then :fire
        when :formal then :earth
        when :casual then :air
        when :special then :dark
        else determine_base_element(stage_girl)
        end
      end

      def generate_stat_modifiers(theme)
        case theme
        when :performance
          { attack: 10, speed: 15 }
        when :battle
          { attack: 20, defense: 5 }
        when :formal
          { defense: 15, health: 10 }
        when :casual
          { speed: 20, health: 5 }
        when :special
          { attack: 15, defense: 10, speed: 10 }
        else
          {}
        end
      end

      def generate_mana_requirements(theme)
        case theme
        when :performance
          { magic: 2, buff: 1 }
        when :battle
          { attack: 3 }
        when :formal
          { defense: 2, healing: 1 }
        when :casual
          { magic: 1, attack: 1 }
        when :special
          { magic: 2, attack: 1, buff: 1 }
        else
          { magic: 1, attack: 1 }
        end
      end

      def generate_themed_effects(theme)
        case theme
        when :performance
          [Effects::ChainComboBoostEffect.new(boost_amount: 5)]
        when :battle
          [Effects::AttackBoostEffect.new(value: 3, duration: :turn)]
        when :formal
          [Effects::DefenseBoostEffect.new(value: 2, duration: :permanent)]
        else
          []
        end
      end

      def create_basic_skill(stage_girl)
        Skill.new(
          type: :act,
          target: :single_opponent,
          name: "#{stage_girl.name}'s Performance",
          description: "Basic performance skill"
        )
      end

      def create_themed_skill(stage_girl, theme)
        Skill.new(
          type: [:act, :climax, :passive].sample,
          target: [:single_opponent, :all_opponents, :team, :self].sample,
          name: "#{theme.to_s.capitalize} #{stage_girl.name}",
          description: "#{theme.to_s.capitalize} themed skill for #{stage_girl.name}"
        )
      end
    end
  end
end
