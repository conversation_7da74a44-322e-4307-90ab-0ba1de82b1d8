module CardBattle
  module Effects
    class SpiritBoostEffect < Effect
      def initialize(value:, duration: :turn)
        super(
          type: :spirit_boost,
          duration: duration,
          value: value,
          description: "Increases spirit power by #{value}"
        )
      end

      def apply_damage_modifier(base_damage)
        base_damage + value
      end
    end

    class PerformanceBoostEffect < Effect
      def initialize(value:, duration: :turn)
        super(
          type: :performance_boost,
          duration: duration,
          value: value,
          description: "Boosts performance by #{value}"
        )
      end

      def apply(target, source)
        return unless target.respond_to?(:school) && target.school == :rinmeikan
        super
      end
    end

    class TeamSupportEffect < Effect
      def initialize(value:, duration: :turn)
        super(
          type: :team_support,
          duration: duration,
          value: value,
          description: "Provides team support of #{value}"
        )
      end

      def apply(target, source)
        source.team.each do |card|
          if card.respond_to?(:school) && card.school == :rinmeikan
            case duration
            when :instant
              card.add_temporary_buff(value)
            when :turn
              card.add_turn_buff(value)
            when :permanent
              card.add_permanent_buff(value)
            end
          end
        end
      end
    end

    class RinmeikanSynergyEffect < Effect
      def initialize(value:, duration: :turn)
        super(
          type: :special,
          duration: duration,
          value: value,
          description: "Enhances Rinmeikan team synergy by #{value}"
        )
      end

      def apply(target, source)
        return unless target.respond_to?(:school) && target.school == :rinmeikan
        
        # Apply synergy bonus to all Rinmeikan cards in the team
        source.team.each do |card|
          if card.respond_to?(:school) && card.school == :rinmeikan
            card.add_turn_buff(value)
          end
        end
      end
    end
  end
end 