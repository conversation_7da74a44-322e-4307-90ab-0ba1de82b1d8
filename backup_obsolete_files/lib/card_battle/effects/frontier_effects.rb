module CardBattle
  module Effects
    class SpeedBoostEffect < Effect
      def initialize(value:, duration: :turn)
        super(
          type: :speed_boost,
          duration: duration,
          value: value,
          description: "Increases speed by #{value}"
        )
      end

      def apply_damage_modifier(base_damage)
        # Speed boosts increase damage and allow for faster skill usage
        base_damage + value
      end

      def apply(target, source)
        return unless target.respond_to?(:school) && target.school == :frontier
        super
      end
    end

    class ComboBoostEffect < Effect
      def initialize(value:, duration: :turn)
        super(
          type: :combo_boost,
          duration: duration,
          value: value,
          description: "Increases combo potential by #{value}"
        )
      end

      def apply(target, source)
        # Combo boosts affect the entire Frontier team
        source.team.each do |card|
          if card.respond_to?(:school) && card.school == :frontier
            case duration
            when :instant
              card.add_temporary_buff(value)
            when :turn
              card.add_turn_buff(value)
            when :permanent
              card.add_permanent_buff(value)
            end
          end
        end
      end
    end

    class PerformanceChainEffect < Effect
      def initialize(value:, duration: :turn)
        super(
          type: :performance_chain,
          duration: duration,
          value: value,
          description: "Creates a performance chain with #{value} power"
        )
      end

      def apply(target, source)
        return unless target.respond_to?(:school) && target.school == :frontier
        
        # Calculate chain bonus based on number of Frontier cards
        chain_count = source.team.count { |c| c.school == :frontier }
        chain_bonus = value * chain_count
        
        # Apply chain bonus to all Frontier cards
        source.team.each do |card|
          if card.respond_to?(:school) && card.school == :frontier
            case duration
            when :instant
              card.add_temporary_buff(chain_bonus)
            when :turn
              card.add_turn_buff(chain_bonus)
            when :permanent
              card.add_permanent_buff(chain_bonus)
            end
          end
        end
      end
    end

    class FrontierSynergyEffect < Effect
      def initialize(value:, duration: :turn)
        super(
          type: :special,
          duration: duration,
          value: value,
          description: "Enhances Frontier team synergy by #{value}"
        )
      end

      def apply(target, source)
        return unless target.respond_to?(:school) && target.school == :frontier
        
        # Apply synergy bonus to all Frontier cards in the team
        source.team.each do |card|
          if card.respond_to?(:school) && card.school == :frontier
            # Each Frontier card gets a bonus based on the number of other Frontier cards
            frontier_count = source.team.count { |c| c.school == :frontier }
            synergy_bonus = value * (frontier_count - 1)
            
            case duration
            when :instant
              card.add_temporary_buff(synergy_bonus)
            when :turn
              card.add_turn_buff(synergy_bonus)
            when :permanent
              card.add_permanent_buff(synergy_bonus)
            end
          end
        end
      end
    end
  end
end 