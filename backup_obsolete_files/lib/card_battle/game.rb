module CardBattle
  class Game
    attr_reader :players, :current_player_index, :turn_number

    def initialize
      @players = [Player.new, Player.new]
      @current_player_index = [0, 1].sample
      @turn_number = 1
      other_index = 1 - @current_player_index
      # Give the non-starting player an extra card
      @players[other_index].send(:draw_card)
      # Start the first player's turn
      current_player.start_turn
    end

    def current_player
      @players[@current_player_index]
    end

    def opponent
      @players[1 - @current_player_index]
    end

    def end_turn
      @current_player_index = 1 - @current_player_index
      @turn_number += 1
      current_player.start_turn
    end

    def play_card(card, position = nil, target = nil)
      # Validate multi-mana requirements
      unless validate_mana_requirements(card)
        return false
      end

      played_card = current_player.play_card(card, position)

      # Apply card effects based on role
      case played_card.role
      when :unit, :entry
        apply_card_effects(played_card, target)
      when :equipment
        current_player.equip_item(played_card)
      when :leader
        apply_leader_effects(played_card)
      end

      # Generate mana based on action effects
      generate_action_mana(played_card, target)

      played_card
    end

    def attack_with_card(attacker_card, target_card)
      # Calculate attribute effectiveness
      effectiveness = AttributeMechanics.calculate_effectiveness(
        attacker_card.maneuver, attacker_card.targeting, attacker_card.element,
        target_card.maneuver, target_card.targeting, target_card.element
      )

      # Calculate hit chance
      hit_chance = AttributeMechanics.calculate_hit_chance(attacker_card, target_card)

      if rand < hit_chance
        base_damage = attacker_card.damage
        final_damage = AttributeMechanics.apply_effectiveness(base_damage, effectiveness)

        # Record damage for adaptive mechanics
        target_card.record_damage(attacker_card.element || :physical, final_damage)

        # Apply damage
        if target_card.respond_to?(:take_damage)
          target_card.take_damage(final_damage)
        else
          opponent.take_damage(final_damage)
        end

        # Increment usage for fatigue system
        attacker_card.increment_usage

        # Build chain combo meter
        current_player.increase_chain_combo_meter(3)

        puts "#{attacker_card.name} attacks #{target_card.name} for #{final_damage} damage! (#{effectiveness})"
        return true
      else
        puts "#{attacker_card.name} missed #{target_card.name}!"
        return false
      end
    end

    def use_skill(card, skill_index, target_card = nil)
      # Determine the actual target based on skill type and target_card
      target = determine_skill_target(card.skills[skill_index], target_card)

      # Use the skill
      success = current_player.use_skill(card, skill_index, target)

      # Apply skill effects
      if success
        apply_skill_effects(card.skills[skill_index], target)
      end

      success
    end

    def winner
      if players[0].dead?
        players[1]
      elsif players[1].dead?
        players[0]
      else
        nil
      end
    end

    def use_chain_combo(combo_type)
      return false unless current_player.can_use_chain_combo?

      success = current_player.use_chain_combo(combo_type)
      if success
        puts "#{current_player} activated #{combo_type} chain combo!"
      end
      success
    end

    def use_equipment(equipment_card, target = nil)
      return false unless current_player.equipment.include?(equipment_card)

      success = equipment_card.use_equipment(target)
      if equipment_card.broken?
        current_player.unequip_item(equipment_card)
        puts "#{equipment_card.name} broke and was unequipped!"
      end
      success
    end

    private

    def validate_mana_requirements(card)
      return true if card.mana_requirements.empty?

      card.mana_requirements.each do |type, amount|
        unless current_player.mana_available?(type, amount)
          puts "Not enough #{type} mana! Need #{amount}, have #{current_player.mana_pools[type][:current]}"
          return false
        end
      end

      # Spend the mana
      card.mana_requirements.each do |type, amount|
        current_player.spend_mana(type, amount)
      end

      true
    end

    def apply_card_effects(card, target = nil)
      # Apply effects to the specified target or opponent's team
      targets = target ? [target] : opponent.team
      targets.each do |t|
        card.apply_effects(t, current_player)
      end
    end

    def apply_leader_effects(leader_card)
      # Leader cards affect the entire team
      current_player.team.each do |card|
        leader_card.apply_effects(card, current_player)
      end
    end

    def generate_action_mana(played_card, target)
      # Generate mana based on the effects of actions on the opponent
      mana_generation = {}

      case played_card.role
      when :unit
        # Successful unit plays generate attack mana
        mana_generation[:attack] = 1
        mana_generation[:magic] = 1 if played_card.element
      when :leader
        # Leader plays generate buff mana
        mana_generation[:buff] = 2
        mana_generation[:magic] = 1
      when :equipment
        # Equipment generates specialized mana
        case played_card.equipment_type
        when :weapon
          mana_generation[:attack] = 2
        when :armor
          mana_generation[:defense] = 2
        when :accessory
          mana_generation[:magic] = 1
          mana_generation[:buff] = 1
        end
      end

      # Apply damage-based mana generation
      if target && played_card.damage > 0
        damage_dealt = played_card.damage
        mana_generation[:attack] = (mana_generation[:attack] || 0) + (damage_dealt / 3).to_i
      end

      current_player.distribute_mana(mana_generation) unless mana_generation.empty?
    end

    def apply_skill_effects(skill, target)
      case skill.target
      when :self
        skill.apply_effects(current_player, current_player)
      when :single_opponent
        skill.apply_effects(target, current_player)
      when :all_opponents
        opponent.team.each do |t|
          skill.apply_effects(t, current_player)
        end
      when :team
        current_player.team.each do |t|
          skill.apply_effects(t, current_player)
        end
      end
    end

    def determine_skill_target(skill, target_card)
      case skill.target
      when :self
        current_player
      when :single_opponent
        target_card || opponent.team.first
      when :all_opponents
        opponent.team
      when :team
        current_player.team
      end
    end
  end
end