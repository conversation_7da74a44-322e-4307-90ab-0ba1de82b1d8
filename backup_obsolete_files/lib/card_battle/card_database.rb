module CardBattle
  class CardDatabase
    class << self
      def initialize
        @cards = {}
        @school_cards = Hash.new { |h, k| h[k] = [] }
        @rarity_cards = Hash.new { |h, k| h[k] = [] }
        @role_cards = Hash.new { |h, k| h[k] = [] }
      end

      def add_card(card)
        card_id = generate_card_id(card)
        @cards[card_id] = card
        @school_cards[card.school] << card if card.school
        @rarity_cards[card.rarity] << card
        @role_cards[card.role] << card
      end

      def get_card(card_id)
        @cards[card_id]
      end

      def get_school_cards(school)
        @school_cards[school]
      end

      def get_rarity_cards(rarity)
        @rarity_cards[rarity]
      end

      def get_role_cards(role)
        @role_cards[role]
      end

      def get_leader_cards(school = nil)
        cards = @role_cards[:leader]
        school ? cards.select { |c| c.school == school } : cards
      end

      def get_unit_cards(school = nil)
        cards = @role_cards[:unit]
        school ? cards.select { |c| c.school == school } : cards
      end

      def get_entry_cards(school = nil)
        cards = @role_cards[:entry]
        school ? cards.select { |c| c.school == school } : cards
      end

      def get_cards_by_cost_range(min_cost, max_cost, school = nil)
        cards = @cards.values.select { |c| c.mana_cost.between?(min_cost, max_cost) }
        school ? cards.select { |c| c.school == school } : cards
      end

      def get_cards_by_rarity_and_school(rarity, school)
        @rarity_cards[rarity].select { |c| c.school == school }
      end

      def get_cards_by_role_and_school(role, school)
        @role_cards[role].select { |c| c.school == school }
      end

      def get_all_cards
        @cards.values
      end

      def get_schools
        @school_cards.keys
      end

      def get_rarities
        @rarity_cards.keys
      end

      def get_roles
        @role_cards.keys
      end

      def clear
        @cards.clear
        @school_cards.clear
        @rarity_cards.clear
        @role_cards.clear
      end

      private

      def generate_card_id(card)
        # Generate a unique ID based on school, role, and name
        # This is a simple implementation - you might want to use a more robust system
        "#{card.school}_#{card.role}_#{card.name}".downcase.gsub(/\s+/, '_')
      end
    end
  end
end 