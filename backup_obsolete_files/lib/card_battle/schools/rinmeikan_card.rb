module CardBattle
  module Schools
    class RinmeikanCard < Card
      attr_reader :spirit_power, :performance_level, :tradition_bonus

      def initialize(mana_cost:, name:, role:, rarity:, effects: [], skills: [], school_effects: [])
        super(mana_cost: mana_cost, name: name, role: role, rarity: rarity, effects: effects, skills: skills)
        @school = :rinmeikan
        @spirit_power = 0
        @performance_level = 0
        @tradition_bonus = 0
        @school_effects = school_effects
      end

      def spirit_boost(value)
        @spirit_power += value
        # Spirit power affects both damage and skill effectiveness
        self
      end

      def performance_boost(value)
        @performance_level += value
        # Performance level affects temporary buffs and skill chains
        self
      end

      def tradition_boost(value)
        @tradition_bonus += value
        # Tradition bonus provides passive benefits to all Rinmeikan cards
        self
      end

      def damage
        # Base damage is modified by spirit power and performance level
        base = super
        base + (@spirit_power * 0.5) + (@performance_level * 0.3) + @tradition_bonus
      end

      def apply_rinmeikan_effects
        # Apply effects to other Rinmeikan cards in the team
        team.each do |card|
          if card.respond_to?(:school) && card.school == :rinmeikan
            # Spirit power is shared among Rinmeikan cards
            card.spirit_boost(@spirit_power * 0.2)
            # Performance boosts affect the entire team
            card.performance_boost(@performance_level * 0.1)
            # Tradition bonus is always active
            card.tradition_boost(@tradition_bonus)
          end
        end
      end

      def can_perform_skill?
        # Skills can be used when spirit power is high enough
        @spirit_power >= 3
      end

      def calculate_tradition_bonus
        # Calculate bonus based on number of Rinmeikan cards in team
        rinmeikan_count = team.count { |c| c.school == :rinmeikan }
        @tradition_bonus * (rinmeikan_count - 1)
      end

      def apply_school_effects
        # Apply school-specific effects
        @school_effects.each do |effect|
          case effect.type
          when :spirit_boost
            spirit_boost(effect.value)
          when :performance_boost
            performance_boost(effect.value)
          when :tradition_boost
            tradition_boost(effect.value)
          when :team_support
            apply_rinmeikan_effects
          end
        end
      end

      def to_s
        "#{name} (Rinmeikan #{role.capitalize}) - Spirit: #{@spirit_power}, Performance: #{@performance_level}, Tradition: #{@tradition_bonus}"
      end
    end
  end
end 