module CardBattle
  module Schools
    class SeishoCard < Card
      attr_reader :performance_power, :stage_presence, :spotlight_count

      def initialize(mana_cost:, name:, role:, rarity:, effects: [], skills: [], school_effects: [])
        super(mana_cost: mana_cost, name: name, role: role, rarity: rarity, effects: effects, skills: skills)
        @school = :seisho
        @performance_power = 0
        @stage_presence = 0
        @spotlight_count = 0  # Tracks number of spotlights (special effects)
        @school_effects = school_effects
      end

      def performance_boost(value)
        @performance_power += value
        # Performance power affects damage and skill effectiveness
        self
      end

      def presence_boost(value)
        @stage_presence += value
        # Stage presence affects spotlight generation and team support
        self
      end

      def gain_spotlight
        @spotlight_count += 1
        # Each spotlight provides a unique bonus
        self
      end

      def damage
        # Base damage is modified by performance power and stage presence
        base = super
        performance_bonus = @performance_power * 0.4
        presence_bonus = @stage_presence * 0.3
        
        # Spotlight bonus
        spotlight_bonus = @spotlight_count * 0.2
        
        base + performance_bonus + presence_bonus + spotlight_bonus
      end

      def apply_seisho_effects
        # Apply effects to other Seisho cards in the team
        team.each do |card|
          if card.respond_to?(:school) && card.school == :seisho
            # Performance power is shared among Seisho cards
            card.performance_boost(@performance_power * 0.2)
            # Stage presence affects the entire team
            card.presence_boost(@stage_presence * 0.1)
            # Spotlight effects are always active
            card.gain_spotlight if @spotlight_count > 0
          end
        end
      end

      def can_perform_special?
        # Special performances can be executed when stage presence is high enough
        @stage_presence >= 2
      end

      def calculate_stage_bonus
        # Calculate bonus based on number of Seisho cards in team
        seisho_count = team.count { |c| c.school == :seisho }
        @stage_presence * (seisho_count - 1)
      end

      def apply_school_effects
        # Apply school-specific effects
        @school_effects.each do |effect|
          case effect.type
          when :performance_boost
            performance_boost(effect.value)
          when :presence_boost
            presence_boost(effect.value)
          when :spotlight_boost
            gain_spotlight
          when :team_support
            apply_seisho_effects
          end
        end
      end

      def to_s
        "#{name} (Seisho #{role.capitalize}) - Performance: #{@performance_power}, Presence: #{@stage_presence}, Spotlights: #{@spotlight_count}"
      end
    end
  end
end 