module CardBattle
  module Schools
    class SiegfeldCard < Card
      attr_reader :precision_power, :tactical_advantage, :critical_rate

      def initialize(mana_cost:, name:, role:, rarity:, effects: [], skills: [], school_effects: [])
        super(mana_cost: mana_cost, name: name, role: role, rarity: rarity, effects: effects, skills: skills)
        @school = :siegfeld
        @precision_power = 0
        @tactical_advantage = 0
        @critical_rate = 0.05  # Base 5% critical rate
        @school_effects = school_effects
      end

      def precision_boost(value)
        @precision_power += value
        # Precision power affects critical rate and damage accuracy
        @critical_rate += (value * 0.01)  # Each point of precision increases crit rate by 1%
        self
      end

      def tactical_boost(value)
        @tactical_advantage += value
        # Tactical advantage affects damage multiplier and skill effectiveness
        self
      end

      def critical_boost(value)
        @critical_rate += value
        # Direct critical rate boost
        self
      end

      def damage
        # Base damage is modified by precision and tactical advantage
        base = super
        precision_bonus = @precision_power * 0.3
        tactical_bonus = @tactical_advantage * 0.4
        
        # Critical hit calculation
        if rand < @critical_rate
          base = base * 2.0  # Critical hits deal double damage
        end
        
        base + precision_bonus + tactical_bonus
      end

      def apply_siegfeld_effects
        # Apply effects to other Siegfeld cards in the team
        team.each do |card|
          if card.respond_to?(:school) && card.school == :siegfeld
            # Precision power is shared among <PERSON>egfeld cards
            card.precision_boost(@precision_power * 0.2)
            # Tactical advantage affects the entire team
            card.tactical_boost(@tactical_advantage * 0.1)
            # Critical rate bonus is always active
            card.critical_boost(@critical_rate * 0.1)
          end
        end
      end

      def can_execute_tactic?
        # Tactics can be executed when tactical advantage is high enough
        @tactical_advantage >= 2
      end

      def calculate_tactical_bonus
        # Calculate bonus based on number of Siegfeld cards in team
        siegfeld_count = team.count { |c| c.school == :siegfeld }
        @tactical_advantage * (siegfeld_count - 1)
      end

      def apply_school_effects
        # Apply school-specific effects
        @school_effects.each do |effect|
          case effect.type
          when :precision_boost
            precision_boost(effect.value)
          when :tactical_boost
            tactical_boost(effect.value)
          when :critical_boost
            critical_boost(effect.value)
          when :team_support
            apply_siegfeld_effects
          end
        end
      end

      def to_s
        "#{name} (Siegfeld #{role.capitalize}) - Precision: #{@precision_power}, Tactical: #{@tactical_advantage}, Crit: #{(@critical_rate * 100).round(1)}%"
      end
    end
  end
end 