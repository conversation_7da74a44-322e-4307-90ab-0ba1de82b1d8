module CardBattle
  module Schools
    class SeiranCard < Card
      attr_reader :grace_power, :elegance_level, :flower_count

      def initialize(mana_cost:, name:, role:, rarity:, effects: [], skills: [], school_effects: [])
        super(
          mana_cost: mana_cost,
          name: name,
          school: :seiran,
          role: role,
          rarity: rarity,
          effects: effects,
          skills: skills
        )
        @grace_power = 0
        @elegance_level = 0
        @flower_count = 0  # Tracks number of flower effects (special bonuses)
        @school_effects = school_effects
      end

      def grace_boost(value)
        @grace_power += value
        # Grace power affects damage and skill effectiveness
        self
      end

      def elegance_boost(value)
        @elegance_level += value
        # Elegance level affects flower generation and team support
        self
      end

      def gain_flower
        @flower_count += 1
        # Each flower provides a unique bonus
        self
      end

      def damage
        # Base damage is modified by grace power and elegance level
        base = super
        grace_bonus = @grace_power * 0.4
        elegance_bonus = @elegance_level * 0.3
        
        # Flower bonus
        flower_bonus = @flower_count * 0.2
        
        base + grace_bonus + elegance_bonus + flower_bonus
      end

      def apply_seiran_effects
        # Apply effects to other Seiran cards in the team
        team.each do |card|
          if card.respond_to?(:school) && card.school == :seiran
            # Grace power is shared among Seiran cards
            card.grace_boost(@grace_power * 0.2)
            # Elegance affects the entire team
            card.elegance_boost(@elegance_level * 0.1)
            # Flower effects are always active
            card.gain_flower if @flower_count > 0
          end
        end
      end

      def can_perform_special?
        # Special performances can be executed when elegance level is high enough
        @elegance_level >= 2
      end

      def calculate_grace_bonus
        # Calculate bonus based on number of Seiran cards in team
        seiran_count = team.count { |c| c.school == :seiran }
        @grace_power * (seiran_count - 1)
      end

      def apply_school_effects
        # Apply school-specific effects
        @school_effects.each do |effect|
          case effect.type
          when :grace_boost
            grace_boost(effect.value)
          when :elegance_boost
            elegance_boost(effect.value)
          when :flower_boost
            gain_flower
          when :team_support
            apply_seiran_effects
          end
        end
      end

      def to_s
        "#{name} (Seiran #{role.capitalize}) - Grace: #{@grace_power}, Elegance: #{@elegance_level}, Flowers: #{@flower_count}"
      end
    end
  end
end 