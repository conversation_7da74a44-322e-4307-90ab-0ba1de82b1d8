module CardBattle
  module Schools
    class FrontierCard < Card
      attr_reader :speed_power, :combo_count, :performance_chain

      def initialize(mana_cost:, name:, role:, rarity:, effects: [], skills: [], school_effects: [])
        super(mana_cost: mana_cost, name: name, role: role, rarity: rarity, effects: effects, skills: skills)
        @school = :frontier
        @speed_power = 0
        @combo_count = 0
        @performance_chain = 0
        @school_effects = school_effects
      end

      def speed_boost(value)
        @speed_power += value
        # Speed power affects both damage and skill usage speed
        self
      end

      def combo_boost(value)
        @combo_count += value
        # Combo count affects damage multiplier and skill chain potential
        self
      end

      def chain_boost(value)
        @performance_chain += value
        # Performance chain affects team-wide bonuses
        self
      end

      def damage
        # Base damage is modified by speed power and combo count
        base = super
        speed_bonus = @speed_power * 0.4
        combo_bonus = @combo_count * 0.3
        chain_bonus = @performance_chain * 0.2
        base + speed_bonus + combo_bonus + chain_bonus
      end

      def apply_frontier_effects
        # Apply effects to other Frontier cards in the team
        team.each do |card|
          if card.respond_to?(:school) && card.school == :frontier
            # Speed power is shared among Frontier cards
            card.speed_boost(@speed_power * 0.2)
            # Combo boosts affect the entire team
            card.combo_boost(@combo_count * 0.1)
            # Chain bonus is always active
            card.chain_boost(@performance_chain)
          end
        end
      end

      def can_chain_skill?
        # Skills can be chained when combo count is high enough
        @combo_count >= 2
      end

      def calculate_chain_bonus
        # Calculate bonus based on number of Frontier cards in team
        frontier_count = team.count { |c| c.school == :frontier }
        @performance_chain * (frontier_count - 1)
      end

      def apply_school_effects
        # Apply school-specific effects
        @school_effects.each do |effect|
          case effect.type
          when :speed_boost
            speed_boost(effect.value)
          when :combo_boost
            combo_boost(effect.value)
          when :chain_boost
            chain_boost(effect.value)
          when :team_support
            apply_frontier_effects
          end
        end
      end

      def to_s
        "#{name} (Frontier #{role.capitalize}) - Speed: #{@speed_power}, Combo: #{@combo_count}, Chain: #{@performance_chain}"
      end
    end
  end
end 