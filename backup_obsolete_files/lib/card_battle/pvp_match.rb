module CardBattle
  # Manages PvP matches with ban/pick phase and stage girl teams
  class PvPMatch
    attr_reader :player1, :player2, :phase, :match_state, :turn_player

    TEAM_SIZE = 6
    BAN_COUNT = 2
    COLLECTION_SIZE = 10

    def initialize(player1_collection, player2_collection)
      @player1 = PvPPlayer.new("Player 1", player1_collection)
      @player2 = PvPPlayer.new("Player 2", player2_collection)
      @phase = :costume_selection
      @match_state = {
        current_player: @player1,
        bans_remaining: { @player1 => BAN_COUNT, @player2 => BAN_COUNT },
        picks_remaining: { @player1 => TEAM_SIZE, @player2 => TEAM_SIZE }
      }
      @turn_player = @player1
      @game = nil
    end

    def start_match
      puts "🎭 PvP MATCH STARTING"
      puts "#{@player1.name} vs #{@player2.name}"
      puts "=" * 50
      
      costume_selection_phase
      ban_pick_phase
      battle_phase
    end

    def costume_selection_phase
      puts "\n👗 COSTUME SELECTION PHASE"
      puts "-" * 30
      
      [@player1, @player2].each do |player|
        puts "\n#{player.name}'s Costume Selection:"
        select_costumes_for_player(player)
      end
      
      @phase = :ban_pick
    end

    def ban_pick_phase
      puts "\n🚫 BAN/PICK PHASE"
      puts "-" * 30
      
      # Ban phase - alternating bans
      ban_phase
      
      # Pick phase - form teams from remaining stage girls
      pick_phase
      
      @phase = :battle
    end

    def battle_phase
      puts "\n⚔️ BATTLE PHASE"
      puts "-" * 30
      
      # Convert stage girls to cards and create game
      player1_cards = @player1.team.map(&:to_card).compact
      player2_cards = @player2.team.map(&:to_card).compact
      
      @game = Game.new
      @game.players[0].instance_variable_set(:@hand, player1_cards)
      @game.players[1].instance_variable_set(:@hand, player2_cards)
      
      # Set player names
      @game.players[0].instance_variable_set(:@name, @player1.name)
      @game.players[1].instance_variable_set(:@name, @player2.name)
      
      puts "Battle ready! Teams formed:"
      display_team(@player1)
      display_team(@player2)
      
      @phase = :complete
    end

    def get_game
      @game
    end

    private

    def select_costumes_for_player(player)
      player.stage_girls.each do |name, stage_girl|
        available_costumes = stage_girl.available_costumes
        
        puts "  #{name} (#{stage_girl.school}):"
        available_costumes.each_with_index do |costume, index|
          puts "    #{index + 1}. #{costume.name} (#{costume.rarity}) - #{costume.role}"
        end
        
        # Auto-select first costume for simulation
        selected_costume = available_costumes.first
        stage_girl.select_costume(selected_costume.id)
        puts "    → Selected: #{selected_costume.name}"
      end
    end

    def ban_phase
      puts "\n🚫 Ban Phase - Each player bans #{BAN_COUNT} opposing stage girls"
      
      BAN_COUNT.times do |ban_round|
        [@player1, @player2].each do |banner|
          opponent = banner == @player1 ? @player2 : @player1
          
          available_targets = opponent.stage_girls.values.reject(&:banned?)
          next if available_targets.empty?
          
          # Auto-select ban target for simulation
          banned_girl = available_targets.sample
          banned_girl.banned = true
          
          puts "#{banner.name} bans #{banned_girl.name} (#{banned_girl.school})"
        end
      end
    end

    def pick_phase
      puts "\n✅ Pick Phase - Form teams of #{TEAM_SIZE} from remaining #{COLLECTION_SIZE - BAN_COUNT} stage girls"
      
      [@player1, @player2].each do |player|
        available_girls = player.stage_girls.values.reject(&:banned?)
        
        puts "\n#{player.name}'s available stage girls:"
        available_girls.each_with_index do |girl, index|
          puts "  #{index + 1}. #{girl.name} (#{girl.school}) - #{girl.selected_costume_name}"
        end
        
        # Auto-select team for simulation
        selected_team = available_girls.sample(TEAM_SIZE)
        player.team = selected_team
        
        puts "\n#{player.name}'s selected team:"
        selected_team.each_with_index do |girl, index|
          puts "  #{index + 1}. #{girl.name} - #{girl.selected_costume_name}"
        end
      end
    end

    def display_team(player)
      puts "\n#{player.name}'s Team:"
      player.team.each_with_index do |stage_girl, index|
        costume = stage_girl.selected_costume
        puts "  #{index + 1}. #{stage_girl.name} - #{costume.name}"
        puts "     School: #{stage_girl.school} | Role: #{costume.role}"
        puts "     Maneuver: #{costume.maneuver} | Element: #{costume.element}"
        puts "     Mana: #{costume.mana_requirements}"
      end
    end
  end

  # Represents a PvP player with their stage girl collection
  class PvPPlayer
    attr_reader :name, :stage_girls
    attr_accessor :team

    def initialize(name, stage_girl_collection)
      @name = name
      @stage_girls = stage_girl_collection
      @team = []
    end

    def available_stage_girls
      @stage_girls.values.reject(&:banned?)
    end

    def banned_stage_girls
      @stage_girls.values.select(&:banned?)
    end

    def team_ready?
      @team.size == PvPMatch::TEAM_SIZE && @team.all?(&:available?)
    end
  end

  # Factory for creating PvP matches
  class PvPMatchFactory
    class << self
      def create_random_match
        # Initialize the database if not already done
        StageGirlDatabase.initialize_database
        
        # Create collections for both players
        player1_collection = StageGirlDatabase.create_player_collection
        player2_collection = StageGirlDatabase.create_player_collection
        
        # Randomly select 10 stage girls for each player
        all_girls = StageGirlDatabase.get_all_stage_girls.map(&:name)
        
        player1_selection = all_girls.sample(10)
        player2_selection = all_girls.sample(10)
        
        # Filter collections to selected girls
        player1_filtered = player1_collection.select { |name, _| player1_selection.include?(name) }
        player2_filtered = player2_collection.select { |name, _| player2_selection.include?(name) }
        
        PvPMatch.new(player1_filtered, player2_filtered)
      end

      def create_school_vs_school_match(school1, school2)
        StageGirlDatabase.initialize_database
        
        # Get all stage girls from each school
        school1_girls = StageGirlDatabase.get_stage_girls_by_school(school1)
        school2_girls = StageGirlDatabase.get_stage_girls_by_school(school2)
        
        # Create collections
        player1_collection = {}
        player2_collection = {}
        
        # Add school girls and fill to 10 with random others
        school1_girls.each { |sg| player1_collection[sg.name] = sg }
        school2_girls.each { |sg| player2_collection[sg.name] = sg }
        
        # Fill remaining slots with random girls
        all_girls = StageGirlDatabase.get_all_stage_girls
        remaining_girls = all_girls.reject { |sg| 
          player1_collection.key?(sg.name) || player2_collection.key?(sg.name) 
        }
        
        while player1_collection.size < 10 && remaining_girls.any?
          girl = remaining_girls.shift
          player1_collection[girl.name] = girl
        end
        
        while player2_collection.size < 10 && remaining_girls.any?
          girl = remaining_girls.shift
          player2_collection[girl.name] = girl
        end
        
        # Create fresh collections for the match
        player1_fresh = StageGirlDatabase.create_player_collection
        player2_fresh = StageGirlDatabase.create_player_collection
        
        player1_filtered = player1_fresh.select { |name, _| player1_collection.key?(name) }
        player2_filtered = player2_fresh.select { |name, _| player2_collection.key?(name) }
        
        match = PvPMatch.new(player1_filtered, player2_filtered)
        match.player1.instance_variable_set(:@name, "#{school1.to_s.capitalize} Academy")
        match.player2.instance_variable_set(:@name, "#{school2.to_s.capitalize} Institute")
        
        match
      end

      def create_custom_match(player1_girls, player2_girls)
        StageGirlDatabase.initialize_database
        
        # Create collections with specified girls
        full_collection1 = StageGirlDatabase.create_player_collection
        full_collection2 = StageGirlDatabase.create_player_collection
        
        player1_collection = full_collection1.select { |name, _| player1_girls.include?(name) }
        player2_collection = full_collection2.select { |name, _| player2_girls.include?(name) }
        
        PvPMatch.new(player1_collection, player2_collection)
      end
    end
  end

  # Statistics and analysis for PvP matches
  class PvPAnalyzer
    def self.analyze_match(match)
      puts "\n📊 MATCH ANALYSIS"
      puts "=" * 40
      
      analyze_team_composition(match.player1, "Player 1")
      analyze_team_composition(match.player2, "Player 2")
      
      analyze_school_distribution(match)
      analyze_role_distribution(match)
      analyze_element_distribution(match)
    end

    def self.analyze_team_composition(player, label)
      puts "\n#{label} Team Composition:"
      
      schools = player.team.map(&:school)
      school_counts = schools.each_with_object(Hash.new(0)) { |school, counts| counts[school] += 1 }
      
      puts "  Schools: #{school_counts}"
      
      roles = player.team.map { |sg| sg.selected_costume.role }
      role_counts = roles.each_with_object(Hash.new(0)) { |role, counts| counts[role] += 1 }
      
      puts "  Roles: #{role_counts}"
      
      elements = player.team.map(&:get_element).compact
      element_counts = elements.each_with_object(Hash.new(0)) { |element, counts| counts[element] += 1 }
      
      puts "  Elements: #{element_counts}"
    end

    def self.analyze_school_distribution(match)
      puts "\nSchool Distribution:"
      all_schools = (match.player1.team + match.player2.team).map(&:school)
      school_counts = all_schools.each_with_object(Hash.new(0)) { |school, counts| counts[school] += 1 }
      
      school_counts.each do |school, count|
        puts "  #{school.to_s.capitalize}: #{count} stage girls"
      end
    end

    def self.analyze_role_distribution(match)
      puts "\nRole Distribution:"
      all_roles = (match.player1.team + match.player2.team).map { |sg| sg.selected_costume.role }
      role_counts = all_roles.each_with_object(Hash.new(0)) { |role, counts| counts[role] += 1 }
      
      role_counts.each do |role, count|
        puts "  #{role.to_s.capitalize}: #{count} stage girls"
      end
    end

    def self.analyze_element_distribution(match)
      puts "\nElement Distribution:"
      all_elements = (match.player1.team + match.player2.team).map(&:get_element).compact
      element_counts = all_elements.each_with_object(Hash.new(0)) { |element, counts| counts[element] += 1 }
      
      element_counts.each do |element, count|
        puts "  #{element.to_s.capitalize}: #{count} stage girls"
      end
    end
  end
end
