require 'spec_helper'

RSpec.describe CardBattle::Player do
  let(:player) { described_class.new }

  describe '#initialize' do
    it 'starts with 30 health' do
      expect(player.health).to eq(30)
    end

    it 'starts with 0 mana slots' do
      expect(player.mana_slots).to eq(0)
    end

    it 'starts with 0 current mana' do
      expect(player.current_mana).to eq(0)
    end

    it 'starts with a deck of 17 cards (after drawing initial hand)' do
      expect(player.deck_size).to eq(17)
    end

    it 'starts with 3 cards in hand' do
      expect(player.hand_size).to eq(3)
    end
  end

  describe '#deck' do
    it 'contains the correct number of cards and valid mana costs after initial hand is drawn' do
      # The deck should have 17 cards, all with mana costs from the original set
      expect(player.deck_size).to eq(17)
      valid_costs = [0, 0, 1, 1, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 5, 5, 6, 6, 7, 8]
      all_costs = player.deck.map(&:mana_cost) + player.hand.map(&:mana_cost)
      expect(all_costs.sort).to eq(valid_costs)
    end
  end

  describe '#start_turn' do
    it 'increases mana slots by 1 (up to 10)' do
      5.times { player.start_turn }
      expect(player.mana_slots).to eq(5)
      
      10.times { player.start_turn }
      expect(player.mana_slots).to eq(10)
    end

    it 'refills mana to match mana slots' do
      player.start_turn
      expect(player.current_mana).to eq(1)
      
      player.start_turn
      expect(player.current_mana).to eq(2)
    end

    it 'draws a card' do
      initial_hand_size = player.hand_size
      initial_deck_size = player.deck_size
      
      player.start_turn
      
      expect(player.hand_size).to eq(initial_hand_size + 1)
      expect(player.deck_size).to eq(initial_deck_size - 1)
    end

    context 'when deck is empty' do
      it 'takes 1 damage instead of drawing' do
        player.instance_variable_set(:@deck, [])
        initial_health = player.health
        
        player.start_turn
        
        expect(player.health).to eq(initial_health - 1)
        expect(player.hand_size).to eq(3) # Hand size shouldn't change
      end
    end

    context 'when hand would exceed 5 cards' do
      it 'discards the drawn card' do
        # Fill hand to 5 cards
        player.instance_variable_set(:@hand, Array.new(5) { CardBattle::Card.new(mana_cost: 1) })
        
        player.start_turn
        
        expect(player.hand_size).to eq(5)
      end
    end
  end

  describe '#take_damage' do
    it 'reduces health by the damage amount' do
      player.take_damage(5)
      expect(player.health).to eq(25)
    end

    it 'can reduce health to 0' do
      player.take_damage(30)
      expect(player.health).to eq(0)
    end

    it 'cannot reduce health below 0' do
      player.take_damage(40)
      expect(player.health).to eq(0)
    end
  end

  describe '#play_card' do
    let(:card) { CardBattle::Card.new(mana_cost: 3) }

    before do
      player.instance_variable_set(:@current_mana, 5)
      player.instance_variable_set(:@hand, [card])
    end

    it 'removes the card from hand' do
      expect { player.play_card(card) }.to change { player.hand_size }.by(-1)
    end

    it 'reduces current mana by card cost' do
      expect { player.play_card(card) }.to change { player.current_mana }.by(-3)
    end

    it 'returns the played card' do
      expect(player.play_card(card)).to eq(card)
    end

    context 'when not enough mana' do
      before do
        player.instance_variable_set(:@current_mana, 2)
      end

      it 'raises an error' do
        expect { player.play_card(card) }.to raise_error(CardBattle::Error, 'Not enough mana')
      end
    end

    context 'when card is not in hand' do
      let(:other_card) { CardBattle::Card.new(mana_cost: 3) }

      it 'raises an error' do
        expect { player.play_card(other_card) }.to raise_error(CardBattle::Error, 'Card not in hand')
      end
    end
  end

  describe '#dead?' do
    it 'returns true when health is 0' do
      player.take_damage(30)
      expect(player).to be_dead
    end

    it 'returns false when health is above 0' do
      expect(player).not_to be_dead
    end
  end
end 