require 'spec_helper'

RSpec.describe CardBattle::Card do
  describe '#initialize' do
    it 'creates a card with a mana cost' do
      card = described_class.new(mana_cost: 3)
      expect(card.mana_cost).to eq(3)
    end

    it 'raises an error for invalid mana costs' do
      expect { described_class.new(mana_cost: -1) }.to raise_error(ArgumentError)
      expect { described_class.new(mana_cost: 9) }.to raise_error(ArgumentError)
    end
  end

  describe '#damage' do
    it 'returns damage equal to mana cost for normal cards' do
      card = described_class.new(mana_cost: 3)
      expect(card.damage).to eq(3)
    end

    it 'returns 0 damage for 0-cost cards' do
      card = described_class.new(mana_cost: 0)
      expect(card.damage).to eq(0)
    end
  end

  describe '#playable?' do
    it 'returns true if player has enough mana' do
      card = described_class.new(mana_cost: 3)
      expect(card.playable?(available_mana: 5)).to be true
    end

    it 'returns false if player has insufficient mana' do
      card = described_class.new(mana_cost: 3)
      expect(card.playable?(available_mana: 2)).to be false
    end

    it 'returns true for 0-cost cards regardless of mana' do
      card = described_class.new(mana_cost: 0)
      expect(card.playable?(available_mana: 0)).to be true
    end
  end
end 