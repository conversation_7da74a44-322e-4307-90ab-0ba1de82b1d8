require 'spec_helper'

RSpec.describe 'Enhanced TCG Mechanics' do
  let(:player) { CardBattle::Player.new }
  let(:opponent) { CardBattle::Player.new }
  let(:game) { CardBattle::Game.new }

  describe 'Multi-mana system' do
    it 'initializes players with 50% starting mana' do
      expected_starting_mana = (CardBattle::Player::BASE_MANA_PER_TYPE * 0.5).to_i
      
      CardBattle::Player::MANA_TYPES.each do |type|
        expect(player.mana_pools[type][:current]).to eq(expected_starting_mana)
        expect(player.mana_pools[type][:max]).to eq(CardBattle::Player::BASE_MANA_PER_TYPE)
      end
    end

    it 'allows spending and gaining mana' do
      expect(player.mana_available?(:magic, 3)).to be true
      expect(player.spend_mana(:magic, 3)).to be true
      expect(player.mana_pools[:magic][:current]).to eq(2)
      
      expect(player.gain_mana(:magic, 2)).to be true
      expect(player.mana_pools[:magic][:current]).to eq(4)
    end

    it 'prevents overspending mana' do
      expect(player.spend_mana(:magic, 10)).to be false
      expect(player.mana_pools[:magic][:current]).to eq(5)
    end

    it 'allows mana stealing between players' do
      initial_player_mana = player.mana_pools[:attack][:current]
      initial_opponent_mana = opponent.mana_pools[:attack][:current]
      
      stolen = player.steal_mana(opponent, :attack, 2)
      
      expect(stolen).to eq(2)
      expect(player.mana_pools[:attack][:current]).to eq(initial_player_mana + 2)
      expect(opponent.mana_pools[:attack][:current]).to eq(initial_opponent_mana - 2)
    end
  end

  describe 'Chain combo system' do
    it 'starts with 0% combo meter' do
      expect(player.chain_combo_meter).to eq(0)
    end

    it 'builds combo meter and allows usage at 50%' do
      expect(player.can_use_chain_combo?).to be false
      
      player.increase_chain_combo_meter(50)
      expect(player.chain_combo_meter).to eq(50)
      expect(player.can_use_chain_combo?).to be true
      
      expect(player.use_chain_combo(:offensive_burst)).to be true
      expect(player.chain_combo_meter).to eq(0)
    end

    it 'caps combo meter at 100%' do
      player.increase_chain_combo_meter(150)
      expect(player.chain_combo_meter).to eq(100)
    end
  end

  describe 'Attribute mechanics' do
    let(:flower_card) { CardBattle::Cards::FlowerKnightCard.new }
    let(:wind_card) { CardBattle::Cards::WindSorceressCard.new }
    let(:ice_card) { CardBattle::Cards::IceAssassinCard.new }

    it 'calculates attribute effectiveness correctly' do
      # Flower beats Wind
      effectiveness = CardBattle::AttributeMechanics.calculate_attribute_effectiveness(:flower, :wind)
      expect(effectiveness).to eq(:strong)

      # Wind beats Cloud
      effectiveness = CardBattle::AttributeMechanics.calculate_attribute_effectiveness(:wind, :cloud)
      expect(effectiveness).to eq(:strong)

      # Ice beats Flower
      effectiveness = CardBattle::AttributeMechanics.calculate_attribute_effectiveness(:ice, :flower)
      expect(effectiveness).to eq(:strong)

      # Same attribute is neutral
      effectiveness = CardBattle::AttributeMechanics.calculate_attribute_effectiveness(:flower, :flower)
      expect(effectiveness).to eq(:neutral)
    end
  end

  describe 'Equipment system' do
    let(:lucky_charm) { CardBattle::Equipment::LuckyCharm.new }
    let(:poison_dagger) { CardBattle::Equipment::PoisonDagger.new }

    it 'allows equipping and unequipping items' do
      player.instance_variable_set(:@hand, [lucky_charm])
      
      expect(player.equip_item(lucky_charm)).to be true
      expect(player.equipment).to include(lucky_charm)
      expect(player.hand).not_to include(lucky_charm)
      
      expect(player.unequip_item(lucky_charm)).to be true
      expect(player.equipment).not_to include(lucky_charm)
    end

    it 'limits equipment slots to 3' do
      3.times do |i|
        equipment = CardBattle::Equipment::LuckyCharm.new
        player.instance_variable_set(:@hand, [equipment])
        expect(player.equip_item(equipment)).to be true
      end
      
      fourth_equipment = CardBattle::Equipment::LuckyCharm.new
      player.instance_variable_set(:@hand, [fourth_equipment])
      expect(player.equip_item(fourth_equipment)).to be false
    end

    it 'handles equipment durability and breaking' do
      expect(poison_dagger.durability).to eq(4)
      expect(poison_dagger.broken?).to be false
      
      4.times { poison_dagger.send(:reduce_durability) }
      
      expect(poison_dagger.broken?).to be true
      expect(poison_dagger.durability).to eq(0)
    end
  end

  describe 'Enhanced card mechanics' do
    let(:adaptive_card) { CardBattle::Cards::AdaptivePaladinCard.new }
    let(:combo_card) { CardBattle::Cards::ComboMasterCard.new }

    it 'validates multi-mana requirements' do
      # Card requires defense: 3, healing: 1, light: 1
      expect(adaptive_card.playable?(player: player)).to be false  # Not enough mana
      
      # Give player enough mana
      player.gain_mana(:defense, 5)
      player.gain_mana(:healing, 5)
      player.gain_mana(:light, 5)
      
      expect(adaptive_card.playable?(player: player)).to be true
    end

    it 'tracks card usage for fatigue system' do
      expect(combo_card.usage_count).to eq(0)
      expect(combo_card.fatigued?).to be false
      
      combo_card.increment_usage
      expect(combo_card.usage_count).to eq(1)
    end

    it 'applies status effects correctly' do
      adaptive_card.apply_status_effect(:adaptive, 3)
      expect(adaptive_card.adaptive?).to be true
      expect(adaptive_card.fatigue_turns).to eq(0)
      
      adaptive_card.apply_fatigue(2)
      expect(adaptive_card.fatigued?).to be true
      expect(adaptive_card.fatigue_turns).to eq(2)
    end

    it 'records damage history for adaptive mechanics' do
      adaptive_card.record_damage(:fire, 5)
      adaptive_card.record_damage(:ice, 3)
      
      expect(adaptive_card.instance_variable_get(:@damage_history).size).to eq(2)
      expect(adaptive_card.instance_variable_get(:@damage_history).first[:type]).to eq(:fire)
    end
  end

  describe 'Enhanced effects' do
    it 'generates mana through effects' do
      effect = CardBattle::Effects::ManaGenerationEffect.new(
        mana_distributions: { magic: 2, attack: 1 }
      )
      
      initial_magic = player.mana_pools[:magic][:current]
      initial_attack = player.mana_pools[:attack][:current]
      
      effect.apply(player, player)
      
      expect(player.mana_pools[:magic][:current]).to eq(initial_magic + 2)
      expect(player.mana_pools[:attack][:current]).to eq(initial_attack + 1)
    end

    it 'steals mana through effects' do
      effect = CardBattle::Effects::ManaStealEffect.new(
        mana_type: :magic,
        steal_amount: 2
      )
      
      initial_player_magic = player.mana_pools[:magic][:current]
      initial_opponent_magic = opponent.mana_pools[:magic][:current]
      
      effect.apply(opponent, player)
      
      expect(player.mana_pools[:magic][:current]).to eq(initial_player_magic + 2)
      expect(opponent.mana_pools[:magic][:current]).to eq(initial_opponent_magic - 2)
    end

    it 'boosts chain combo meter through effects' do
      effect = CardBattle::Effects::ChainComboBoostEffect.new(boost_amount: 15)
      
      expect(player.chain_combo_meter).to eq(0)
      effect.apply(player, player)
      expect(player.chain_combo_meter).to eq(15)
    end
  end

  describe 'Game integration' do
    it 'validates mana requirements when playing cards' do
      card = CardBattle::Cards::AdaptivePaladinCard.new
      game.current_player.instance_variable_set(:@hand, [card])
      
      # Should fail due to insufficient mana
      expect(game.play_card(card)).to be false
    end

    it 'generates mana based on actions' do
      card = CardBattle::Cards::FlowerKnightCard.new
      game.current_player.instance_variable_set(:@hand, [card])
      
      # Give enough mana to play the card
      game.current_player.gain_mana(:flower, 5)
      
      initial_flower_mana = game.current_player.mana_pools[:flower][:current]
      
      game.play_card(card)
      
      # Should generate some mana from playing the card
      expect(game.current_player.mana_pools[:flower][:current]).to be >= initial_flower_mana - 2
    end
  end
end
