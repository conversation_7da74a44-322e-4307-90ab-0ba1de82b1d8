require 'spec_helper'

RSpec.describe CardBattle::Game do
  let(:game) { described_class.new }

  describe '#initialize' do
    it 'creates two players' do
      expect(game.players.size).to eq(2)
      expect(game.players[0]).to be_a(CardBattle::Player)
      expect(game.players[1]).to be_a(CardBattle::Player)
    end

    it 'randomly selects the starting player' do
      expect([0, 1]).to include(game.current_player_index)
    end

    it 'gives the second player an extra card' do
      # The non-starting player should have 4 cards in hand
      other_index = 1 - game.current_player_index
      expect(game.players[other_index].hand_size).to eq(4)
    end
  end

  describe '#current_player and #opponent' do
    it 'returns the correct player and opponent' do
      expect(game.current_player).to eq(game.players[game.current_player_index])
      expect(game.opponent).to eq(game.players[1 - game.current_player_index])
    end
  end

  describe '#end_turn' do
    it 'switches the current player' do
      current = game.current_player
      game.end_turn
      expect(game.current_player).not_to eq(current)
    end

    it 'starts the new player\'s turn' do
      game.end_turn
      expect(game.current_player.current_mana).to eq(game.current_player.mana_slots)
    end
  end

  describe '#play_card' do
    it 'lets the current player play a card and deals damage to the opponent' do
      card = game.current_player.hand.find { |c| c.mana_cost > 0 }
      game.current_player.instance_variable_set(:@current_mana, card.mana_cost)
      opponent = game.opponent
      initial_health = opponent.health
      game.play_card(card)
      expect(opponent.health).to eq(initial_health - card.damage)
      expect(game.current_player.hand).not_to include(card)
    end
  end

  describe '#winner' do
    it 'returns nil if no player is dead' do
      expect(game.winner).to be_nil
    end

    it 'returns the winning player if one player is dead' do
      game.opponent.take_damage(30)
      expect(game.winner).to eq(game.current_player)
    end
  end
end 