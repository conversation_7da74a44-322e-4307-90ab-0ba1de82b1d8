#!/usr/bin/env ruby

require_relative 'lib/card_battle'

puts "🎭 REVUE STARLIGHT: RELIVE - SIMPLIFIED PLAYTEST"
puts "=" * 60

# Available stage girls for quick selection with their schools
STAGE_GIRLS = {
  "<PERSON>" => "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>" => "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>" => "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>" => "Seisho",
  "Maya" => "<PERSON>ish<PERSON>",
  "Junna" => "Seisho",
  "Nana" => "Seisho",
  "Futaba" => "Rinmeikan",
  "Kaoruko" => "Rinmeikan"
}

def create_quick_team(team_name, girl_names, team_number)
  puts "\n👥 Creating #{team_name}..."
  team = []

  girl_names.each_with_index do |girl_name, index|
    school = STAGE_GIRLS[girl_name] || "Unknown"

    # Create stage girl
    stage_girl = CardBattle::StageGirl.new(name: girl_name, school: school)

    # Add a basic costume
    costume = CardBattle::Costume.new(
      id: "#{girl_name.downcase}_uniform",
      name: "School Uniform",
      rarity: :common,
      role: :unit,
      element: :star,
      skills: []
    )
    stage_girl.add_costume(costume)
    stage_girl.select_costume(costume.id)

    # Create battle unit
    battle_unit = CardBattle::BattleUnit.new(stage_girl, team_number, index)

    puts "  #{girl_name} (#{school}): Ready for battle"
    team << battle_unit
  end

  team
end

def display_team_status(team, team_name)
  puts "\n#{team_name} Status:"
  team.each do |unit|
    status = unit.alive? ? "ALIVE" : "DEFEATED"
    brilliance_bar = "█" * (unit.brilliance / 10) + "░" * (10 - unit.brilliance / 10)
    puts "  #{unit.name}: #{unit.current_hp}/#{unit.max_hp} HP | Brilliance: #{unit.brilliance}/100 [#{brilliance_bar}] [#{status}]"
  end
end

def display_available_acts(unit)
  puts "\n  Available ACTs for #{unit.name}:"
  
  # Load real card data to show authentic ACTs
  begin
    act1 = unit.act1
    act2 = unit.act2  
    act3 = unit.act3
    climax = unit.climax_act
    
    puts "    ACT1: #{act1.name} (Cost: #{act1.cost}, Power: #{act1.power})"
    puts "    ACT2: #{act2.name} (Cost: #{act2.cost}, Power: #{act2.power})"
    puts "    ACT3: #{act3.name} (Cost: #{act3.cost}, Power: #{act3.power})"
    
    if unit.brilliance >= 100
      puts "    CLIMAX: #{climax.name} (Cost: #{climax.cost}, Power: #{climax.power}) ⭐"
    else
      puts "    CLIMAX: #{climax.name} (Requires 100 Brilliance - Current: #{unit.brilliance})"
    end
  rescue => e
    puts "    [Error loading ACTs: #{e.message}]"
  end
end

def run_quick_battle
  puts "\n⚔️  STARTING QUICK BATTLE..."

  # Create teams
  team1 = create_quick_team("Team Seisho", ["Karen", "Hikari", "Mahiru"], 1)
  team2 = create_quick_team("Team Siegfeld", ["Claudine", "Maya", "Junna"], 2)
  
  # Initialize battle
  battle = CardBattle::AuthenticPvpBattle.new(team1, team2)
  
  puts "\n🎭 BATTLE BEGINS!"
  puts "Team sizes: #{team1.size} vs #{team2.size}"
  
  # Show initial team status
  display_team_status(team1, "Team Seisho")
  display_team_status(team2, "Team Siegfeld")
  
  # Run 3 turns for demonstration
  3.times do |turn|
    puts "\n" + "=" * 50
    puts "🎯 TURN #{turn + 1}"
    puts "=" * 50
    
    begin
      # Execute one turn
      battle.execute_turn
      
      # Show updated status
      display_team_status(team1, "Team Seisho")
      display_team_status(team2, "Team Siegfeld")
      
      # Check for victory
      if battle.battle_over?
        winner = battle.winner
        puts "\n🏆 BATTLE OVER! #{winner} WINS!"
        break
      end
      
    rescue => e
      puts "❌ Error during turn: #{e.message}"
      puts "Continuing to next turn..."
    end
  end
  
  puts "\n✅ QUICK BATTLE DEMONSTRATION COMPLETE!"
end

def show_card_data_sample
  puts "\n📋 REAL CARD DATA SAMPLE:"
  puts "-" * 40

  # Show a few examples of real card data
  sample_girls = ["Karen", "Hikari", "Mahiru"]

  sample_girls.each_with_index do |girl_name, index|
    puts "\n🎭 #{girl_name}:"

    begin
      school = STAGE_GIRLS[girl_name] || "Unknown"
      stage_girl = CardBattle::StageGirl.new(name: girl_name, school: school)

      # Add basic costume
      costume = CardBattle::Costume.new(
        id: "#{girl_name.downcase}_uniform",
        name: "School Uniform",
        rarity: :common,
        role: :unit,
        element: :star,
        skills: []
      )
      stage_girl.add_costume(costume)
      stage_girl.select_costume(costume.id)

      unit = CardBattle::BattleUnit.new(stage_girl, 1, index)
      display_available_acts(unit)

    rescue => e
      puts "  [Error loading data: #{e.message}]"
    end
  end
end

def interactive_menu
  loop do
    puts "\n🎭 REVUE STARLIGHT PLAYTEST MENU"
    puts "=" * 40
    puts "1. Quick Battle (3v3)"
    puts "2. Show Real Card Data Sample"
    puts "3. Team Status Demo"
    puts "4. Exit"
    print "\nSelect option (1-4): "
    
    choice = gets.chomp
    
    case choice
    when "1"
      run_quick_battle
    when "2"
      show_card_data_sample
    when "3"
      team = create_quick_team("Demo Team", ["Karen", "Hikari", "Mahiru"], 1)
      display_team_status(team, "Demo Team")
      team.each { |unit| display_available_acts(unit) }
    when "4"
      puts "\n🎭 Thanks for playtesting! Goodbye!"
      break
    else
      puts "❌ Invalid choice. Please select 1-4."
    end
  end
end

# Main execution
begin
  puts "\n🎮 SIMPLIFIED PLAYTEST READY!"
  puts "This script demonstrates:"
  puts "  ✅ Real card data loading (573 cards)"
  puts "  ✅ Authentic ACT names and costs"
  puts "  ✅ 3v3 quick battles"
  puts "  ✅ Brilliance system"
  puts "  ✅ HP tracking"
  puts "  ✅ Turn-based combat"
  
  interactive_menu
  
rescue => e
  puts "\n❌ PLAYTEST ERROR: #{e.message}"
  puts "Backtrace:"
  puts e.backtrace.first(5)
  puts "\n🔧 Make sure all card files are properly loaded."
end
