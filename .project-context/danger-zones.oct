===DANGER_ZONES===
// Critical areas requiring extreme caution in the TCG project
// VERSION: 1.0
// GUARDIAN: ARTEMIS (Boundary protection and risk management)

META:
  PURPOSE::"Identify and protect critical system boundaries"
  RISK_LEVEL::HIGH
  MONITORING::CONTINUOUS
  LAST_ASSESSMENT::"2025-01-16"

0.DEF:
  DANGER_ZONE::"Area where changes risk system integrity"
  MUTATION_RISK::"Probability of introducing breaking changes"
  SEMANTIC_CANCER::"Uncontrolled growth of complexity or inconsistency"
  BOUNDARY_VIOLATION::"Crossing established architectural limits"
  FIDELITY_BREACH::"Deviation from original game mechanics"

---

CRITICAL_ZONES:
  
  GAME_MECHANICS_CORE:
    RISK_LEVEL::CRITICAL
    DESCRIPTION::"Core battle system and PvP mechanics"
    PROTECTED_ELEMENTS::[
      "Attribute effectiveness (flower>wind>snow>flower)",
      "AGI-based turn order calculation", 
      "Brilliance bar and climax act system",
      "5v5 battle team composition",
      "6 ACT points per turn limit"
    ]
    MUTATION_TRIGGERS::[
      "Changing fundamental battle math",
      "Altering attribute relationships",
      "Modifying turn order logic",
      "Breaking ACT point economy"
    ]
    GUARDIAN_ANTIBODIES::[ATHENA_LOGIC, APOLLO_PRECISION]
    VIOLATION_RESPONSE::IMMEDIATE_ROLLBACK
    
  API_INTEGRATION_LAYER:
    RISK_LEVEL::HIGH
    DESCRIPTION::"Karth.top API communication and data contracts"
    PROTECTED_ELEMENTS::[
      "Stage girl data structure integrity",
      "Costume/memoir/accessory relationships",
      "API endpoint contracts",
      "Data transformation pipelines"
    ]
    MUTATION_TRIGGERS::[
      "Changing API response parsing",
      "Modifying data structure schemas",
      "Breaking ID mapping systems",
      "Altering caching mechanisms"
    ]
    GUARDIAN_ANTIBODIES::[HERMES_COMMUNICATION, ARTEMIS_BOUNDARIES]
    VIOLATION_RESPONSE::DATA_VALIDATION_FAILURE
    
  PICK_BAN_SYSTEM:
    RISK_LEVEL::HIGH
    DESCRIPTION::"PvP roster selection and team composition"
    PROTECTED_ELEMENTS::[
      "10-card initial roster",
      "2-card ban per player",
      "5-card final team composition",
      "No duplicate restrictions",
      "School mixing allowance"
    ]
    MUTATION_TRIGGERS::[
      "Changing roster or team sizes",
      "Modifying ban mechanics",
      "Adding duplicate restrictions",
      "Breaking school mixing rules"
    ]
    GUARDIAN_ANTIBODIES::[ATHENA_STRATEGY, ARTEMIS_BOUNDARIES]
    VIOLATION_RESPONSE::GAME_BALANCE_CORRUPTION

---

MODERATE_ZONES:
  
  RUBY_ARCHITECTURE:
    RISK_LEVEL::MEDIUM
    DESCRIPTION::"Core Ruby class structure and module organization"
    PROTECTED_ELEMENTS::[
      "StageGirl and Card class hierarchy",
      "Battle system inheritance patterns",
      "Module namespace organization",
      "Database integration patterns"
    ]
    MUTATION_TRIGGERS::[
      "Breaking class inheritance",
      "Violating module boundaries",
      "Changing core interfaces",
      "Introducing circular dependencies"
    ]
    GUARDIAN_ANTIBODIES::[HEPHAESTUS_STRUCTURE, APOLLO_PRECISION]
    VIOLATION_RESPONSE::ARCHITECTURE_WARNING
    
  ARTWORK_MANAGEMENT:
    RISK_LEVEL::MEDIUM
    DESCRIPTION::"Character artwork and asset management system"
    PROTECTED_ELEMENTS::[
      "Character-to-artwork mapping",
      "File path conventions",
      "Asset loading mechanisms",
      "Costume-artwork relationships"
    ]
    MUTATION_TRIGGERS::[
      "Breaking file path patterns",
      "Changing asset loading logic",
      "Modifying mapping algorithms",
      "Corrupting artwork databases"
    ]
    GUARDIAN_ANTIBODIES::[DEMETER_ORGANIZATION, HERMES_INTEGRATION]
    VIOLATION_RESPONSE::ASSET_CORRUPTION_WARNING

---

LOW_RISK_ZONES:
  
  DOCUMENTATION_LAYER:
    RISK_LEVEL::LOW
    DESCRIPTION::"Non-critical documentation and examples"
    PROTECTED_ELEMENTS::[
      "README files",
      "Example scripts",
      "Development guides",
      "Comment consistency"
    ]
    MUTATION_TRIGGERS::[
      "Inconsistent documentation style",
      "Outdated examples",
      "Missing code comments",
      "Broken documentation links"
    ]
    GUARDIAN_ANTIBODIES::[DEMETER_GROWTH, HERMES_COMMUNICATION]
    VIOLATION_RESPONSE::DOCUMENTATION_WARNING
    
  TESTING_INFRASTRUCTURE:
    RISK_LEVEL::LOW
    DESCRIPTION::"Test files and development utilities"
    PROTECTED_ELEMENTS::[
      "Test file organization",
      "Mock data consistency",
      "Development script functionality",
      "Debugging utilities"
    ]
    MUTATION_TRIGGERS::[
      "Breaking test isolation",
      "Inconsistent mock data",
      "Removing debug capabilities",
      "Test dependency issues"
    ]
    GUARDIAN_ANTIBODIES::[ARTEMIS_VALIDATION, APOLLO_PRECISION]
    VIOLATION_RESPONSE::TEST_QUALITY_WARNING

---

FORBIDDEN_SEQUENCES:
  
  SEMANTIC_CANCER_PATTERNS:
    CHAOS_CASCADE::[DIONYSUS, DIONYSUS, DIONYSUS]
    DESCRIPTION::"Uncontrolled complexity introduction"
    DETECTION::"Multiple simultaneous architecture changes"
    PREVENTION::"Single change per session rule"
    
    CONFLICT_SPIRAL::[ARES, ARES, ARES]
    DESCRIPTION::"Aggressive changes without consideration"
    DETECTION::"Breaking changes without compatibility"
    PREVENTION::"Backward compatibility requirements"
    
    PRECISION_CHAOS_OSCILLATION::[APOLLO, DIONYSUS, APOLLO]
    DESCRIPTION::"Alternating between strict and loose implementations"
    DETECTION::"Inconsistent code quality standards"
    PREVENTION::"Consistent style enforcement"

---

MONITORING_PROTOCOLS:
  
  CONTINUOUS_MONITORING:
    FREQUENCY::PER_COMMIT
    AUTOMATED_CHECKS::[
      "Game mechanics validation",
      "API contract compliance",
      "Architecture consistency",
      "Test coverage maintenance"
    ]
    ALERT_THRESHOLDS::[
      "Any critical zone modification",
      "Test coverage drop >5%",
      "Performance regression >10%",
      "API response format changes"
    ]
    
  PERIODIC_ASSESSMENT:
    FREQUENCY::WEEKLY
    MANUAL_REVIEWS::[
      "Code quality assessment",
      "Architecture evolution review",
      "Game balance validation",
      "Documentation currency check"
    ]
    STAKEHOLDER_INVOLVEMENT::USER_CONSULTATION

---

RECOVERY_PROTOCOLS:
  
  IMMEDIATE_RESPONSE:
    CRITICAL_VIOLATION::[
      "Stop all development",
      "Assess damage scope",
      "Implement rollback",
      "Validate system integrity"
    ]
    HIGH_RISK_VIOLATION::[
      "Isolate affected components",
      "Implement containment",
      "Plan remediation",
      "Execute controlled fix"
    ]
    
  LONG_TERM_RECOVERY:
    SYSTEM_HARDENING::[
      "Strengthen violated boundaries",
      "Improve detection mechanisms",
      "Update prevention protocols",
      "Enhance monitoring coverage"
    ]
    KNOWLEDGE_INTEGRATION::[
      "Document lessons learned",
      "Update danger zone definitions",
      "Refine guardian antibodies",
      "Improve team awareness"
    ]

===END_DANGER_ZONES===
