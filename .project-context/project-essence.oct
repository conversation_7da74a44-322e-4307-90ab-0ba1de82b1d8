===PROJECT_ESSENCE===
// Core identity and purpose of the Revue Starlight TCG project
// VERSION: 1.0
// ESSENCE_GUARDIAN: ZEUS (Authority and final decisions)

META:
  PROJECT_NAME::"Revue Starlight: ReLive TCG Reverse Engineering"
  CORE_MISSION::"Faithfully recreate the PvP battle system of Revue Starlight: ReLive"
  IMPLEMENTATION_LANGUAGE::RUBY
  PRIMARY_DATA_SOURCE::"https://karth.top"
  PROJECT_PHILOSOPHY::AUTHENTICITY+ELEGANCE+FUNCTIONALITY

0.DEF:
  ESSENCE::"The fundamental, unchangeable nature of the project"
  AUTHENTICITY::"Faithful recreation of original game mechanics"
  REVERSE_ENGINEERING::"Reconstructing system behavior from observation"
  STAGE_GIRL::"Character from Revue Starlight with multiple costume options"
  REVUE::"Competitive battle between stage girls"
  KARTH_TOP::"Primary API source for game data and mechanics"

---

CORE_IDENTITY:
  
  PROJECT_DNA:
    PRIMARY_GENOME::GAME_FIDELITY
    DESCRIPTION::"Absolute commitment to authentic recreation"
    MANIFESTATION::[
      "Exact attribute effectiveness calculations",
      "Precise turn order mechanics (AGI-based)",
      "Authentic brilliance and climax act systems",
      "Correct PvP team composition rules"
    ]
    MUTATION_RESISTANCE::ABSOLUTE
    
    SECONDARY_GENOME::TECHNICAL_EXCELLENCE
    DESCRIPTION::"High-quality Ruby implementation"
    MANIFESTATION::[
      "Clean, maintainable code architecture",
      "Comprehensive test coverage",
      "Efficient API integration",
      "Modular, extensible design"
    ]
    MUTATION_RESISTANCE::HIGH
    
    TERTIARY_GENOME::COMMUNITY_VALUE
    DESCRIPTION::"Useful tool for Revue Starlight community"
    MANIFESTATION::[
      "Accurate battle simulations",
      "Character and costume databases",
      "Tournament and match analysis",
      "Educational game mechanic insights"
    ]
    MUTATION_RESISTANCE::MEDIUM

---

FUNDAMENTAL_CONSTRAINTS:
  
  IMMUTABLE_LAWS:
    GAME_MECHANICS_FIDELITY:
      PRINCIPLE::"Original game mechanics are sacred"
      IMPLICATIONS::[
        "No 'improvements' to game balance",
        "No simplification of complex mechanics",
        "No deviation from established rules",
        "Research required before implementation"
      ]
      VIOLATION_CONSEQUENCE::PROJECT_FAILURE
      
    DATA_SOURCE_INTEGRITY:
      PRINCIPLE::"Karth.top is the authoritative data source"
      IMPLICATIONS::[
        "API contracts must be respected",
        "Data transformations must preserve meaning",
        "Caching must maintain consistency",
        "Updates must track source changes"
      ]
      VIOLATION_CONSEQUENCE::DATA_CORRUPTION
      
    RUBY_IMPLEMENTATION:
      PRINCIPLE::"Ruby is the chosen implementation language"
      IMPLICATIONS::[
        "Idiomatic Ruby patterns preferred",
        "Gem ecosystem leveraged appropriately",
        "Performance optimized within Ruby constraints",
        "Maintainability prioritized over micro-optimizations"
      ]
      VIOLATION_CONSEQUENCE::ARCHITECTURE_CHAOS

---

CORE_MECHANICS:
  
  BATTLE_SYSTEM_ESSENCE:
    TEAM_COMPOSITION::5_STAGE_GIRLS_PER_TEAM
    TURN_SYSTEM::AGI_BASED_INITIATIVE_ORDER
    ACTION_ECONOMY::6_ACT_POINTS_PER_TURN
    ATTRIBUTE_SYSTEM::[
      "FLOWER > WIND > SNOW > FLOWER",
      "MOON > SPACE > CLOUD > MOON", 
      "STAR and SUN neutral to all"
    ]
    SPECIAL_MECHANICS::[
      "Brilliance bar accumulation",
      "Climax act requirements",
      "Finish act conditions",
      "Passive effect interactions"
    ]
    
  PVP_SYSTEM_ESSENCE:
    ROSTER_SELECTION::10_STAGE_GIRLS_INITIAL
    BAN_PHASE::2_BANS_PER_PLAYER
    TEAM_FORMATION::5_GIRLS_FROM_REMAINING_8
    CONSTRAINTS::[
      "No duplicate restrictions",
      "School mixing allowed",
      "Costume selection per character",
      "Equipment and memoir assignment"
    ]
    
  DATA_INTEGRATION_ESSENCE:
    CHARACTER_DATA::STAGE_GIRLS_WITH_MULTIPLE_COSTUMES
    SKILL_DATA::COSTUME_SPECIFIC_ABILITIES
    EQUIPMENT_DATA::[
      "Memoirs (one per character)",
      "Accessories (stackable effects)"
    ]
    ARTWORK_INTEGRATION::CHARACTER_VISUAL_ASSETS

---

SUCCESS_CRITERIA:
  
  PRIMARY_SUCCESS:
    AUTHENTIC_BATTLES:
      MEASUREMENT::"Battle outcomes match original game calculations"
      VALIDATION::"Community expert verification"
      THRESHOLD::>95%_ACCURACY
      
    COMPLETE_CHARACTER_ROSTER:
      MEASUREMENT::"All stage girls with costume options implemented"
      VALIDATION::"Karth.top data coverage"
      THRESHOLD::100%_COVERAGE
      
    FUNCTIONAL_PVP_SYSTEM:
      MEASUREMENT::"Full pick-ban-battle cycle operational"
      VALIDATION::"End-to-end tournament simulation"
      THRESHOLD::COMPLETE_FUNCTIONALITY
      
  SECONDARY_SUCCESS:
    CODE_QUALITY:
      MEASUREMENT::"Maintainable, well-tested Ruby codebase"
      VALIDATION::"Code review and metrics"
      THRESHOLD::EXCELLENT_STANDARDS
      
    PERFORMANCE:
      MEASUREMENT::"Responsive battle calculations and API interactions"
      VALIDATION::"Benchmark testing"
      THRESHOLD::SUB_SECOND_RESPONSE
      
    DOCUMENTATION:
      MEASUREMENT::"Comprehensive, accurate project documentation"
      VALIDATION::"User and developer feedback"
      THRESHOLD::SELF_EXPLANATORY

---

STAKEHOLDER_ALIGNMENT:
  
  PRIMARY_STAKEHOLDER:
    IDENTITY::PROJECT_USER
    INTERESTS::[
      "Accurate game simulation",
      "Complete character roster",
      "Reliable battle calculations",
      "Tournament analysis capabilities"
    ]
    SATISFACTION_METRICS::[
      "Simulation accuracy",
      "Feature completeness",
      "System reliability",
      "Ease of use"
    ]
    
  SECONDARY_STAKEHOLDERS:
    REVUE_STARLIGHT_COMMUNITY:
      INTERESTS::[
        "Game mechanic understanding",
        "Character analysis tools",
        "Battle strategy insights",
        "Tournament organization"
      ]
      
    FUTURE_DEVELOPERS:
      INTERESTS::[
        "Clean, maintainable code",
        "Comprehensive documentation",
        "Extensible architecture",
        "Clear development patterns"
      ]
      
    AI_DEVELOPMENT_AGENTS:
      INTERESTS::[
        "Clear specifications",
        "Consistent interfaces",
        "Predictable behavior",
        "Semantic stability"
      ]

---

EVOLUTIONARY_BOUNDARIES:
  
  NEVER_COMPROMISE:
    GAME_AUTHENTICITY::"Original mechanics are inviolate"
    DATA_INTEGRITY::"Source data must be preserved accurately"
    CORE_ARCHITECTURE::"Ruby-based, modular design maintained"
    
  CAREFULLY_EVOLVE:
    PERFORMANCE_OPTIMIZATION::"Improve speed without changing behavior"
    FEATURE_ADDITION::"Extend capabilities without breaking existing"
    CODE_QUALITY::"Refactor for maintainability continuously"
    
  FREELY_ADAPT:
    DOCUMENTATION_STYLE::"Improve clarity and completeness"
    DEVELOPMENT_TOOLS::"Enhance productivity and quality"
    USER_INTERFACE::"Better presentation of core functionality"

---

PROJECT_MANTRAS:
  
  DEVELOPMENT_PRINCIPLES:
    AUTHENTICITY_FIRST::"When in doubt, research the original game"
    SIMPLICITY_THROUGH_STRUCTURE::"Complex mechanics need clear organization"
    EVOLUTION_THROUGH_STABILITY::"Strong foundations enable growth"
    COMMUNITY_THROUGH_QUALITY::"Excellence attracts engagement"
    
  DECISION_GUIDELINES:
    FIDELITY_OVER_CONVENIENCE::"Accurate is better than easy"
    CLARITY_OVER_CLEVERNESS::"Understandable is better than impressive"
    PROGRESS_OVER_PERFECTION::"Working is better than theoretical"
    COLLABORATION_OVER_ISOLATION::"Shared understanding enables success"

===END_PROJECT_ESSENCE===
