===AI_AGENT_COORDINATION===
// OCTAVE protocol for AI agent handoffs and coordination
// VERSION: 1.0
// PURPOSE: Ensure consistent understanding across AI coding sessions

META:
  PROJECT::"Revue Starlight ReLive TCG Reverse Engineering"
  COORDINATION_TYPE::MULTI_SESSION_AI_HANDOFF
  SEMANTIC_GUARDIAN::ATHENA  // Wisdom and strategic planning
  LAST_UPDATED::"2025-01-16"

0.DEF:
  HANDOFF::"Transfer of context and responsibility between AI agents"
  MUTATION_RISK::"Probability of semantic drift in understanding"
  CONTEXT_INTEGRITY::"Preservation of project understanding across sessions"
  SEMANTIC_ANCHOR::"Core concepts that must remain stable"

---

COORDINATION_PROTOCOL:
  
  SESSION_HANDOFF:
    REQUIRED_ELEMENTS::[
      "Current progress state",
      "Active work context", 
      "Pending decisions",
      "Known constraints",
      "Risk areas"
    ]
    HANDOFF_FORMAT::STRUCTURED_OCTAVE
    VALIDATION::SEMANTIC_IMMUNE_SYSTEM
    
  CONTEXT_PRESERVATION:
    CORE_ANCHORS::[
      "Game mechanics fidelity to original",
      "Ruby codebase architecture", 
      "Karth.top API integration",
      "PvP battle system authenticity"
    ]
    MUTATION_PREVENTION::HIGH_PRIORITY
    DRIFT_DETECTION::AUTOMATED

---

AGENT_ROLES:
  
  APOLLO_PRECISION:
    DOMAIN::"Code implementation and technical accuracy"
    RESPONSIBILITIES::[
      "Ruby syntax correctness",
      "Performance optimization",
      "Test implementation",
      "API integration precision"
    ]
    HANDOFF_TO::[ATHENA_STRATEGY, HERMES_INTEGRATION]
    
  ATHENA_STRATEGY:
    DOMAIN::"Architecture and game logic design"
    RESPONSIBILITIES::[
      "System architecture decisions",
      "Game mechanics interpretation",
      "Data structure design",
      "Strategic planning"
    ]
    HANDOFF_TO::[APOLLO_PRECISION, HEPHAESTUS_BUILDING]
    
  HERMES_INTEGRATION:
    DOMAIN::"Communication and data flow"
    RESPONSIBILITIES::[
      "API communication",
      "Data transformation",
      "System integration",
      "Documentation coordination"
    ]
    HANDOFF_TO::[APOLLO_PRECISION, DEMETER_GROWTH]
    
  HEPHAESTUS_BUILDING:
    DOMAIN::"Construction and implementation"
    RESPONSIBILITIES::[
      "Feature implementation",
      "System building",
      "Tool creation",
      "Infrastructure setup"
    ]
    HANDOFF_TO::[ARTEMIS_PROTECTION, APOLLO_PRECISION]
    
  ARTEMIS_PROTECTION:
    DOMAIN::"Quality assurance and boundaries"
    RESPONSIBILITIES::[
      "Testing and validation",
      "Error handling",
      "Security considerations",
      "Boundary enforcement"
    ]
    HANDOFF_TO::[ATHENA_STRATEGY, DEMETER_GROWTH]
    
  DEMETER_GROWTH:
    DOMAIN::"Project evolution and sustainability"
    RESPONSIBILITIES::[
      "Feature expansion",
      "Documentation growth",
      "Community building",
      "Long-term maintenance"
    ]
    HANDOFF_TO::[ATHENA_STRATEGY, HERMES_INTEGRATION]

---

HANDOFF_CHECKLIST:
  
  BEFORE_HANDOFF:
    DOCUMENT_STATE::[
      "Current implementation status",
      "Active file modifications",
      "Test results and coverage",
      "Known issues and blockers"
    ]
    CONTEXT_CAPTURE::[
      "Decision rationale",
      "Alternative approaches considered",
      "Future work planned",
      "Dependencies identified"
    ]
    
  DURING_HANDOFF:
    SEMANTIC_VALIDATION::[
      "Core concepts unchanged",
      "Architecture consistency maintained",
      "Game logic fidelity preserved",
      "API contracts respected"
    ]
    KNOWLEDGE_TRANSFER::[
      "Technical context shared",
      "Business logic explained",
      "Risk areas highlighted",
      "Success criteria clarified"
    ]
    
  AFTER_HANDOFF:
    CONTINUITY_CHECK::[
      "Understanding verified",
      "Context questions resolved",
      "Work plan confirmed",
      "Communication established"
    ]
    IMMUNE_SYSTEM_ACTIVATION::[
      "Mutation detection enabled",
      "Semantic anchors verified",
      "Drift monitoring active",
      "Recovery protocols ready"
    ]

---

COMMUNICATION_PATTERNS:
  
  PROGRESS_UPDATES:
    FORMAT::STRUCTURED_STATUS
    FREQUENCY::PER_MAJOR_MILESTONE
    CONTENT::[
      "Completed work summary",
      "Current challenges",
      "Next steps planned",
      "Help needed"
    ]
    
  DECISION_POINTS:
    FORMAT::DECISION_MATRIX
    STAKEHOLDERS::[USER, CURRENT_AGENT, FUTURE_AGENTS]
    DOCUMENTATION::MANDATORY
    RATIONALE::EXPLICIT
    
  RISK_ESCALATION:
    TRIGGERS::[
      "Semantic drift detected",
      "Architecture violation risk",
      "Game logic contradiction",
      "Performance degradation"
    ]
    RESPONSE::IMMEDIATE_CONSULTATION
    RESOLUTION::COLLABORATIVE

---

SUCCESS_METRICS:
  
  COORDINATION_EFFECTIVENESS:
    CONTEXT_PRESERVATION::>95%
    HANDOFF_SMOOTHNESS::SEAMLESS
    DECISION_CONSISTENCY::HIGH
    PROGRESS_CONTINUITY::UNBROKEN
    
  SEMANTIC_INTEGRITY:
    CORE_CONCEPT_STABILITY::ABSOLUTE
    ARCHITECTURE_CONSISTENCY::MAINTAINED
    GAME_LOGIC_FIDELITY::PRESERVED
    API_CONTRACT_COMPLIANCE::100%

===END_COORDINATION===
