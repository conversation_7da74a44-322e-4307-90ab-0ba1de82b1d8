# Authentic Battle Mechanics Implementation
*Development Notes - January 17, 2025*

## Overview

This document describes how we've implemented the authentic Revue Starlight: ReLive battle mechanics based on gameplay analysis and community research. Our goal was to create a faithful recreation of the original game's strategic depth and tactical complexity.

## Core Battle Flow

### Turn Structure
Each battle turn follows a specific sequence:

1. **ACT Card Generation**: Each team receives exactly 6 Action Points (AP) to distribute among available skills
2. **Start-of-Turn Effects**: Status effects, regeneration, and passive abilities activate
3. **Action Execution**: Skills execute in order of AP cost (lowest first), then by character speed
4. **End-of-Turn Effects**: Damage over time, status duration reduction, and cleanup
5. **Climax Revue Check**: Characters with 100+ brilliance can trigger special abilities

### Action Point Economy
The most crucial mechanic is the 6 AP per team system. Unlike many games where each character gets individual action points, Revue Starlight gives the entire team a shared pool of 6 AP each turn. This creates interesting resource management decisions:

- **1 AP Skills**: Basic attacks, low damage but efficient
- **2 AP Skills**: Special attacks with medium damage and +20 brilliance bonus
- **3 AP Skills**: Area-of-effect attacks hitting multiple enemies
- **0 AP Skills**: Climax acts (require 100 brilliance, consume all brilliance)

Teams typically get 5 skill cards randomly selected from their characters' available abilities, with costs ranging from 1-4 AP. The randomness adds tactical uncertainty - you might get all expensive skills or all cheap ones.

## Brilliance System (Kirameki)

Brilliance is the special meter that enables powerful climax acts. Here's how it works:

### Starting Amount
- **PvP battles**: Each character starts with 50 brilliance
- **PvE battles**: Usually start at 0 brilliance

### Gaining Brilliance
Characters accumulate brilliance through several methods:
- **Using skills**: 7 brilliance for 1 AP skills, 14 for 2 AP skills, 21 for 3 AP skills
- **Taking damage**: 10 brilliance per 100 damage received
- **Dealing damage**: 5 brilliance per 100 damage dealt
- **Special effects**: Some memoirs and skills provide bonus brilliance

### Climax Acts
When a character reaches 100 brilliance, they can perform a climax act:
- **Automatic availability**: Climax act cards automatically appear in your hand
- **High damage**: Typically 3x the character's attack power
- **All brilliance consumed**: Character drops to 0 brilliance after use
- **Special effects**: Often hit all enemies and apply status effects

## Positioning and Speed

### Automatic Arrangement
Unlike games with manual positioning, Revue Starlight automatically arranges characters based on their AGI (agility) stat:
- **Front row**: Slowest characters (natural tanks)
- **Back row**: Fastest characters (natural damage dealers)
- **No manual control**: Strategy comes from team composition, not positioning

### Turn Order Priority
Actions execute in a specific order:
1. **AP cost** (lower cost skills go first)
2. **Character AGI** (higher agility wins ties)
3. **Random tiebreaker** (if all factors are identical)

This means a 1 AP skill from a slow character will execute before a 3 AP skill from a fast character.

## Damage Calculations

### Base Damage Formula
```
Damage = (ATK × skill_power_percentage) - (defense × 0.3)
Minimum damage = 1 (always deal at least 1 damage)
```

### Attribute Effectiveness
The game uses two separate elemental cycles:

**Flower Cycle** (1.5x advantage, 0.67x disadvantage):
- Flower beats Wind
- Wind beats Snow  
- Snow beats Flower

**Moon Cycle** (1.5x advantage, 0.67x disadvantage):
- Moon beats Space
- Space beats Cloud
- Cloud beats Moon

**Neutral Elements**:
- Star and Sun have no advantages or disadvantages against any element
- Same element vs same element = neutral (1.0x damage)

### Critical Hits
- **Base rate**: 5% chance for all characters
- **DEX influence**: Each point of DEX adds 1% critical chance
- **Damage multiplier**: Critical hits deal 1.5x damage
- **Calculation**: (5% + DEX stat) = total critical chance

## Status Effects and Interactions

### Damage Over Time
- **Burn**: Deals damage each turn, reduces ACT by 10% (non-stacking)
- **Poison**: Fixed damage per turn, stackable applications

### Control Effects
- **Freeze**: Prevents skill usage, broken by taking damage
- **Stun/Sleep**: Prevents actions for a set duration
- **Various breaking conditions**: Different effects have different removal methods

### Defensive Effects
- **Dodge/Evasion**: Prevents damage from one hit, consumed per hit (not per attack)
- **Barriers**: Absorb specific damage amounts before breaking
- **Guts/Fortitude**: Survive lethal damage with 1 HP, no brilliance gain from guts damage

## Equipment Integration

### Memoirs
- **Limit**: One memoir per character
- **Stat bonuses**: Add directly to character's base stats
- **Special effects**: Brilliance bonuses, status immunities, skill modifications

### Accessories  
- **Stacking**: Multiple accessories can stack their effects
- **Categories**: Stat boosts, active skills, passive effects
- **Compatibility**: Some accessories only work with specific cards

## Strategic Depth

### Team Composition
The pick-ban system creates strategic depth:
1. **Roster building**: Select 10 stage girls
2. **Ban phase**: Each team bans 2 opposing characters
3. **Final team**: Choose 5 characters from remaining 8
4. **No restrictions**: Any school, element, or rarity combinations allowed

### Meta Considerations
Current high-level play favors:
- **Back row choppers**: Fast characters with high single-target damage
- **Aggressive strategies**: Offense over defense due to damage scaling
- **Stage effects**: Unit skills that affect teammates or enemies
- **Speed focus**: AGI is crucial for turn order and positioning

### Resource Management
Success requires managing multiple resources:
- **AP allocation**: How to spend your 6 AP each turn
- **Brilliance timing**: When to use climax acts for maximum impact
- **Status effect timing**: When to apply debuffs or remove buffs

## Implementation Accuracy

Our implementation faithfully recreates these mechanics based on:
- **Gameplay video analysis**: Observed actual battle behavior
- **Community guides**: Player-researched formulas and interactions
- **User expertise**: Detailed explanations from experienced players
- **Testing validation**: Confirmed mechanics work as expected

The result is a battle system that captures the strategic depth and tactical complexity of the original Revue Starlight: ReLive, providing an authentic competitive experience for players.

## Technical Notes

The battle system is implemented in `lib/card_battle/authentic_battle_system.rb` with full integration to our SQLite database system. It maintains backward compatibility with existing code while providing the authentic mechanics described above.

Key features include:
- Complete turn-based battle engine
- Accurate damage calculations with all modifiers
- Proper status effect handling and interactions
- Climax revue mechanics with brilliance tracking
- Equipment integration with memoirs and accessories
- Comprehensive logging for debugging and analysis
