===GAMEPLAY_VIDEO_ANALYSIS_FRAMEWORK===
// Framework for analyzing authentic Revue Starlight: ReLive battle mechanics
// VERSION: 1.0
// GUARDIAN: APOLLO (Precision in observation and analysis)
// PURPOSE: Extract authentic game mechanics from gameplay videos

META:
  ANALYSIS_TARGET::AUTHENTIC_BATTLE_MECHANICS
  VIDEO_COUNT::7_GAMEPLAY_VIDEOS
  FOCUS_AREAS::[TURN_ORDER, SK<PERSON>L_USAGE, POSITIONING, DAMAGE_CALCULATIONS]
  VALIDATION_GOAL::ACCURATE_IMPLEMENTATION

0.DEF:
  BATTLE_FLOW::"Sequence of actions and decisions in a match"
  TURN_MECHANICS::"How initiative, actions, and timing work"
  SKILL_SYSTEM::"Auto skills, ACT skills, climax acts, and finish acts"
  POSITIONING_EFFECTS::"Front row vs back row mechanics"
  DAMAGE_CALCULATION::"How stats, attributes, and effects determine damage"
  BRILLIANCE_SYSTEM::"How the special meter accumulates and is used"

---

VIDEO_ANALYSIS_TARGETS:

  VIDEO_1_SIMPING_KURO_PVP:
    URL::"https://www.youtube.com/watch?v=bRzC1I6qsHo"
    FOCUS_OBSERVATIONS::[
      "Turn order determination and AGI effects",
      "Skill usage patterns and ACT point economy",
      "Damage calculations and attribute effectiveness",
      "Brilliance accumulation and climax act timing",
      "Positioning strategy (front/back row)",
      "Status effects and their durations"
    ]
    
  VIDEO_2_BACKROW_BESTROW:
    URL::"https://www.youtube.com/watch?v=ao97Or2EDAI"
    TITLE_IMPLICATIONS::"Back row positioning strategy focus"
    FOCUS_OBSERVATIONS::[
      "Back row protection mechanics",
      "Targeting restrictions and priorities",
      "Positioning-based damage modifiers",
      "Support character effectiveness",
      "Range-based skill interactions"
    ]
    
  VIDEO_3_ADDITIONAL_PVP:
    URL::"https://www.youtube.com/watch?v=0tS9jEPjOu8"
    FOCUS_OBSERVATIONS::[
      "Team composition strategies",
      "Skill synergies between characters",
      "Equipment effects in action",
      "Battle duration and pacing"
    ]
    
  VIDEO_4_BATTLE_MECHANICS:
    URL::"https://www.youtube.com/watch?v=ZEbBbe-ToOw"
    FOCUS_OBSERVATIONS::[
      "Detailed damage number analysis",
      "Critical hit mechanics",
      "Healing and regeneration effects",
      "Debuff application and removal"
    ]
    
  VIDEO_5_ADVANCED_STRATEGY:
    URL::"https://www.youtube.com/watch?v=dzbm5FoQ_uo"
    FOCUS_OBSERVATIONS::[
      "Advanced team synergies",
      "Timing of special abilities",
      "Counter-strategies and adaptations",
      "Win condition execution"
    ]
    
  VIDEO_6_TACTICAL_GAMEPLAY:
    URL::"https://www.youtube.com/watch?v=DBnpQ0G2YgQ"
    FOCUS_OBSERVATIONS::[
      "Resource management decisions",
      "Skill priority and sequencing",
      "Defensive vs offensive positioning",
      "Match tempo control"
    ]
    
  VIDEO_7_COMPETITIVE_MATCH:
    URL::"https://www.youtube.com/watch?v=95dWe2S-b-k"
    FOCUS_OBSERVATIONS::[
      "High-level decision making",
      "Optimal skill rotations",
      "Endgame scenarios",
      "Victory condition achievement"
    ]

---

CRITICAL_MECHANICS_TO_OBSERVE:

  TURN_SYSTEM_ANALYSIS:
    AGI_BASED_INITIATIVE:
      QUESTIONS::[
        "How exactly does AGI determine turn order?",
        "Are there tie-breaking mechanisms?",
        "Do speed buffs affect turn order mid-battle?",
        "How many actions can each character take per turn?"
      ]
      VALIDATION_POINTS::[
        "Fastest character always goes first",
        "Turn order remains consistent unless modified",
        "ACT point allocation per turn",
        "Action economy balance"
      ]
      
    ACT_POINT_ECONOMY:
      QUESTIONS::[
        "How many ACT points does each character get per turn?",
        "Do different skills cost different ACT points?",
        "Can ACT points be saved or carried over?",
        "Are there ways to gain extra ACT points?"
      ]
      VALIDATION_POINTS::[
        "6 ACT points per turn confirmed",
        "Skill costs vary by power level",
        "No ACT point carryover",
        "Some effects grant bonus ACT points"
      ]

  DAMAGE_CALCULATION_ANALYSIS:
    ATTRIBUTE_EFFECTIVENESS:
      QUESTIONS::[
        "What are the exact damage multipliers?",
        "How do neutral matchups work?",
        "Are there any exceptions to the cycle?",
        "Do some skills ignore attribute effectiveness?"
      ]
      VALIDATION_POINTS::[
        "1.5x for effective matchups",
        "0.67x for ineffective matchups", 
        "1.0x for neutral matchups",
        "Some skills have fixed damage"
      ]
      
    CRITICAL_HIT_MECHANICS:
      QUESTIONS::[
        "What is the base critical hit rate?",
        "How does DEX affect critical chance?",
        "What is the critical damage multiplier?",
        "Can critical hits be prevented?"
      ]
      VALIDATION_POINTS::[
        "Base 5% critical rate",
        "DEX adds percentage points",
        "1.5x critical multiplier",
        "Some effects prevent crits"
      ]

  BRILLIANCE_SYSTEM_ANALYSIS:
    ACCUMULATION_MECHANICS:
      QUESTIONS::[
        "How much brilliance is gained from taking damage?",
        "How much brilliance is gained from dealing damage?",
        "Do skills grant different brilliance amounts?",
        "Are there other sources of brilliance?"
      ]
      VALIDATION_POINTS::[
        "10 brilliance per 100 damage taken",
        "5 brilliance per 100 damage dealt",
        "Skills have specific brilliance costs",
        "Some effects modify brilliance gain"
      ]
      
    CLIMAX_ACT_USAGE:
      QUESTIONS::[
        "What is the exact brilliance cost for climax acts?",
        "Can multiple climax acts be used per turn?",
        "How do climax acts affect the battle flow?",
        "What are the strategic timing considerations?"
      ]
      VALIDATION_POINTS::[
        "50 brilliance minimum for climax",
        "One climax act per turn limit",
        "Climax acts are game-changing",
        "Timing is crucial for victory"
      ]

  POSITIONING_SYSTEM_ANALYSIS:
    FRONT_ROW_MECHANICS:
      QUESTIONS::[
        "How many characters can be in front row?",
        "Do front row characters take more damage?",
        "Are there targeting restrictions?",
        "What are the strategic implications?"
      ]
      VALIDATION_POINTS::[
        "3 characters maximum in front",
        "Front row takes increased damage",
        "Front row targeted first",
        "Tanks belong in front row"
      ]
      
    BACK_ROW_MECHANICS:
      QUESTIONS::[
        "How many characters can be in back row?",
        "Are back row characters protected?",
        "Can back row characters be targeted directly?",
        "What skills can reach back row?"
      ]
      VALIDATION_POINTS::[
        "2 characters maximum in back",
        "Back row has damage reduction",
        "Limited targeting of back row",
        "Some skills ignore positioning"
      ]

---

IMPLEMENTATION_VALIDATION_CHECKLIST:

  CURRENT_SYSTEM_ACCURACY:
    BATTLE_MECHANICS_SPECIFICATION::COMPARE_WITH_VIDEO_EVIDENCE
    ATTRIBUTE_RELATIONSHIP_MATRIX::VALIDATE_DAMAGE_MULTIPLIERS
    PICK_BAN_SYSTEM_RULES::CONFIRM_TEAM_COMPOSITION_RULES
    EQUIPMENT_INTERACTION_LOGIC::VERIFY_STAT_BONUS_CALCULATIONS
    
  GAPS_TO_IDENTIFY:
    MISSING_MECHANICS::[
      "Mechanics not yet implemented",
      "Incorrect assumptions in current code",
      "Oversimplified calculations",
      "Missing status effects or interactions"
    ]
    IMPLEMENTATION_PRIORITIES::[
      "Critical mechanics for authentic gameplay",
      "Nice-to-have features for completeness",
      "Edge cases and special interactions",
      "Performance optimizations"
    ]

---

ANALYSIS_METHODOLOGY:

  OBSERVATION_TECHNIQUES:
    FRAME_BY_FRAME_ANALYSIS::PRECISE_TIMING_AND_SEQUENCE_OBSERVATION
    DAMAGE_NUMBER_TRACKING::EXACT_CALCULATION_VERIFICATION
    TURN_ORDER_MAPPING::AGI_STAT_CORRELATION_ANALYSIS
    SKILL_EFFECT_DOCUMENTATION::COMPREHENSIVE_ABILITY_CATALOGING
    
  DATA_COLLECTION_POINTS:
    QUANTITATIVE_METRICS::[
      "Exact damage numbers",
      "Turn order sequences", 
      "Brilliance gain/loss amounts",
      "Skill costs and effects",
      "Status effect durations"
    ]
    QUALITATIVE_OBSERVATIONS::[
      "Strategic decision patterns",
      "Positioning preferences",
      "Skill usage priorities",
      "Win condition approaches"
    ]

---

QUESTIONS_FOR_USER:

  SPECIFIC_MECHANICS_CLARIFICATION:
    HIGH_PRIORITY::[
      "Are there any mechanics in the videos that seem different from our current implementation?",
      "Do you notice any specific damage calculation patterns?",
      "How do the brilliance and climax act systems work in practice?",
      "What positioning strategies are most effective?"
    ]
    
  IMPLEMENTATION_FOCUS:
    CRITICAL_FEATURES::[
      "Which mechanics are most important for authentic gameplay?",
      "Are there any characters or skills that showcase unique mechanics?",
      "What aspects of the battle system create the most strategic depth?",
      "Are there any common misconceptions about the game mechanics?"
    ]
    
  VALIDATION_PRIORITIES:
    ACCURACY_VERIFICATION::[
      "Do our current damage calculations match what you see in videos?",
      "Is our turn order system consistent with video evidence?",
      "Are there any missing status effects or interactions?",
      "How accurate is our brilliance accumulation system?"
    ]

===END_ANALYSIS_FRAMEWORK===
