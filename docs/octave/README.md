# OCTAVE Documentation System

This directory contains the OCTAVE (Olympian Common Text And Vocabulary Engine) documentation for the Revue Starlight: ReLive TCG project. OCTAVE is an AI-native documentation format designed for precise communication between AI agents and complex system specification.

## Documentation Structure

### 🏛️ Project Foundation (`.project-context/`)
Core project identity and coordination documents:

- **[ai-agent-coordination.oct](.project-context/ai-agent-coordination.oct)** - AI agent handoff protocols and role definitions
- **[danger-zones.oct](.project-context/danger-zones.oct)** - Critical areas requiring extreme caution
- **[evolutionary-system.oct](.project-context/evolutionary-system.oct)** - Controlled project evolution framework
- **[journey-log.oct](.project-context/journey-log.oct)** - Project development chronicle and milestones
- **[project-essence.oct](.project-context/project-essence.oct)** - Core identity and immutable principles
- **[semantic-immune-system.oct](.project-context/semantic-immune-system.oct)** - Protection against concept drift

### 🎮 Game Mechanics Specifications
Definitive specifications of Revue Starlight game systems:

- **[battle-mechanics-specification.oct.md](battle-mechanics-specification.oct.md)** - Complete 5v5 battle system specification
- **[attribute-relationship-matrix.oct.md](attribute-relationship-matrix.oct.md)** - Attribute effectiveness calculations and strategic implications
- **[pick-ban-system-rules.oct.md](pick-ban-system-rules.oct.md)** - PvP roster selection and team composition process
- **[equipment-memoir-interaction-logic.oct.md](equipment-memoir-interaction-logic.oct.md)** - Equipment system and stat interaction mechanics

### 🤖 AI Agent Communication
Templates and protocols for AI development coordination:

- **[progress-handoff-template.oct.md](progress-handoff-template.oct.md)** - Standardized context transfer between AI sessions
- **[feature-specification-template.oct.md](feature-specification-template.oct.md)** - Template for complex feature documentation
- **[system-state-documentation.oct.md](system-state-documentation.oct.md)** - Current project state for debugging and context

### 📚 Documentation System
Meta-documentation about the documentation approach:

- **[hybrid-documentation-system.oct.md](hybrid-documentation-system.oct.md)** - Multi-format documentation strategy and guidelines

## OCTAVE Format Overview

OCTAVE uses structured syntax designed for AI comprehension:

```octave
SECTION_NAME:
  KEY::VALUE                    // Assignment
  NESTED_STRUCTURE:
    CHILD_KEY::CHILD_VALUE
  RELATIONSHIPS::A+B            // Synthesis
  CONFLICTS::SPEED _VERSUS_ ACCURACY  // Tension
  SEQUENCES::[START->PROCESS->END]     // Progression
```

### Key Operators
- `::` - Assignment (KEY::VALUE)
- `+` - Synthesis (combines elements)
- `_VERSUS_` - Tension (represents trade-offs)
- `->` - Progression (shows sequence/flow)
- `//` - Comments

## Usage Guidelines

### When to Use OCTAVE
✅ **Use OCTAVE for:**
- Complex game mechanics with relationships
- AI agent handoff documentation
- System architecture specifications
- Strategic planning documents
- Risk assessment and danger zones

❌ **Don't use OCTAVE for:**
- Simple user instructions
- Basic code comments
- Installation guides
- Casual documentation

### When to Use Other Formats
- **Markdown**: User guides, README files, tutorials
- **Ruby YARD**: API documentation, method specifications
- **Code Comments**: Implementation details, algorithm explanations

## Semantic Protection

The documentation system includes semantic immune system protection to prevent concept drift:

- **Mutation Detection**: Automated identification of understanding changes
- **Concept Anchoring**: Core principles that must remain stable
- **Validation Protocols**: Verification of documentation accuracy
- **Recovery Mechanisms**: Restoration of correct understanding

## Contributing to OCTAVE Documentation

1. **Understand the Audience**: AI agents vs. human users vs. developers
2. **Choose Appropriate Format**: Use the format decision matrix
3. **Follow OCTAVE Syntax**: Maintain semantic precision
4. **Validate Relationships**: Ensure all connections are explicit
5. **Test Understanding**: Verify AI agents can parse correctly

## Cross-References

### Related Traditional Documentation
- [Main README](../../README.md) - Project overview
- [Enhanced README](../../ENHANCED_README.md) - Detailed project description
- [Game Mechanics Analysis](../../GAME_MECHANICS_ANALYSIS.md) - Traditional analysis

### Implementation References
- [Ruby Codebase](../../lib/) - Implementation of documented systems
- [Test Suite](../../spec/) - Validation of documented behaviors
- [Example Scripts](../../bin/) - Demonstrations of documented features

## Maintenance

OCTAVE documentation is maintained according to the hybrid documentation system:

- **Frequency**: Milestone-based updates
- **Triggers**: Major feature completion, architecture changes
- **Validation**: Semantic immune system checks
- **Quality**: Semantic precision and relationship clarity

## Support

For questions about OCTAVE documentation:
1. Consult the [hybrid documentation system](hybrid-documentation-system.oct.md)
2. Review the [semantic immune system](.project-context/semantic-immune-system.oct) for protection protocols
3. Check the [journey log](.project-context/journey-log.oct) for historical context
4. Refer to the [project essence](.project-context/project-essence.oct) for core principles

---

*This documentation system represents a synthesis of human creativity and machine precision, designed to enable effective collaboration between AI agents and human developers in complex software projects.*
