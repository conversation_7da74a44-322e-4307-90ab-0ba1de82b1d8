===PROGRESS_HANDOFF_TEMPLATE===
// Template for AI agent session handoffs and context preservation
// VERSION: 1.0
// GUARDIAN: HERMES (Communication and knowledge transfer)
// PURPOSE: Ensure seamless continuation between AI coding sessions

META:
  TEMPLATE_TYPE::AI_AGENT_HANDOFF
  USAGE_FREQUENCY::PER_SESSION_TRANSITION
  SEMANTIC_PROTECTION::ACTIVE
  LAST_UPDATED::"2025-01-16"

0.DEF:
  HANDOFF::"Transfer of project context and responsibility between AI agents"
  CONTEXT_INTEGRITY::"Preservation of understanding across sessions"
  WORK_CONTINUITY::"Seamless progression without knowledge loss"
  SEMANTIC_ANCHOR::"Core concept that must remain stable"
  MUTATION_RISK::"Probability of understanding drift during transfer"

---

HANDOFF_HEADER:
  
  SESSION_METADATA:
    HANDOFF_DATE::""  // Fill with current date
    PREVIOUS_AGENT_ROLE::""  // e.g., APOLLO_PRECISION, ATHENA_STRATEGY
    RECEIVING_AGENT_ROLE::""  // Expected next agent archetype
    SESSION_DURATION::""  // Time spent on current work
    HANDOFF_REASON::""  // Why handoff is occurring
    
  PROJECT_STATE_SNAPSHOT:
    CURRENT_PHASE::""  // e.g., PHASE_1_CORE_DOCUMENTATION
    ACTIVE_WORK_AREA::""  // Current focus area
    COMPLETION_PERCENTAGE::""  // Estimated progress
    NEXT_MILESTONE::""  // Immediate goal
    CRITICAL_DEPENDENCIES::[]  // Blocking factors

---

WORK_CONTEXT:
  
  COMPLETED_WORK:
    MAJOR_ACCOMPLISHMENTS::[]
    // List significant achievements in current session
    // Example: "Implemented battle mechanics specification"
    
    FILES_MODIFIED::[]
    // List all files created or changed
    // Include brief description of changes
    
    TESTS_ADDED_OR_UPDATED::[]
    // Document test coverage changes
    
    DOCUMENTATION_CREATED::[]
    // New documentation or updates
    
  ACTIVE_WORK:
    CURRENT_TASK::""
    // Detailed description of what you're working on
    
    IMPLEMENTATION_APPROACH::""
    // Strategy being used
    
    PROGRESS_STATUS::""
    // How far along the current task
    
    IMMEDIATE_NEXT_STEPS::[]
    // Specific actions to continue work
    
  PENDING_DECISIONS:
    ARCHITECTURAL_CHOICES::[]
    // Design decisions that need to be made
    
    IMPLEMENTATION_OPTIONS::[]
    // Technical approaches under consideration
    
    USER_CONSULTATION_NEEDED::[]
    // Questions requiring user input
    
    RESEARCH_REQUIRED::[]
    // Areas needing investigation

---

TECHNICAL_CONTEXT:
  
  CODEBASE_STATE:
    CURRENT_BRANCH::""  // If using version control
    BUILD_STATUS::""  // Working/broken/needs attention
    TEST_COVERAGE::""  // Current test status
    KNOWN_ISSUES::[]  // Bugs or problems identified
    
  DEPENDENCIES:
    EXTERNAL_APIS::[]
    // Karth.top or other API status
    
    GEM_DEPENDENCIES::[]
    // Ruby gem requirements or updates
    
    DATA_DEPENDENCIES::[]
    // Required data files or sources
    
    SYSTEM_REQUIREMENTS::[]
    // Environment or setup needs
    
  PERFORMANCE_CONSIDERATIONS:
    OPTIMIZATION_OPPORTUNITIES::[]
    // Areas for improvement identified
    
    BOTTLENECKS_DISCOVERED::[]
    // Performance issues found
    
    MEMORY_USAGE_NOTES::[]
    // Resource consumption observations

---

SEMANTIC_ANCHORS:
  
  CORE_CONCEPTS_VERIFIED:
    GAME_MECHANICS_FIDELITY::""
    // Confirm understanding of authentic mechanics
    
    API_INTEGRATION_APPROACH::""
    // Validate karth.top integration strategy
    
    ARCHITECTURE_CONSISTENCY::""
    // Verify adherence to Ruby patterns
    
    DOCUMENTATION_STANDARDS::""
    // Confirm OCTAVE usage and quality
    
  PROTECTED_DECISIONS:
    IMMUTABLE_CHOICES::[]
    // Decisions that cannot be changed
    
    VALIDATED_APPROACHES::[]
    // Methods confirmed to work
    
    REJECTED_ALTERNATIVES::[]
    // Approaches tried and abandoned
    
    USER_REQUIREMENTS::[]
    // Explicit user specifications

---

RISK_ASSESSMENT:
  
  MUTATION_RISKS:
    HIGH_RISK_AREAS::[]
    // Code or concepts prone to drift
    
    SEMANTIC_VULNERABILITIES::[]
    // Understanding that could degrade
    
    COMPLEXITY_HOTSPOTS::[]
    // Areas where confusion is likely
    
    INTEGRATION_FRAGILITIES::[]
    // Connections that could break
    
  MITIGATION_STRATEGIES:
    VALIDATION_CHECKPOINTS::[]
    // How to verify understanding
    
    REFERENCE_MATERIALS::[]
    // Documents to consult for clarity
    
    TESTING_APPROACHES::[]
    // How to validate implementations
    
    ROLLBACK_PLANS::[]
    // Recovery strategies if problems arise

---

COMMUNICATION_NOTES:
  
  USER_INTERACTIONS:
    RECENT_FEEDBACK::[]
    // User comments or corrections
    
    PENDING_QUESTIONS::[]
    // Questions asked but not answered
    
    CLARIFICATIONS_NEEDED::[]
    // Areas requiring user input
    
    APPROVAL_REQUIRED::[]
    // Decisions needing user confirmation
    
  COLLABORATION_CONTEXT:
    WORKING_STYLE_NOTES::""
    // User preferences observed
    
    COMMUNICATION_PATTERNS::""
    // How user likes to interact
    
    FEEDBACK_INCORPORATION::""
    // How user input was handled
    
    EXPECTATION_ALIGNMENT::""
    // Confirmation of shared understanding

---

HANDOFF_VALIDATION:
  
  CONTEXT_TRANSFER_CHECKLIST:
    TECHNICAL_UNDERSTANDING::[]
    // Verify technical context is clear
    
    BUSINESS_LOGIC_CLARITY::[]
    // Confirm game mechanics understanding
    
    IMPLEMENTATION_STRATEGY::[]
    // Validate approach is documented
    
    RISK_AWARENESS::[]
    // Ensure risks are communicated
    
  CONTINUITY_VERIFICATION:
    WORK_RESUMPTION_PLAN::""
    // How next agent should continue
    
    SUCCESS_CRITERIA::""
    // How to measure progress
    
    COMPLETION_DEFINITION::""
    // What constitutes finished work
    
    QUALITY_STANDARDS::""
    // Expected level of implementation

---

RECEIVING_AGENT_GUIDANCE:
  
  IMMEDIATE_PRIORITIES::[]
  // What to focus on first
  
  CONTEXT_REVIEW_CHECKLIST::[]
  // Documents to read for understanding
  
  VALIDATION_STEPS::[]
  // How to verify inherited understanding
  
  ESCALATION_TRIGGERS::[]
  // When to seek help or clarification
  
  SUCCESS_INDICATORS::[]
  // Signs that handoff was successful

---

TEMPLATE_USAGE_NOTES:
  
  FILLING_INSTRUCTIONS:
    COMPLETENESS::"Fill all relevant sections thoroughly"
    SPECIFICITY::"Provide concrete details, not generalities"
    CLARITY::"Write for an agent with no prior context"
    VALIDATION::"Double-check all technical details"
    
  QUALITY_STANDARDS:
    SEMANTIC_PRECISION::"Use exact terminology"
    TECHNICAL_ACCURACY::"Verify all code and system details"
    STRATEGIC_CLARITY::"Explain reasoning behind decisions"
    RISK_TRANSPARENCY::"Honestly assess potential problems"
    
  HANDOFF_SUCCESS_CRITERIA:
    UNDERSTANDING_TRANSFER::"Receiving agent can continue work immediately"
    CONTEXT_PRESERVATION::"No loss of project understanding"
    RISK_MITIGATION::"Potential problems are anticipated"
    QUALITY_MAINTENANCE::"Standards and approaches are maintained"

===END_HANDOFF_TEMPLATE===
