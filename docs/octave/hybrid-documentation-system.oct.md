===HYBRID_DOCUMENTATION_SYSTEM===
// Complete specification of the project's multi-format documentation approach
// VERSION: 1.0
// GUARDIAN: DEMETER (Sustainable growth and organization)
// PURPOSE: Define when and how to use different documentation formats

META:
  SYSTEM_TYPE::HYBRID_DOCUMENTATION_ECOSYSTEM
  FORMATS_INTEGRATED::[OCTAVE, MARKDOWN, RUBY_YARD, CODE_COMMENTS]
  STRATEGIC_APPROACH::RIGHT_TOOL_FOR_RIGHT_PURPOSE
  MAINTENANCE_PHILOSOPHY::SUSTAINABLE_AND_SCALABLE

0.DEF:
  HYBRID_SYSTEM::"Multi-format documentation approach using optimal format per use case"
  DOCUMENTATION_FIDELITY::"Accuracy and completeness of information"
  AUDIENCE_OPTIMIZATION::"Tailoring format to intended readers"
  MAINTENANCE_BURDEN::"Effort required to keep documentation current"
  SEMANTIC_CONSISTENCY::"Unified understanding across all formats"

---

DOCUMENTATION_FORMAT_MATRIX:
  
  OCTAVE_USAGE:
    OPTIMAL_FOR::[
      "Complex game mechanics specifications",
      "AI agent handoff documentation", 
      "System architecture decisions",
      "Strategic planning documents",
      "Risk assessment and danger zones",
      "Feature specifications with high complexity"
    ]
    AUDIENCE::AI_AGENTS+TECHNICAL_STAKEHOLDERS
    MAINTENANCE::HIGH_VALUE_LOW_FREQUENCY
    SEMANTIC_PROTECTION::MAXIMUM
    
  MARKDOWN_USAGE:
    OPTIMAL_FOR::[
      "User-facing README files",
      "Installation and setup guides",
      "Tutorial and getting started docs",
      "Project overview and vision",
      "Community contribution guidelines",
      "Release notes and changelogs"
    ]
    AUDIENCE::HUMAN_USERS+DEVELOPERS+COMMUNITY
    MAINTENANCE::MODERATE_FREQUENCY
    SEMANTIC_PROTECTION::STANDARD
    
  RUBY_YARD_USAGE:
    OPTIMAL_FOR::[
      "Class and method documentation",
      "API reference generation",
      "Parameter and return value specs",
      "Usage examples for code",
      "Type annotations and contracts",
      "Automated documentation generation"
    ]
    AUDIENCE::DEVELOPERS+API_CONSUMERS
    MAINTENANCE::CONTINUOUS_WITH_CODE_CHANGES
    SEMANTIC_PROTECTION::CODE_SYNCHRONIZED
    
  INLINE_COMMENTS_USAGE:
    OPTIMAL_FOR::[
      "Complex algorithm explanations",
      "Business logic clarifications",
      "Temporary implementation notes",
      "TODO and FIXME annotations",
      "Performance optimization notes",
      "Bug fix explanations"
    ]
    AUDIENCE::CODE_MAINTAINERS
    MAINTENANCE::IMMEDIATE_WITH_CODE
    SEMANTIC_PROTECTION::MINIMAL

---

CONTENT_ALLOCATION_STRATEGY:
  
  GAME_MECHANICS_DOCUMENTATION:
    PRIMARY_FORMAT::OCTAVE
    RATIONALE::"Complex relationships require structured representation"
    EXAMPLES::[
      "Battle system specifications",
      "Attribute effectiveness matrices",
      "Equipment interaction logic",
      "Pick-ban system rules"
    ]
    SUPPLEMENTARY_FORMATS::[
      "Markdown summaries for users",
      "YARD docs for implementation classes",
      "Code comments for calculation details"
    ]
    
  API_INTEGRATION_DOCUMENTATION:
    PRIMARY_FORMAT::OCTAVE_FOR_COMPLEX_RUBY_YARD_FOR_SIMPLE
    DECISION_CRITERIA::[
      "Simple CRUD operations: YARD",
      "Complex data transformations: OCTAVE",
      "Error handling strategies: OCTAVE", 
      "Usage examples: YARD"
    ]
    INTEGRATION_APPROACH::CROSS_REFERENCE_BETWEEN_FORMATS
    
  USER_GUIDANCE_DOCUMENTATION:
    PRIMARY_FORMAT::MARKDOWN
    RATIONALE::"Human-readable format for human-facing content"
    STRUCTURE::[
      "README.md: Project overview",
      "INSTALLATION.md: Setup instructions",
      "USAGE.md: Basic usage examples",
      "CONTRIBUTING.md: Development guidelines"
    ]
    OCTAVE_INTEGRATION::LINK_TO_TECHNICAL_SPECS_WHEN_NEEDED

---

DOCUMENTATION_WORKFLOW:
  
  CREATION_PROCESS:
    STEP_1_AUDIENCE_IDENTIFICATION:
      QUESTION::"Who will read this documentation?"
      OPTIONS::[
        "AI agents: Consider OCTAVE",
        "Human users: Consider Markdown",
        "Developers: Consider YARD",
        "Code maintainers: Consider comments"
      ]
      
    STEP_2_COMPLEXITY_ASSESSMENT:
      QUESTION::"How complex is the information?"
      GUIDELINES::[
        "High complexity with relationships: OCTAVE",
        "Moderate complexity: Markdown or YARD",
        "Simple explanations: Comments or YARD",
        "Procedural instructions: Markdown"
      ]
      
    STEP_3_MAINTENANCE_CONSIDERATION:
      QUESTION::"How often will this change?"
      STRATEGY::[
        "Rarely changes: OCTAVE acceptable",
        "Changes with features: Markdown",
        "Changes with code: YARD or comments",
        "Frequently updated: Choose lightweight format"
      ]
      
    STEP_4_FORMAT_SELECTION:
      DECISION_MATRIX::USE_ABOVE_CRITERIA
      VALIDATION::CONFIRM_CHOICE_WITH_TEAM
      IMPLEMENTATION::CREATE_USING_SELECTED_FORMAT
      
  MAINTENANCE_PROCESS:
    OCTAVE_MAINTENANCE:
      FREQUENCY::MILESTONE_BASED
      TRIGGERS::[
        "Major feature completion",
        "Architecture changes",
        "Game mechanics updates",
        "AI agent feedback"
      ]
      VALIDATION::SEMANTIC_IMMUNE_SYSTEM_CHECKS
      
    MARKDOWN_MAINTENANCE:
      FREQUENCY::RELEASE_BASED
      TRIGGERS::[
        "New feature releases",
        "Installation changes",
        "User workflow updates",
        "Community feedback"
      ]
      VALIDATION::USER_TESTING_AND_FEEDBACK
      
    YARD_MAINTENANCE:
      FREQUENCY::CONTINUOUS
      TRIGGERS::[
        "Code changes",
        "API modifications",
        "Method signature updates",
        "Class restructuring"
      ]
      VALIDATION::AUTOMATED_GENERATION_CHECKS
      
    COMMENT_MAINTENANCE:
      FREQUENCY::IMMEDIATE
      TRIGGERS::[
        "Code modifications",
        "Bug fixes",
        "Algorithm changes",
        "Performance optimizations"
      ]
      VALIDATION::CODE_REVIEW_PROCESS

---

CROSS_FORMAT_INTEGRATION:
  
  LINKING_STRATEGY:
    OCTAVE_TO_MARKDOWN:
      METHOD::REFERENCE_LINKS_IN_MARKDOWN_TO_OCTAVE_SPECS
      EXAMPLE::"See [Battle Mechanics](docs/octave/battle-mechanics.oct.md)"
      PURPOSE::PROVIDE_DETAILED_SPECS_FOR_INTERESTED_USERS
      
    OCTAVE_TO_YARD:
      METHOD::YARD_COMMENTS_REFERENCE_OCTAVE_SPECIFICATIONS
      EXAMPLE::"# See docs/octave/equipment-logic.oct.md for complete specification"
      PURPOSE::CONNECT_IMPLEMENTATION_TO_SPECIFICATION
      
    MARKDOWN_TO_YARD:
      METHOD::MARKDOWN_GUIDES_LINK_TO_GENERATED_API_DOCS
      EXAMPLE::"[API Reference](docs/yard/index.html)"
      PURPOSE::PROVIDE_DETAILED_IMPLEMENTATION_REFERENCE
      
    COMMENTS_TO_OCTAVE:
      METHOD::COMPLEX_ALGORITHMS_REFERENCE_OCTAVE_SPECS
      EXAMPLE::"# Algorithm based on battle-mechanics.oct.md section DAMAGE_CALCULATION"
      PURPOSE::CONNECT_CODE_TO_AUTHORITATIVE_SPECIFICATION
      
  CONSISTENCY_MAINTENANCE:
    SINGLE_SOURCE_OF_TRUTH:
      PRINCIPLE::"Each piece of information has one authoritative source"
      IMPLEMENTATION::[
        "Game mechanics: OCTAVE specifications",
        "API contracts: YARD documentation", 
        "User procedures: Markdown guides",
        "Implementation details: Code comments"
      ]
      VALIDATION::REGULAR_CONSISTENCY_CHECKS
      
    CHANGE_PROPAGATION:
      PROCESS::[
        "Update authoritative source first",
        "Identify dependent documentation",
        "Update or link to new information",
        "Validate consistency across formats"
      ]
      AUTOMATION::WHERE_POSSIBLE_USE_GENERATION_TOOLS

---

QUALITY_STANDARDS:
  
  OCTAVE_QUALITY_CRITERIA:
    SEMANTIC_PRECISION::EXACT_TERMINOLOGY_USAGE
    STRUCTURAL_CONSISTENCY::FOLLOW_OCTAVE_SYNTAX_RULES
    RELATIONSHIP_CLARITY::EXPLICIT_CONNECTIONS_BETWEEN_CONCEPTS
    VALIDATION_COMPLETENESS::COMPREHENSIVE_TESTING_SCENARIOS
    
  MARKDOWN_QUALITY_CRITERIA:
    READABILITY::CLEAR_PROSE_FOR_HUMAN_CONSUMPTION
    STRUCTURE::LOGICAL_ORGANIZATION_WITH_HEADERS
    COMPLETENESS::COVER_ALL_NECESSARY_USER_SCENARIOS
    ACCESSIBILITY::APPROPRIATE_FOR_TARGET_AUDIENCE
    
  YARD_QUALITY_CRITERIA:
    COMPLETENESS::ALL_PUBLIC_METHODS_DOCUMENTED
    ACCURACY::PARAMETER_AND_RETURN_TYPES_CORRECT
    EXAMPLES::USAGE_EXAMPLES_FOR_COMPLEX_METHODS
    GENERATION::CLEAN_AUTOMATED_OUTPUT
    
  COMMENT_QUALITY_CRITERIA:
    RELEVANCE::EXPLAIN_WHY_NOT_WHAT
    CONCISENESS::BRIEF_BUT_SUFFICIENT
    CURRENCY::UPDATED_WITH_CODE_CHANGES
    CLARITY::UNDERSTANDABLE_TO_FUTURE_MAINTAINERS

---

TOOLING_AND_AUTOMATION:
  
  OCTAVE_TOOLING:
    VALIDATION::SEMANTIC_IMMUNE_SYSTEM
    GENERATION::CUSTOM_TOOLS_FOR_CROSS_REFERENCES
    MAINTENANCE::MANUAL_WITH_AUTOMATED_CHECKS
    
  MARKDOWN_TOOLING:
    VALIDATION::LINK_CHECKERS_AND_SPELL_CHECKERS
    GENERATION::AUTOMATED_TABLE_OF_CONTENTS
    MAINTENANCE::MANUAL_WITH_AUTOMATED_FORMATTING
    
  YARD_TOOLING:
    VALIDATION::AUTOMATED_DOCUMENTATION_COVERAGE_CHECKS
    GENERATION::AUTOMATED_API_DOCUMENTATION_GENERATION
    MAINTENANCE::INTEGRATED_WITH_CODE_DEVELOPMENT
    
  INTEGRATION_TOOLING:
    CROSS_REFERENCE_VALIDATION::AUTOMATED_LINK_CHECKING
    CONSISTENCY_CHECKING::CUSTOM_TOOLS_FOR_CONTENT_VALIDATION
    MAINTENANCE_ALERTS::NOTIFICATIONS_FOR_OUTDATED_CONTENT

---

SUCCESS_METRICS:
  
  DOCUMENTATION_EFFECTIVENESS:
    USER_SUCCESS_RATE::PERCENTAGE_OF_USERS_COMPLETING_TASKS
    DEVELOPER_ONBOARDING_TIME::TIME_TO_PRODUCTIVE_CONTRIBUTION
    AI_AGENT_HANDOFF_SUCCESS::SEAMLESS_CONTEXT_TRANSFER_RATE
    MAINTENANCE_BURDEN::TIME_SPENT_ON_DOCUMENTATION_UPDATES
    
  QUALITY_INDICATORS:
    DOCUMENTATION_COVERAGE::PERCENTAGE_OF_FEATURES_DOCUMENTED
    ACCURACY_RATE::PERCENTAGE_OF_DOCUMENTATION_MATCHING_IMPLEMENTATION
    CONSISTENCY_SCORE::ALIGNMENT_ACROSS_DIFFERENT_FORMATS
    USER_SATISFACTION::FEEDBACK_ON_DOCUMENTATION_USEFULNESS
    
  SYSTEM_HEALTH:
    FORMAT_UTILIZATION::APPROPRIATE_USE_OF_EACH_FORMAT
    CROSS_REFERENCE_INTEGRITY::WORKING_LINKS_BETWEEN_FORMATS
    UPDATE_FREQUENCY::REGULAR_MAINTENANCE_OF_ALL_FORMATS
    AUTOMATION_EFFECTIVENESS::SUCCESSFUL_AUTOMATED_PROCESSES

===END_HYBRID_SYSTEM===
