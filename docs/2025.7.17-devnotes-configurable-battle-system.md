# Configurable Battle System Documentation

## Overview

The Revue Starlight: ReLive battle system now supports configurable parameters through environment variables, allowing for flexible testing and gameplay adjustments without code changes.

## Turn Limit Configuration

### Historical Context

**Original Implementation:**
- The battle system had a **20-turn safety limit** in the playthrough script
- This was an arbitrary safety measure, not based on authentic game mechanics
- The official battle mechanics specification states: `TURN_LIMIT::NONE // Battle continues until victory`

**Current Implementation:**
- **Default**: 30 turns (user-specified preference)
- **Configurable**: Can be set via `BATTLE_MAX_TURNS` environment variable
- **Unlimited**: Set to 0 for no turn limit (matches authentic game behavior)

### Configuration Options

```bash
# Set 30-turn limit (default)
export BATTLE_MAX_TURNS=30

# Set unlimited battles (authentic game behavior)
export BATTLE_MAX_TURNS=0

# Set custom limit
export BATTLE_MAX_TURNS=50
```

## Pool Size Configuration

### Available Pool Size
Controls how many stage girls each player can select from:

```bash
# Default: 25 stage girls per player
export BATTLE_POOL_SIZE=25

# Smaller pool for faster games
export BATTLE_POOL_SIZE=15

# Larger pool for more variety
export BATTLE_POOL_SIZE=35
```

### Total Costume Pool
Controls the total number of high-tier costumes available:

```bash
# Default: 28 total elite costumes
export BATTLE_TOTAL_POOL=28

# All available high-tier costumes
export BATTLE_TOTAL_POOL=50
```

## Battle Features Configuration

### Randomized Pools
Enable/disable randomized pool selection:

```bash
# Enable randomized pools (default)
export BATTLE_RANDOMIZED_POOLS=true

# Disable for consistent testing
export BATTLE_RANDOMIZED_POOLS=false
```

### Verbose Logging
Control battle logging detail:

```bash
# Enable detailed logging
export BATTLE_VERBOSE=true

# Standard logging (default)
export BATTLE_VERBOSE=false
```

## Usage Examples

### Standard Configuration (Default)
```bash
ruby bin/authentic_battle_playthrough.rb
```
- 30-turn limit
- 25 stage girls per player from 28 total
- Randomized pools enabled

### Unlimited Battle (Authentic Game)
```bash
export BATTLE_MAX_TURNS=0
ruby bin/authentic_battle_playthrough.rb
```
- No turn limit (battles continue until victory)
- Matches authentic Revue Starlight: ReLive behavior

### Quick Testing Configuration
```bash
export BATTLE_MAX_TURNS=10
export BATTLE_POOL_SIZE=15
export BATTLE_TOTAL_POOL=20
ruby bin/authentic_battle_playthrough.rb
```
- Shorter battles for rapid testing
- Smaller pools for faster selection

### Tournament Configuration
```bash
export BATTLE_MAX_TURNS=50
export BATTLE_POOL_SIZE=35
export BATTLE_TOTAL_POOL=50
export BATTLE_VERBOSE=true
ruby bin/authentic_battle_playthrough.rb
```
- Extended battles with detailed logging
- Large pools for maximum variety

## Implementation Details

### BattleConfig Class
The configuration system is implemented through a `BattleConfig` class that reads environment variables with sensible defaults:

```ruby
class BattleConfig
  def self.max_turns
    ENV['BATTLE_MAX_TURNS']&.to_i || 30
  end
  
  def self.available_pool_size
    ENV['BATTLE_POOL_SIZE']&.to_i || 25
  end
  
  # ... other configuration methods
end
```

### Turn Limit Logic
```ruby
max_turns = BattleConfig.max_turns
if max_turns > 0 && @turn_count >= max_turns
  log_event("⏰ Turn limit reached (#{max_turns} turns)")
  break
end
```

### Winner Determination
When turn limit is reached:
1. **Primary**: Team with more surviving stage girls wins
2. **Tiebreaker**: Team with more total HP wins
3. **Perfect tie**: Declared as draw

## Future Enhancements

The configurable system can be extended to support:
- Custom attribute effectiveness multipliers
- Brilliance accumulation rates
- Critical hit chances
- Status effect durations
- Memoir/accessory effects

## File Naming Convention

RTF output files now use sequential numbering:
- `2025.7.17-devnotes-relivePickBanPlaythrough-01-FINAL-FIXED.rtf`
- `2025.7.17-devnotes-relivePickBanPlaythrough-02-CONFIGURABLE.rtf`
- `2025.7.17-devnotes-relivePickBanPlaythrough-03-[NEXT-FEATURE].rtf`

This ensures clear chronological ordering and feature identification.
