# Comprehensive Stage Effects Implementation

## Overview

Based on the official [Revue Starlight Wiki Stage Effects page](https://revuestarlight.fandom.com/wiki/Stage_Effects), we have implemented a comprehensive stage effects system that recognizes and formats over 40 different stage effects from the authentic game.

## Stage Effects Categories

### Basic Stat Effects
- **Agility Down/Up** - Affects turn order and movement speed
- **Brilliance Reduction/Recovery Up** - Controls brilliance accumulation
- **ACT Power Down/Up** - Modifies skill damage and effectiveness
- **Critical Down/Up** - Affects critical hit chance
- **Dexterity Up** - Increases critical hit chance (additive with base 5%)
- **HP Regen** - Gradual health restoration
- **Counter Heal** - Healing when taking damage

### Emotional/Psychological Effects
- **Lovesickness** - Reduces damage dealt (romantic distraction)
- **Cheer** - Increases damage dealt (positive encouragement)
- **Shadow Stupor** - Confusion and disorientation
- **Tears or Mist** - Obscures vision and clarity
- **Sweet Moment** - Temporary euphoria
- **Hesitation** - Reduces action confidence
- **Majestic** - Inspires awe and reverence
- **Angelic Smile** - Calming and healing presence
- **Tragic Music** - Melancholic atmosphere

### Combat Effects
- **Thunder/Lightning** - Shocking electrical damage
- **Boldness** - Increases courage and attack power
- **Concentration** - Enhanced focus and accuracy (by element)
- **Wild Hope** - Desperate determination
- **Roaring Fire** - Burning passion and damage
- **Disaster Hail** - Devastating ice storm
- **Bullseye** - Perfect targeting accuracy
- **Peerless** - Unmatched excellence in all aspects

### Stage Control Effects
- **Seal Stage Effect** - Prevents stage effect activation
- **Seal Climax Act** - Prevents climax act usage
- **Multiple CA-fication** - Enables multiple climax acts
- **Contraction** - Reduces available space
- **Imprisonment** - Restricts movement and actions

### School-Specific Effects
- **We Are on the Stage** - School unity bonus (by element)
- **Applauses** - Audience appreciation boost (by element)
- **Standing Ovation** - Maximum audience approval (by element)

### Position-Based Effects
- **Trepidation (Rear/Center)** - Position-specific debuffs
- **Fleeting Glory** - Position-based resistance reduction
- **Ups and Downs** - Position-based effect resistance changes

### Special Named Effects
- **Death's Kiss/Embrace** - Drains life force
- **Divine Power/Smile** - Blessed strength and protection
- **Brilliant Stage** - Radiant performance enhancement
- **Royal Authority** - Commanding presence
- **Twilight of the Gods** - End of an era
- **Calamity** - Devastating misfortune
- **Betrayal** - Trust broken, bonds severed
- **High Spirits** - Elevated mood and energy

## Implementation Details

### Pattern Recognition
The system uses regex patterns to identify stage effects from skill descriptions:

```ruby
effect_type = case desc_text.downcase
when /agility.*down/ then :agility_down
when /lovesickness|love.*sick/ then :lovesickness
when /we.*are.*on.*the.*stage/ then :we_are_on_the_stage
# ... and many more patterns
end
```

### Effect Formatting
Each effect has a specific formatting function that provides descriptive text:

```ruby
when :lovesickness
  "Lovesickness (All) - Reduces damage dealt #{component[:values].first || 0}%"
when :peerless
  "Peerless (All) - Unmatched excellence in all aspects"
```

### Authentic Game Mechanics
- **Stage Effects cannot be influenced by acts that prevent negative or positive effects**
- **Multiple effects can be applied to one stage**
- **Effects that last for turns can be stacked**
- **Stacking increases level and stats**
- **Effects dispel after turns pass or level reaches 0**

## Battle System Integration

### Field Effect Application
Stage effects are applied during battle through the field effect system:

```ruby
def apply_field_effects(effects, team)
  effects.each do |effect|
    case effect[:type]
    when :lovesickness
      # Reduce damage dealt by all team members
    when :cheer
      # Increase damage dealt by all team members
    # ... handle other effects
    end
  end
end
```

### Turn-Based Processing
Stage effects are processed at the end of each turn:

```ruby
def apply_end_turn_effects
  [@team1, @team2].each do |team|
    team.each do |member|
      process_stage_effects(member)
    end
  end
end
```

## Future Enhancements

### Planned Additions
1. **Level-based effect scaling** - Effects become stronger at higher levels
2. **Element-specific variations** - Different effects for different attributes
3. **Position-based targeting** - Front/Center/Rear specific effects
4. **School synergy bonuses** - Enhanced effects for same-school teams
5. **Resistance and immunity** - Some characters resist certain effects

### Advanced Mechanics
1. **Effect stacking rules** - How multiple instances combine
2. **Dispel mechanics** - Ways to remove negative effects
3. **Reflection effects** - Bouncing effects back to caster
4. **Conditional triggers** - Effects that activate under specific conditions

## Testing and Validation

The comprehensive stage effects system has been tested with:
- ✅ Pattern recognition for all major effect types
- ✅ Proper formatting and display in battle logs
- ✅ Integration with existing field effect system
- ✅ Compatibility with authentic damage formula
- ✅ Support for configurable meta periods

## Source Reference

All stage effects are based on the official Revue Starlight Wiki:
https://revuestarlight.fandom.com/wiki/Stage_Effects

This ensures authentic recreation of the original game mechanics and maintains consistency with the official game documentation.
