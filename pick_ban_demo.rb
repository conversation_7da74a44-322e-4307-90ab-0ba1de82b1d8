# frozen_string_literal: true

require_relative 'lib/card_battle/authentic_pick_ban_system'
require_relative 'lib/card_battle/authentic_pvp_battle'
require_relative 'lib/card_battle/battle_unit'

# Demo script for the Authentic Pick-Ban System
class PickBanDemo
  def self.run
    puts "🌟" * 30
    puts "AUTHENTIC PICK-BAN SYSTEM DEMO"
    puts "🌟" * 30
    puts
    puts "This demo implements the exact pick-ban rules:"
    puts "1. Each player selects 10 stage girls (duplicates allowed)"
    puts "2. Each player bans 2 opposing stage girls"
    puts "3. Each player forms a 5-member team from remaining 8"
    puts
    puts "Press Enter to start the demo..."
    gets

    # Create and run pick-ban system
    pick_ban = CardBattle::AuthenticPickBanSystem.new("Player Alpha", "Player Beta")
    final_teams = pick_ban.start_pick_ban_match

    # Convert teams to battle format and start battle
    puts "\n" + "⚔️" * 25
    puts "PROCEEDING TO BATTLE"
    puts "⚔️" * 25

    team1_battle_units = create_battle_units(final_teams[0], "Player Alpha")
    team2_battle_units = create_battle_units(final_teams[1], "Player Beta")

    puts "\n🎬 Starting authentic 5v5 battle with pick-ban teams..."
    
    begin
      battle = CardBattle::AuthenticPvPBattle.new(team1_battle_units, team2_battle_units)
      battle.start_battle
      puts "\n🏆 Pick-Ban battle completed successfully!"
    rescue => e
      puts "\n❌ Battle error: #{e.message}"
      puts "Pick-Ban system worked correctly, battle system needs integration"
    end
  end

  def self.run_automated_demo
    puts "🤖" * 30
    puts "AUTOMATED PICK-BAN DEMO"
    puts "🤖" * 30
    puts "Running automated demo with pre-selected choices..."
    puts

    # Create pick-ban system
    pick_ban = CardBattle::AuthenticPickBanSystem.new("Team Alpha", "Team Beta")
    
    # Simulate automated roster selection
    puts "📋 STEP 1: AUTOMATED ROSTER SELECTION"
    puts "-" * 40
    
    # Team Alpha: Mix of strong characters with some duplicates
    team_alpha_roster = ["Karen", "Maya", "Akira", "Hikari", "Claudine", "Tamao", "Ryoko", "Karen", "Maya", "Akira"]
    pick_ban.player1.roster = team_alpha_roster
    puts "Team Alpha roster: #{team_alpha_roster.join(', ')}"
    
    # Team Beta: Different mix with duplicates
    team_beta_roster = ["Hikari", "Futaba", "Ichie", "Aruru", "Stella", "Koharu", "Nana", "Hikari", "Futaba", "Ichie"]
    pick_ban.player2.roster = team_beta_roster
    puts "Team Beta roster: #{team_beta_roster.join(', ')}"
    
    # Simulate bans
    puts "\n🚫 STEP 2: AUTOMATED BAN PHASE"
    puts "-" * 40
    
    # Team Alpha bans Team Beta's strongest
    team_alpha_bans = ["Hikari", "Stella"]  # Ban duplicates and strong characters
    pick_ban.player1.bans = team_alpha_bans
    puts "Team Alpha bans: #{team_alpha_bans.join(', ')}"
    
    # Team Beta bans Team Alpha's strongest
    team_beta_bans = ["Karen", "Maya"]  # Ban the duplicates
    pick_ban.player2.bans = team_beta_bans
    puts "Team Beta bans: #{team_beta_bans.join(', ')}"
    
    # Apply bans
    pick_ban.player1.available_for_team = team_alpha_roster - team_beta_bans
    pick_ban.player2.available_for_team = team_beta_roster - team_alpha_bans
    
    puts "\nRemaining after bans:"
    puts "Team Alpha: #{pick_ban.player1.available_for_team.join(', ')} (#{pick_ban.player1.available_for_team.size})"
    puts "Team Beta: #{pick_ban.player2.available_for_team.join(', ')} (#{pick_ban.player2.available_for_team.size})"
    
    # Simulate team composition
    puts "\n✅ STEP 3: AUTOMATED TEAM COMPOSITION"
    puts "-" * 50
    
    # Select 5 from remaining 8
    team_alpha_final = pick_ban.player1.available_for_team.first(5)
    team_beta_final = pick_ban.player2.available_for_team.first(5)
    
    pick_ban.player1.final_team = team_alpha_final
    pick_ban.player2.final_team = team_beta_final
    
    puts "Team Alpha final: #{team_alpha_final.join(', ')}"
    puts "Team Beta final: #{team_beta_final.join(', ')}"
    
    puts "\n🏆 PICK-BAN COMPLETE!"
    puts "✅ Roster selection: 10 characters each (duplicates allowed)"
    puts "✅ Ban phase: 2 bans each"
    puts "✅ Team composition: 5 from remaining 8"
    puts
    puts "Ready for authentic 5v5 battle!"

    [team_alpha_final, team_beta_final]
  end

  private

  def self.create_battle_units(character_names, team_name)
    character_names.map.with_index do |char_name, index|
      # Create simplified stage girl for battle
      stage_girl = create_simple_stage_girl(char_name)
      stage_girl
    end
  end

  def self.create_simple_stage_girl(name)
    # Create a simplified stage girl object for battle
    stage_girl = Object.new
    stage_girl.define_singleton_method(:name) { name }
    stage_girl.define_singleton_method(:school) { get_character_school(name) }
    stage_girl.define_singleton_method(:selected_costume) do
      costume = Object.new
      costume.define_singleton_method(:id) { "authentic_costume" }
      costume.define_singleton_method(:name) { "Authentic Costume" }
      costume
    end
    stage_girl.define_singleton_method(:get_element) do
      case get_character_school(name)
      when :seisho then :star
      when :rinmeikan then :moon  
      when :frontier then :flower
      when :siegfeld then :snow
      when :seiran then :wind
      else :star
      end
    end
    stage_girl
  end

  def self.get_character_school(char_name)
    schools = {
      seisho: ["Karen", "Hikari", "Mahiru", "Claudine", "Maya", "Futaba", "Junna", "Nana", "Kaoruko"],
      rinmeikan: ["Tamao", "Ichie", "Fumi", "Rui", "Yuyuko"],
      frontier: ["Aruru", "Misora", "Lalafin", "Tsukasa", "Shizuha"],
      siegfeld: ["Akira", "Michiru", "Yachiyo", "Meifan", "Shiori", "Stella", "Shiro", "Ryoko", "Minku", "Kuina"],
      seiran: ["Koharu", "Suzu", "Hisame"]
    }
    
    schools.each do |school, chars|
      return school if chars.include?(char_name)
    end
    :unknown
  end
end

# Menu system
def main_menu
  loop do
    puts "\n" + "=" * 40
    puts "PICK-BAN SYSTEM DEMO"
    puts "=" * 40
    puts "1. Interactive Pick-Ban Demo"
    puts "2. Automated Pick-Ban Demo"
    puts "3. Rules Explanation"
    puts "4. Exit"
    puts
    print "Select option (1-4): "
    
    choice = gets.chomp
    
    case choice
    when "1"
      PickBanDemo.run
    when "2"
      PickBanDemo.run_automated_demo
    when "3"
      show_rules
    when "4"
      puts "Thank you for testing the Pick-Ban system! 🌟"
      break
    else
      puts "❌ Invalid choice, please try again."
    end
  end
end

def show_rules
  puts "\n📋 PICK-BAN RULES"
  puts "=" * 30
  puts
  puts "🎯 OBJECTIVE:"
  puts "Create the strongest 5-member team through strategic roster building and banning"
  puts
  puts "📋 STEP 1 - ROSTER SELECTION:"
  puts "• Each player selects 10 stage girls"
  puts "• Duplicates are allowed (e.g., 10 Hikaris, 10 Akiras)"
  puts "• No school, rarity, or element constraints"
  puts "• All 32 stage girls available to both players"
  puts
  puts "🚫 STEP 2 - BAN PHASE:"
  puts "• Each player sees opponent's 10-girl roster"
  puts "• Each player bans 2 opposing stage girls"
  puts "• Banned girls cannot be used in final team"
  puts "• Strategic banning to disrupt opponent's synergy"
  puts
  puts "✅ STEP 3 - TEAM COMPOSITION:"
  puts "• Each player has 8 remaining stage girls (10 - 2 bans)"
  puts "• Each player selects 5 girls for final battle team"
  puts "• 3 girls remain unused as reserves"
  puts
  puts "⚔️ BATTLE:"
  puts "• Authentic 5v5 Revue Starlight: Relive mechanics"
  puts "• Real card data and stats from karth.top database"
  puts "• AGI-based turn order, ACT point system, attribute effectiveness"
  puts
  puts "Press Enter to continue..."
  gets
end

# Run the demo
if __FILE__ == $0
  main_menu
end
