#!/usr/bin/env ruby
# frozen_string_literal: true

require_relative 'lib/card_battle/moveset_analyzer'
require_relative 'lib/card_battle/status_effects_system'

# Demonstration of Moveset Analysis and Game Mechanics
class MovesetAnalysisDemo
  def self.run
    puts "=" * 70
    puts "🎭 REVUE STARLIGHT: RELIVE - MOVESET ANALYSIS & GAME MECHANICS"
    puts "=" * 70
    puts "Analyzing card movesets, status effects, and battle mechanics"
    puts "Based on authentic card data from karth.top database"
    puts

    loop do
      puts "\n" + "=" * 50
      puts "MOVESET ANALYSIS MENU"
      puts "=" * 50
      puts "1. Analyze Specific Card Moveset"
      puts "2. Compare Multiple Cards"
      puts "3. Status Effects Demonstration"
      puts "4. Attribute Effectiveness Analysis"
      puts "5. School Synergy Analysis"
      puts "6. Position Strategy Analysis"
      puts "7. YouTube Research Discussion"
      puts "8. Exit"
      print "\nSelect option (1-8): "

      choice = gets.chomp
      case choice
      when "1"
        analyze_specific_card
      when "2"
        compare_multiple_cards
      when "3"
        demonstrate_status_effects
      when "4"
        analyze_attribute_effectiveness
      when "5"
        analyze_school_synergy
      when "6"
        analyze_position_strategy
      when "7"
        discuss_youtube_research
      when "8"
        puts "\nThank you for exploring the game mechanics! 🌟"
        break
      else
        puts "❌ Invalid choice, please try again."
      end
    end
  end

  def self.analyze_specific_card
    puts "\n🔍 SPECIFIC CARD ANALYSIS"
    puts "Enter a card ID to analyze (e.g., akira-4010002, ichie-2020022):"
    print "Card ID: "
    
    card_id = gets.chomp.downcase
    card_data = load_card_data(card_id)
    
    unless card_data
      puts "❌ Card not found. Available examples: akira-4010002, ichie-2020022, karen-1010031"
      return
    end

    analysis = CardBattle::MovesetAnalyzer.analyze_card(card_data)
    display_card_analysis(analysis)
  end

  def self.compare_multiple_cards
    puts "\n⚔️ MULTIPLE CARD COMPARISON"
    puts "Enter 2-3 card IDs to compare (separated by commas):"
    print "Card IDs: "
    
    input = gets.chomp
    card_ids = input.split(',').map(&:strip).map(&:downcase)
    
    if card_ids.size < 2
      puts "❌ Please enter at least 2 card IDs"
      return
    end

    analyses = []
    card_ids.each do |card_id|
      card_data = load_card_data(card_id)
      if card_data
        analyses << CardBattle::MovesetAnalyzer.analyze_card(card_data)
      else
        puts "⚠️ Card #{card_id} not found, skipping..."
      end
    end

    display_card_comparison(analyses)
  end

  def self.demonstrate_status_effects
    puts "\n🎯 STATUS EFFECTS DEMONSTRATION"
    puts "Showing how different status effects work in battle"
    puts

    # Create mock battle units
    akira = create_mock_unit("Akira", :snow, :rear)
    ichie = create_mock_unit("Ichie", :sun, :middle)

    puts "Battle Scenario: Akira vs Ichie"
    puts "Akira (Snow/Rear) vs Ichie (Sun/Middle)"
    puts

    # Demonstrate freeze effect
    puts "🧊 Akira uses Meteor Strahl (Climax ACT):"
    puts "- Deals 300 Snow damage"
    puts "- Applies Greater Freeze for 2 turns"
    
    CardBattle::StatusEffectsSystem.apply_status_effect(
      ichie, :freeze, level: :greater, duration: 2, value: 0, source: akira
    )

    puts "\n📊 Turn Effects Processing:"
    3.times do |turn|
      puts "\n--- Turn #{turn + 1} ---"
      puts "Ichie status: #{ichie.status_effects.map(&:to_s).join(', ')}"
      puts "Can Ichie act? #{ichie.can_act? ? 'Yes' : 'No (Frozen)'}"
      
      ichie.process_turn_effects
    end

    # Demonstrate resistance
    puts "\n🛡️ Resistance Demonstration:"
    CardBattle::StatusEffectsSystem.apply_status_effect(
      akira, :freeze_resistance, level: :greater, duration: 3, value: 100
    )
    
    puts "Akira now has Greater Freeze Resistance (100)"
    success = CardBattle::StatusEffectsSystem.apply_status_effect(
      akira, :freeze, level: :normal, duration: 2, value: 0, source: ichie
    )
    puts "Normal Freeze attempt: #{success ? 'Success' : 'Resisted'}"
  end

  def self.analyze_attribute_effectiveness
    puts "\n🌟 ATTRIBUTE EFFECTIVENESS ANALYSIS"
    puts "Revue Starlight attribute wheel and damage calculations"
    puts

    attributes = [:flower, :wind, :snow, :moon, :space, :cloud, :star, :sun]
    
    puts "Attribute Effectiveness Wheel:"
    puts "Flower > Wind > Snow > Flower (Triangle 1)"
    puts "Moon > Space > Cloud > Moon (Triangle 2)"  
    puts "Star and Sun are neutral"
    puts

    puts "Damage Multipliers:"
    puts "Strong Against: 1.2x damage"
    puts "Weak Against: 0.8x damage"
    puts "Neutral: 1.0x damage"
    puts

    puts "Example Calculations:"
    base_damage = 1000
    
    puts "Base damage: #{base_damage}"
    puts "Flower vs Wind: #{(base_damage * 1.2).round} (Strong)"
    puts "Flower vs Snow: #{(base_damage * 0.8).round} (Weak)"
    puts "Flower vs Moon: #{base_damage} (Neutral)"
  end

  def self.analyze_school_synergy
    puts "\n🏫 SCHOOL SYNERGY ANALYSIS"
    puts "How different schools work together in teams"
    puts

    schools = {
      seisho: "Performance-focused, spotlight generation",
      rinmeikan: "Traditional, spirit-based buffs",
      frontier: "Speed and combo chains",
      siegfeld: "Precision and control",
      seiran: "Balance and adaptability"
    }

    schools.each do |school, description|
      puts "#{school.to_s.capitalize}: #{description}"
    end

    puts "\nSynergy Examples:"
    puts "• 3+ Seisho cards: +15% performance power"
    puts "• 2+ Frontier cards: Combo chain bonuses"
    puts "• Mixed schools: Diverse tactical options"
    puts "• Same school: Specialized synergy effects"
  end

  def self.analyze_position_strategy
    puts "\n📍 POSITION STRATEGY ANALYSIS"
    puts "Front, Middle, Rear positioning and targeting"
    puts

    positions = {
      front: "High HP, draws attacks, protects team",
      middle: "Balanced, supports front and rear",
      rear: "High damage, vulnerable to rear attacks"
    }

    positions.each do |pos, description|
      puts "#{pos.to_s.capitalize}: #{description}"
    end

    puts "\nTargeting Patterns:"
    puts "• Most ACTs target front enemies first"
    puts "• Some ACTs specifically target rear (high damage dealers)"
    puts "• AoE ACTs hit multiple positions"
    puts "• Position affects turn order (AGI-based)"
  end

  def self.discuss_youtube_research
    puts "\n📺 YOUTUBE RESEARCH DISCUSSION"
    puts "How gameplay videos can help understand missing mechanics"
    puts

    puts "🎯 What YouTube videos could help with:"
    puts "• Status effect duration and stacking behavior"
    puts "• Exact damage calculation formulas"
    puts "• Turn order determination with tied AGI"
    puts "• Brilliance generation rates and climax timing"
    puts "• Stage effect interactions and priorities"
    puts "• Memoir and accessory stat bonuses"
    puts "• School synergy activation conditions"
    puts

    puts "🔍 Specific mechanics to research:"
    puts "• Do status effects stack or override?"
    puts "• How does 'Greater' level affect resistance piercing?"
    puts "• What's the exact formula for critical hit chance?"
    puts "• How do position changes affect targeting?"
    puts "• Do some effects have hidden duration extensions?"
    puts

    puts "📋 Recommended video types:"
    puts "• High-level PvP matches with detailed UI"
    puts "• Status effect showcases and testing"
    puts "• Damage calculation demonstrations"
    puts "• Team composition guides with explanations"
    puts "• Frame-by-frame analysis of combat mechanics"
    puts

    puts "💡 Analysis approach:"
    puts "1. Record specific scenarios with known inputs"
    puts "2. Measure exact damage numbers and durations"
    puts "3. Test edge cases and interactions"
    puts "4. Compare with card data to verify formulas"
    puts "5. Document any discrepancies for further research"
  end

  private

  def self.load_card_data(card_id)
    card_file = "lib/card_battle/cards/#{card_id}.rb"
    return nil unless File.exist?(card_file)

    begin
      load card_file
      constant_name = card_id.upcase.gsub('-', '_')
      Object.const_get(constant_name)
    rescue => e
      puts "⚠️ Error loading #{card_id}: #{e.message}"
      nil
    end
  end

  def self.display_card_analysis(analysis)
    puts "\n📊 CARD ANALYSIS RESULTS"
    puts "=" * 50

    info = analysis[:basic_info]
    puts "Name: #{info[:name]}"
    puts "Card ID: #{info[:card_id]}"
    puts "Rarity: #{info[:rarity]} ⭐"
    puts "Attribute: #{info[:attribute]}"
    puts "Position: #{info[:position]}"
    puts "Cost: #{info[:cost]}"

    puts "\n📈 Stats:"
    stats = analysis[:stats]
    puts "HP: #{stats[:hp]}, ATK: #{stats[:atk]}, AGI: #{stats[:agi]}, DEX: #{stats[:dex]}"

    puts "\n⚔️ ACT Abilities:"
    analysis[:acts].each do |act_key, act_data|
      puts "#{act_key.to_s.upcase}: #{act_data[:name]} (Cost: #{act_data[:cost]})"
      act_data[:effects].each do |effect|
        puts "  - #{effect[:description]}"
        puts "    Power: #{effect[:power_values].join(', ')}" if effect[:power_values].any?
        puts "    Duration: #{effect[:duration]} turns" if effect[:duration]
      end
    end

    puts "\n🎯 Moveset Summary:"
    movesets = analysis[:movesets]
    movesets.each do |category, moves|
      next if moves.empty?
      puts "#{category.to_s.capitalize}: #{moves.size} abilities"
    end

    puts "\n🔗 Synergies:"
    synergies = analysis[:synergies]
    puts "School: #{synergies[:school_synergy][:school]}"
    puts "Attribute: #{synergies[:attribute_synergy][:primary_attribute]}"
    puts "Position: #{synergies[:position_synergy][:position]}"
  end

  def self.display_card_comparison(analyses)
    puts "\n⚖️ CARD COMPARISON"
    puts "=" * 60

    analyses.each_with_index do |analysis, index|
      info = analysis[:basic_info]
      puts "#{index + 1}. #{info[:name]} (#{info[:card_id]})"
      puts "   Attribute: #{info[:attribute]}, Position: #{info[:position]}, Cost: #{info[:cost]}"
      
      movesets = analysis[:movesets]
      offensive = movesets[:offensive].size
      control = movesets[:control].size
      support = movesets[:support].size
      puts "   Movesets: #{offensive} offensive, #{control} control, #{support} support"
    end

    puts "\n📊 Comparison Summary:"
    puts "Use this data to build synergistic teams and counter strategies!"
  end

  def self.create_mock_unit(name, attribute, position)
    unit = Object.new
    unit.define_singleton_method(:name) { name }
    unit.define_singleton_method(:attribute) { attribute }
    unit.define_singleton_method(:position) { position }
    unit.define_singleton_method(:max_hp) { 30000 }
    unit.define_singleton_method(:status_effects) { @status_effects ||= [] }
    unit.define_singleton_method(:take_damage) { |dmg| puts "#{name} takes #{dmg} damage!" }
    
    # Include status effect methods
    unit.extend(CardBattle::StatusEffectMethods)
    unit
  end
end

# Run the demo if this file is executed directly
if __FILE__ == $0
  MovesetAnalysisDemo.run
end
